(()=>{var e={};e.id=126,e.ids=[126],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1527:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>b,routeModule:()=>g,serverHooks:()=>k,workAsyncStorage:()=>v,workUnitAsyncStorage:()=>w});var t={};s.r(t),s.d(t,{POST:()=>i});var o=s(6559),a=s(8088),n=s(7719),c=s(2190),d=s(5069);async function i(e){try{let{event:r}=await e.json(),s=e.headers.get("X-Webhook-Signature");return process.env.WEBHOOK_SECRET&&s&&console.log("Webhook signature received:",s),await u(r),c.NextResponse.json({success:!0,message:"Webhook processed successfully"})}catch(e){return console.error("Webhook processing failed:",e),c.NextResponse.json({success:!1,error:"Webhook processing failed"},{status:500})}}async function u(e){let{type:r,data:s}=e;switch(r){case"lead.created":case"lead.updated":await p(s);break;case"student.created":case"student.updated":await l(s);break;case"course.created":case"course.updated":await h(s);break;default:console.log(`Unhandled webhook event type: ${r}`)}}async function p(e){try{await d.db.auditLog.create({data:{userId:"system",action:"LEAD_ACTIVITY",resourceType:"LEAD",resourceId:e.id,newValues:{leadData:e,source:"staff_service_webhook"},ipAddress:"127.0.0.1",userAgent:"System Webhook"}}),console.log("Lead event processed and logged:",e.id)}catch(e){throw console.error("Failed to handle lead event:",e),e}}async function l(e){try{await d.db.auditLog.create({data:{userId:"system",action:"STUDENT_ACTIVITY",resourceType:"STUDENT",resourceId:e.id,newValues:{studentData:e,source:"service_webhook"},ipAddress:"127.0.0.1",userAgent:"System Webhook"}}),console.log("Student event processed and logged:",e.id)}catch(e){throw console.error("Failed to handle student event:",e),e}}async function h(e){try{await d.db.auditLog.create({data:{userId:"system",action:"COURSE_ACTIVITY",resourceType:"COURSE",resourceId:e.id,newValues:{courseData:e,source:"staff_service_webhook"},ipAddress:"127.0.0.1",userAgent:"System Webhook"}}),console.log("Course event processed and logged:",e.id)}catch(e){throw console.error("Failed to handle course event:",e),e}}let g=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/webhooks/receive/route",pathname:"/api/webhooks/receive",filename:"route",bundlePath:"app/api/webhooks/receive/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-admin-service\\src\\app\\api\\webhooks\\receive\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:v,workUnitAsyncStorage:w,serverHooks:k}=g;function b(){return(0,n.patchFetch)({workAsyncStorage:v,workUnitAsyncStorage:w})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5069:(e,r,s)=>{"use strict";s.d(r,{db:()=>a,z:()=>o});var t=s(6330);let o=globalThis.prisma??new t.PrismaClient({log:["query","error","warn"]}),a=o},6330:e=>{"use strict";e.exports=require("@prisma/client")},6487:()=>{},8335:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[447,199],()=>s(1527));module.exports=t})();