(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[889],{285:(e,s,t)=>{"use strict";t.d(s,{$:()=>d});var a=t(5155);t(2115);var r=t(4624),n=t(2085),i=t(9434);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:s,variant:t,size:n,asChild:d=!1,...c}=e,o=d?r.DX:"button";return(0,a.jsx)(o,{"data-slot":"button",className:(0,i.cn)(l({variant:t,size:n,className:s})),...c})}},2523:(e,s,t)=>{"use strict";t.d(s,{p:()=>i});var a=t(5155),r=t(2115),n=t(9434);let i=r.forwardRef((e,s)=>{let{className:t,type:r,...i}=e;return(0,a.jsx)("input",{type:r,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...i})});i.displayName="Input"},2657:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},5868:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},6695:(e,s,t)=>{"use strict";t.d(s,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>n,aR:()=>i});var a=t(5155);t(2115);var r=t(9434);function n(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",s),...t})}function i(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...t})}function l(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",s),...t})}function d(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",s),...t})}function c(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",s),...t})}},9009:(e,s,t)=>{Promise.resolve().then(t.bind(t,9428))},9428:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>z});var a=t(5155),r=t(2115),n=t(5493),i=t(9946);let l=(0,i.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),d=(0,i.A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]),c=(0,i.A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);var o=t(5868);let x=(0,i.A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]),u=(0,i.A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);var m=t(2657),h=t(285),p=t(2523),g=t(6695),v=t(4624),f=t(2085),b=t(9434);let j=(0,f.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function y(e){let{className:s,variant:t,asChild:r=!1,...n}=e,i=r?v.DX:"span";return(0,a.jsx)(i,{"data-slot":"badge",className:(0,b.cn)(j({variant:t}),s),...n})}function N(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,a.jsx)("table",{"data-slot":"table",className:(0,b.cn)("w-full caption-bottom text-sm",s),...t})})}function w(e){let{className:s,...t}=e;return(0,a.jsx)("thead",{"data-slot":"table-header",className:(0,b.cn)("[&_tr]:border-b",s),...t})}function k(e){let{className:s,...t}=e;return(0,a.jsx)("tbody",{"data-slot":"table-body",className:(0,b.cn)("[&_tr:last-child]:border-0",s),...t})}function S(e){let{className:s,...t}=e;return(0,a.jsx)("tr",{"data-slot":"table-row",className:(0,b.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",s),...t})}function A(e){let{className:s,...t}=e;return(0,a.jsx)("th",{"data-slot":"table-head",className:(0,b.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",s),...t})}function _(e){let{className:s,...t}=e;return(0,a.jsx)("td",{"data-slot":"table-cell",className:(0,b.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",s),...t})}function z(){let{data:e}=(0,n.wV)(),[s,t]=(0,r.useState)([]),[i,v]=(0,r.useState)(!0),[f,b]=(0,r.useState)(""),[j,z]=(0,r.useState)(""),[D,M]=(0,r.useState)(1),[P,R]=(0,r.useState)(1),Z=(0,r.useCallback)(async()=>{try{v(!0);let e=new URLSearchParams({page:D.toString(),limit:"10"});j&&e.append("status",j);let s=await fetch("/api/payments?".concat(e)),a=await s.json();a.success&&(t(a.data.payments),R(a.data.pagination.pages))}catch(e){console.error("Error fetching payments:",e)}finally{v(!1)}},[D,j]);(0,r.useEffect)(()=>{Z()},[Z]);let $=e=>{switch(e){case"verified":return(0,a.jsxs)(y,{className:"bg-green-100 text-green-800",children:[(0,a.jsx)(l,{className:"w-3 h-3 mr-1"}),"Verified"]});case"disputed":return(0,a.jsxs)(y,{className:"bg-red-100 text-red-800",children:[(0,a.jsx)(d,{className:"w-3 h-3 mr-1"}),"Disputed"]});case"recorded":return(0,a.jsxs)(y,{className:"bg-yellow-100 text-yellow-800",children:[(0,a.jsx)(c,{className:"w-3 h-3 mr-1"}),"Recorded"]});default:return(0,a.jsx)(y,{variant:"secondary",children:e})}},B=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e),C=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"});return e?(0,a.jsxs)("div",{className:"p-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Payment Management"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Record and track payment information"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,a.jsx)(g.Zp,{children:(0,a.jsx)(g.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(o.A,{className:"h-8 w-8 text-green-600"}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Revenue"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"$0"})]})]})})}),(0,a.jsx)(g.Zp,{children:(0,a.jsx)(g.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(l,{className:"h-8 w-8 text-green-600"}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Verified"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"0"})]})]})})}),(0,a.jsx)(g.Zp,{children:(0,a.jsx)(g.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(c,{className:"h-8 w-8 text-yellow-600"}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Pending"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"0"})]})]})})}),(0,a.jsx)(g.Zp,{children:(0,a.jsx)(g.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(d,{className:"h-8 w-8 text-red-600"}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Disputed"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"0"})]})]})})})]}),(0,a.jsxs)(g.Zp,{className:"mb-6",children:[(0,a.jsxs)(g.aR,{children:[(0,a.jsx)(g.ZB,{children:"Payment Records"}),(0,a.jsx)(g.BT,{children:"Manage and track all payment records"})]}),(0,a.jsxs)(g.Wu,{children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 mb-6",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(x,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,a.jsx)(p.p,{placeholder:"Search payments...",value:f,onChange:e=>b(e.target.value),className:"pl-10"})]})}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)("select",{value:j,onChange:e=>z(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"",children:"All Status"}),(0,a.jsx)("option",{value:"recorded",children:"Recorded"}),(0,a.jsx)("option",{value:"verified",children:"Verified"}),(0,a.jsx)("option",{value:"disputed",children:"Disputed"})]}),(0,a.jsxs)(h.$,{children:[(0,a.jsx)(u,{className:"h-4 w-4 mr-2"}),"New Payment"]})]})]}),(0,a.jsx)("div",{className:"border rounded-lg",children:(0,a.jsxs)(N,{children:[(0,a.jsx)(w,{children:(0,a.jsxs)(S,{children:[(0,a.jsx)(A,{children:"Student ID"}),(0,a.jsx)(A,{children:"Amount"}),(0,a.jsx)(A,{children:"Date"}),(0,a.jsx)(A,{children:"Method"}),(0,a.jsx)(A,{children:"Status"}),(0,a.jsx)(A,{children:"Recorded By"}),(0,a.jsx)(A,{children:"Actions"})]})}),(0,a.jsx)(k,{children:i?(0,a.jsx)(S,{children:(0,a.jsxs)(_,{colSpan:7,className:"text-center py-8",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,a.jsx)("p",{className:"mt-2 text-gray-600",children:"Loading payments..."})]})}):0===s.length?(0,a.jsx)(S,{children:(0,a.jsx)(_,{colSpan:7,className:"text-center py-8",children:(0,a.jsx)("p",{className:"text-gray-600",children:"No payment records found"})})}):s.map(e=>(0,a.jsxs)(S,{children:[(0,a.jsx)(_,{className:"font-medium",children:e.studentId}),(0,a.jsx)(_,{children:B(e.amount)}),(0,a.jsx)(_,{children:C(e.paymentDate)}),(0,a.jsx)(_,{className:"capitalize",children:e.paymentMethod.replace("_"," ")}),(0,a.jsx)(_,{children:$(e.status)}),(0,a.jsxs)(_,{children:[e.recordedByUser.firstName," ",e.recordedByUser.lastName]}),(0,a.jsx)(_,{children:(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(h.$,{variant:"outline",size:"sm",children:(0,a.jsx)(m.A,{className:"h-4 w-4"})}),"recorded"===e.status&&(0,a.jsx)(h.$,{variant:"outline",size:"sm",children:"Verify"})]})})]},e.id))})]})}),P>1&&(0,a.jsx)("div",{className:"flex justify-center mt-6",children:(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(h.$,{variant:"outline",onClick:()=>M(e=>Math.max(e-1,1)),disabled:1===D,children:"Previous"}),(0,a.jsxs)("span",{className:"px-4 py-2 text-sm text-gray-600",children:["Page ",D," of ",P]}),(0,a.jsx)(h.$,{variant:"outline",onClick:()=>M(e=>Math.min(e+1,P)),disabled:D===P,children:"Next"})]})})]})]})]}):(0,a.jsx)("div",{children:"Please sign in to access this page."})}},9434:(e,s,t)=>{"use strict";t.d(s,{cn:()=>n});var a=t(2596),r=t(9688);function n(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,r.QP)((0,a.$)(s))}}},e=>{var s=s=>e(e.s=s);e.O(0,[493,497,441,684,358],()=>s(9009)),_N_E=e.O()}]);