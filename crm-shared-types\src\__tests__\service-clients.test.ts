import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { 
  AdminServiceClient, 
  StaffServiceClient, 
  StudentServiceClient,
  ServiceClientFactory,
  ServiceCommunicationError,
  EnhancedServiceClient
} from '../lib/service-clients';

// Mock fetch globally
global.fetch = jest.fn() as jest.MockedFunction<typeof fetch>;

describe('Service Clients', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('EnhancedServiceClient', () => {
    let client: EnhancedServiceClient;

    beforeEach(() => {
      client = new EnhancedServiceClient(
        'http://localhost:3001',
        'test-service',
        'test-api-key'
      );
    });

    it('should retry failed requests', async () => {
      const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;
      
      // First two calls fail, third succeeds
      mockFetch
        .mockRejectedValueOnce(new Error('Network error'))
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true, data: 'test' }),
        } as Response);

      const result = await client.get('/test');
      
      expect(mockFetch).toHaveBeenCalledTimes(3);
      expect(result).toEqual({ success: true, data: 'test' });
    });

    it('should throw ServiceCommunicationError after max retries', async () => {
      const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;
      
      // All calls fail
      mockFetch.mockRejectedValue(new Error('Network error'));

      await expect(client.get('/test')).rejects.toThrow(ServiceCommunicationError);
      expect(mockFetch).toHaveBeenCalledTimes(3); // Default max retries
    });

    it('should include proper authentication headers', async () => {
      const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;
      
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true }),
      } as Response);

      await client.get('/test');

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:3001/test',
        expect.objectContaining({
          method: 'GET',
          headers: expect.objectContaining({
            'X-Service-Name': 'test-service',
            'X-API-Key': 'test-api-key',
          }),
        })
      );
    });
  });

  describe('AdminServiceClient', () => {
    let client: AdminServiceClient;

    beforeEach(() => {
      client = new AdminServiceClient(
        'http://localhost:3001',
        'test-service',
        'test-api-key'
      );
    });

    it('should get payments with pagination', async () => {
      const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;
      const mockPayments = [
        { id: '1', amount: 100, status: 'verified' },
        { id: '2', amount: 200, status: 'pending' },
      ];

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true, data: mockPayments }),
      } as Response);

      const result = await client.getPayments(1, 10);

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:3001/api/payments?page=1&limit=10',
        expect.any(Object)
      );
      expect(result).toEqual({ success: true, data: mockPayments });
    });

    it('should create payment record', async () => {
      const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;
      const paymentData = {
        studentId: 'student-1',
        amount: 500,
        paymentMethod: 'card' as const,
        description: 'Course fee',
        paymentDate: new Date(),
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true, id: 'payment-1' }),
      } as Response);

      const result = await client.createPayment(paymentData);

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:3001/api/payments',
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(paymentData),
        })
      );
      expect(result).toEqual({ success: true, id: 'payment-1' });
    });
  });

  describe('StaffServiceClient', () => {
    let client: StaffServiceClient;

    beforeEach(() => {
      client = new StaffServiceClient(
        'http://localhost:3002',
        'test-service',
        'test-api-key'
      );
    });

    it('should get leads with filters', async () => {
      const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;
      const mockLeads = [
        { id: '1', name: 'John Doe', status: 'new' },
        { id: '2', name: 'Jane Smith', status: 'contacted' },
      ];

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true, data: mockLeads }),
      } as Response);

      const result = await client.getLeads('new');

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:3002/api/leads?page=1&limit=20&status=new',
        expect.any(Object)
      );
      expect(result).toEqual({ success: true, data: mockLeads });
    });

    it('should create course', async () => {
      const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;
      const courseData = {
        title: 'Advanced English',
        description: 'Advanced level English course',
        duration: 40,
        price: 300,
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true, id: 'course-1' }),
      } as Response);

      const result = await client.createCourse(courseData);

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:3002/api/courses',
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(courseData),
        })
      );
      expect(result).toEqual({ success: true, id: 'course-1' });
    });
  });

  describe('StudentServiceClient', () => {
    let client: StudentServiceClient;

    beforeEach(() => {
      client = new StudentServiceClient(
        'http://localhost:3003',
        'test-service',
        'test-api-key'
      );
    });

    it('should get student submissions', async () => {
      const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;
      const mockSubmissions = [
        { id: '1', title: 'Essay Writing', dueDate: '2024-01-15' },
        { id: '2', title: 'Grammar Exercise', dueDate: '2024-01-20' },
      ];

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true, data: mockSubmissions }),
      } as Response);

      const result = await client.getSubmissions('student-1');

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:3003/api/submissions?studentId=student-1',
        expect.any(Object)
      );
      expect(result).toEqual({ success: true, data: mockSubmissions });
    });

    it('should update student progress', async () => {
      const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;
      const progressData = {
        assignmentId: 'assignment-1',
        progressPercentage: 75,
        feedback: 'Good progress',
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true }),
      } as Response);

      const result = await client.updateProgress(progressData);

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:3003/api/progress',
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(progressData),
        })
      );
      expect(result).toEqual({ success: true });
    });
  });

  describe('ServiceClientFactory', () => {
    let factory: ServiceClientFactory;

    beforeEach(() => {
      factory = new ServiceClientFactory({
        adminServiceUrl: 'http://localhost:3001',
        staffServiceUrl: 'http://localhost:3002',
        studentServiceUrl: 'http://localhost:3003',
        serviceName: 'test-service',
        apiKey: 'test-api-key',
      });
    });

    it('should create admin client', () => {
      const client = factory.getAdminClient();
      expect(client).toBeInstanceOf(AdminServiceClient);
    });

    it('should create staff client', () => {
      const client = factory.getStaffClient();
      expect(client).toBeInstanceOf(StaffServiceClient);
    });

    it('should create student client', () => {
      const client = factory.getStudentClient();
      expect(client).toBeInstanceOf(StudentServiceClient);
    });

    it('should reuse client instances', () => {
      const client1 = factory.getAdminClient();
      const client2 = factory.getAdminClient();
      expect(client1).toBe(client2);
    });
  });
});
