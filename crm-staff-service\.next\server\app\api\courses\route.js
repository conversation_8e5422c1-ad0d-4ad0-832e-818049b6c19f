/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/courses/route";
exports.ids = ["app/api/courses/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcourses%2Froute&page=%2Fapi%2Fcourses%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcourses%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CInnovative%20Centre%5Ccrm-staff-service%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CInnovative%20Centre%5Ccrm-staff-service&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcourses%2Froute&page=%2Fapi%2Fcourses%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcourses%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CInnovative%20Centre%5Ccrm-staff-service%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CInnovative%20Centre%5Ccrm-staff-service&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Windows_11_Documents_augment_projects_Innovative_Centre_crm_staff_service_src_app_api_courses_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/courses/route.ts */ \"(rsc)/./src/app/api/courses/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/courses/route\",\n        pathname: \"/api/courses\",\n        filename: \"route\",\n        bundlePath: \"app/api/courses/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Innovative Centre\\\\crm-staff-service\\\\src\\\\app\\\\api\\\\courses\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Windows_11_Documents_augment_projects_Innovative_Centre_crm_staff_service_src_app_api_courses_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcourses%2Froute&page=%2Fapi%2Fcourses%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcourses%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CInnovative%20Centre%5Ccrm-staff-service%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CInnovative%20Centre%5Ccrm-staff-service&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/courses/route.ts":
/*!**************************************!*\
  !*** ./src/app/api/courses/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n// Courses Management API - Staff Service\n\n\n\n\n\nconst createCourseSchema = zod__WEBPACK_IMPORTED_MODULE_4__.z.object({\n    courseCode: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().min(1, \"Course code is required\"),\n    title: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().min(1, \"Title is required\"),\n    description: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().optional(),\n    level: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().min(1, \"Level is required\"),\n    durationWeeks: zod__WEBPACK_IMPORTED_MODULE_4__.z.number().positive(\"Duration must be positive\"),\n    maxStudents: zod__WEBPACK_IMPORTED_MODULE_4__.z.number().positive(\"Max students must be positive\"),\n    price: zod__WEBPACK_IMPORTED_MODULE_4__.z.number().positive(\"Price must be positive\")\n});\nconst updateCourseSchema = zod__WEBPACK_IMPORTED_MODULE_4__.z.object({\n    courseCode: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().min(1).optional(),\n    title: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().min(1).optional(),\n    description: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().optional(),\n    level: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().min(1).optional(),\n    durationWeeks: zod__WEBPACK_IMPORTED_MODULE_4__.z.number().positive().optional(),\n    maxStudents: zod__WEBPACK_IMPORTED_MODULE_4__.z.number().positive().optional(),\n    price: zod__WEBPACK_IMPORTED_MODULE_4__.z.number().positive().optional(),\n    status: zod__WEBPACK_IMPORTED_MODULE_4__.z.nativeEnum(_prisma_client__WEBPACK_IMPORTED_MODULE_3__.CourseStatus).optional()\n});\n// GET /api/courses - List courses\nasync function GET(request) {\n    try {\n        const session = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.auth)();\n        if (!session?.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get(\"page\") || \"1\");\n        const limit = parseInt(searchParams.get(\"limit\") || \"10\");\n        const status = searchParams.get(\"status\");\n        const level = searchParams.get(\"level\");\n        const skip = (page - 1) * limit;\n        const where = {};\n        if (status) where.status = status;\n        if (level) where.level = level;\n        const [courses, total] = await Promise.all([\n            _lib_db__WEBPACK_IMPORTED_MODULE_2__.prisma.course.findMany({\n                where,\n                skip,\n                take: limit,\n                include: {\n                    createdByUser: {\n                        select: {\n                            firstName: true,\n                            lastName: true,\n                            email: true\n                        }\n                    },\n                    groups: {\n                        select: {\n                            id: true,\n                            groupName: true,\n                            currentStudents: true,\n                            maxStudents: true,\n                            status: true\n                        }\n                    },\n                    _count: {\n                        select: {\n                            enrollments: true,\n                            groups: true\n                        }\n                    }\n                },\n                orderBy: {\n                    createdAt: \"desc\"\n                }\n            }),\n            _lib_db__WEBPACK_IMPORTED_MODULE_2__.prisma.course.count({\n                where\n            })\n        ]);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                courses,\n                pagination: {\n                    page,\n                    limit,\n                    total,\n                    pages: Math.ceil(total / limit)\n                }\n            }\n        });\n    } catch (error) {\n        console.error(\"Error fetching courses:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/courses - Create new course\nasync function POST(request) {\n    try {\n        const session = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.auth)();\n        if (!session?.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        // Check if user has permission to create courses\n        if (![\n            \"manager\",\n            \"teacher\"\n        ].includes(session.user.role)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Forbidden\"\n            }, {\n                status: 403\n            });\n        }\n        const body = await request.json();\n        const validatedData = createCourseSchema.parse(body);\n        // Check if course code already exists\n        const existingCourse = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.prisma.course.findUnique({\n            where: {\n                courseCode: validatedData.courseCode\n            }\n        });\n        if (existingCourse) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Course code already exists\"\n            }, {\n                status: 400\n            });\n        }\n        const course = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.prisma.course.create({\n            data: {\n                ...validatedData,\n                createdBy: session.user.id\n            },\n            include: {\n                createdByUser: {\n                    select: {\n                        firstName: true,\n                        lastName: true,\n                        email: true\n                    }\n                }\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: course\n        });\n    } catch (error) {\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_4__.z.ZodError) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Validation error\",\n                details: error.errors\n            }, {\n                status: 400\n            });\n        }\n        console.error(\"Error creating course:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/courses/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @auth/prisma-adapter */ \"(rsc)/../node_modules/@auth/prisma-adapter/index.js\");\n/* harmony import */ var _db__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./db */ \"(rsc)/./src/lib/db.ts\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n// NextAuth.js v5 Configuration for Staff Service\n\n\n\n\n\nconst { handlers: { GET, POST }, auth, signIn, signOut } = (0,next_auth__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    adapter: (0,_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_2__.PrismaAdapter)(_db__WEBPACK_IMPORTED_MODULE_3__.prisma),\n    session: {\n        strategy: \"jwt\",\n        maxAge: 8 * 60 * 60\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        error: \"/auth/error\"\n    },\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                try {\n                    // Find staff user\n                    const staffUser = await _db__WEBPACK_IMPORTED_MODULE_3__.prisma.staffUser.findUnique({\n                        where: {\n                            email: credentials.email\n                        },\n                        include: {\n                            teacher: true\n                        }\n                    });\n                    if (!staffUser || !staffUser.isActive) {\n                        return null;\n                    }\n                    // Verify password\n                    const isValidPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_4__[\"default\"].compare(credentials.password, staffUser.passwordHash);\n                    if (!isValidPassword) {\n                        return null;\n                    }\n                    return {\n                        id: staffUser.id,\n                        email: staffUser.email,\n                        name: `${staffUser.firstName} ${staffUser.lastName}`,\n                        role: staffUser.role,\n                        employeeId: staffUser.employeeId,\n                        department: staffUser.department,\n                        isTeacher: !!staffUser.teacher,\n                        teacherId: staffUser.teacher?.id\n                    };\n                } catch (error) {\n                    console.error(\"Authentication error:\", error);\n                    return null;\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n                token.id = user.id;\n                token.employeeId = user.employeeId;\n                token.department = user.department;\n                token.isTeacher = user.isTeacher;\n                token.teacherId = user.teacherId;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.id;\n                session.user.role = token.role;\n                session.user.employeeId = token.employeeId;\n                session.user.department = token.department;\n                session.user.isTeacher = token.isTeacher;\n                session.user.teacherId = token.teacherId;\n            }\n            return session;\n        },\n        async authorized ({ auth, request: { nextUrl } }) {\n            const isLoggedIn = !!auth?.user;\n            const isOnDashboard = nextUrl.pathname.startsWith(\"/dashboard\");\n            const isOnAuth = nextUrl.pathname.startsWith(\"/auth\");\n            if (isOnDashboard) {\n                if (isLoggedIn) return true;\n                return false; // Redirect unauthenticated users to login page\n            } else if (isOnAuth) {\n                if (isLoggedIn) return Response.redirect(new URL(\"/dashboard\", nextUrl));\n                return true;\n            }\n            return true;\n        }\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log: [\n        'query',\n        'error',\n        'warn'\n    ]\n});\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4QztBQUU5QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxDQUFDO0lBQy9ESSxLQUFLO1FBQUM7UUFBUztRQUFTO0tBQU87QUFDakMsR0FBRztBQUVILElBQUlDLElBQXFDLEVBQUVKLGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxXaW5kb3dzIDExXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXElubm92YXRpdmUgQ2VudHJlXFxjcm0tc3RhZmYtc2VydmljZVxcc3JjXFxsaWJcXGRiLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50JztcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XG59O1xuXG5leHBvcnQgY29uc3QgcHJpc21hID0gZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/PyBuZXcgUHJpc21hQ2xpZW50KHtcbiAgbG9nOiBbJ3F1ZXJ5JywgJ2Vycm9yJywgJ3dhcm4nXSxcbn0pO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYTtcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwibG9nIiwicHJvY2VzcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@auth","vendor-chunks/next-auth","vendor-chunks/oauth4webapi","vendor-chunks/jose","vendor-chunks/bcryptjs","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/@panva","vendor-chunks/prisma","vendor-chunks/@prisma","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcourses%2Froute&page=%2Fapi%2Fcourses%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcourses%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CInnovative%20Centre%5Ccrm-staff-service%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CInnovative%20Centre%5Ccrm-staff-service&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();