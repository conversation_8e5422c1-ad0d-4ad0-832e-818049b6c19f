# Student Service Environment Variables

# Database Connection
STUDENT_DATABASE_URL="postgresql://postgres:password@localhost:5432/crm_student_db"

# Authentication (NextAuth.js v5)
NEXTAUTH_SECRET="student-service-super-secret-key-for-development-only-2024"
NEXTAUTH_URL="http://localhost:3002"

# Service URLs for Inter-Service Communication
ADMIN_SERVICE_URL="http://localhost:3000"
STAFF_SERVICE_URL="http://localhost:3001"
SHARED_TYPES_URL="http://localhost:3003"

# Service Authentication
SERVICE_API_KEY="development-service-api-key"
SERVICE_NAME="crm-student-service"

# Session Settings
SESSION_TIMEOUT=7200
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900
