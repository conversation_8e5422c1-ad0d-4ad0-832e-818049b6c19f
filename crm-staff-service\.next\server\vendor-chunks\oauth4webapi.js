"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/oauth4webapi";
exports.ids = ["vendor-chunks/oauth4webapi"];
exports.modules = {

/***/ "(rsc)/./node_modules/oauth4webapi/build/index.js":
/*!**************************************************!*\
  !*** ./node_modules/oauth4webapi/build/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AUTHORIZATION_RESPONSE_ERROR: () => (/* binding */ AUTHORIZATION_RESPONSE_ERROR),\n/* harmony export */   AuthorizationResponseError: () => (/* binding */ AuthorizationResponseError),\n/* harmony export */   ClientSecretBasic: () => (/* binding */ ClientSecretBasic),\n/* harmony export */   ClientSecretJwt: () => (/* binding */ ClientSecretJwt),\n/* harmony export */   ClientSecretPost: () => (/* binding */ ClientSecretPost),\n/* harmony export */   DPoP: () => (/* binding */ DPoP),\n/* harmony export */   HTTP_REQUEST_FORBIDDEN: () => (/* binding */ HTTP_REQUEST_FORBIDDEN),\n/* harmony export */   INVALID_REQUEST: () => (/* binding */ INVALID_REQUEST),\n/* harmony export */   INVALID_RESPONSE: () => (/* binding */ INVALID_RESPONSE),\n/* harmony export */   INVALID_SERVER_METADATA: () => (/* binding */ INVALID_SERVER_METADATA),\n/* harmony export */   JSON_ATTRIBUTE_COMPARISON: () => (/* binding */ JSON_ATTRIBUTE_COMPARISON),\n/* harmony export */   JWT_CLAIM_COMPARISON: () => (/* binding */ JWT_CLAIM_COMPARISON),\n/* harmony export */   JWT_TIMESTAMP_CHECK: () => (/* binding */ JWT_TIMESTAMP_CHECK),\n/* harmony export */   JWT_USERINFO_EXPECTED: () => (/* binding */ JWT_USERINFO_EXPECTED),\n/* harmony export */   KEY_SELECTION: () => (/* binding */ KEY_SELECTION),\n/* harmony export */   MISSING_SERVER_METADATA: () => (/* binding */ MISSING_SERVER_METADATA),\n/* harmony export */   None: () => (/* binding */ None),\n/* harmony export */   OperationProcessingError: () => (/* binding */ OperationProcessingError),\n/* harmony export */   PARSE_ERROR: () => (/* binding */ PARSE_ERROR),\n/* harmony export */   PrivateKeyJwt: () => (/* binding */ PrivateKeyJwt),\n/* harmony export */   REQUEST_PROTOCOL_FORBIDDEN: () => (/* binding */ REQUEST_PROTOCOL_FORBIDDEN),\n/* harmony export */   RESPONSE_BODY_ERROR: () => (/* binding */ RESPONSE_BODY_ERROR),\n/* harmony export */   RESPONSE_IS_NOT_CONFORM: () => (/* binding */ RESPONSE_IS_NOT_CONFORM),\n/* harmony export */   RESPONSE_IS_NOT_JSON: () => (/* binding */ RESPONSE_IS_NOT_JSON),\n/* harmony export */   ResponseBodyError: () => (/* binding */ ResponseBodyError),\n/* harmony export */   TlsClientAuth: () => (/* binding */ TlsClientAuth),\n/* harmony export */   UNSUPPORTED_OPERATION: () => (/* binding */ UNSUPPORTED_OPERATION),\n/* harmony export */   UnsupportedOperationError: () => (/* binding */ UnsupportedOperationError),\n/* harmony export */   WWWAuthenticateChallengeError: () => (/* binding */ WWWAuthenticateChallengeError),\n/* harmony export */   WWW_AUTHENTICATE_CHALLENGE: () => (/* binding */ WWW_AUTHENTICATE_CHALLENGE),\n/* harmony export */   _expectedIssuer: () => (/* binding */ _expectedIssuer),\n/* harmony export */   _nodiscoverycheck: () => (/* binding */ _nodiscoverycheck),\n/* harmony export */   _nopkce: () => (/* binding */ _nopkce),\n/* harmony export */   allowInsecureRequests: () => (/* binding */ allowInsecureRequests),\n/* harmony export */   authorizationCodeGrantRequest: () => (/* binding */ authorizationCodeGrantRequest),\n/* harmony export */   backchannelAuthenticationGrantRequest: () => (/* binding */ backchannelAuthenticationGrantRequest),\n/* harmony export */   backchannelAuthenticationRequest: () => (/* binding */ backchannelAuthenticationRequest),\n/* harmony export */   calculatePKCECodeChallenge: () => (/* binding */ calculatePKCECodeChallenge),\n/* harmony export */   checkProtocol: () => (/* binding */ checkProtocol),\n/* harmony export */   clientCredentialsGrantRequest: () => (/* binding */ clientCredentialsGrantRequest),\n/* harmony export */   clockSkew: () => (/* binding */ clockSkew),\n/* harmony export */   clockTolerance: () => (/* binding */ clockTolerance),\n/* harmony export */   customFetch: () => (/* binding */ customFetch),\n/* harmony export */   deviceAuthorizationRequest: () => (/* binding */ deviceAuthorizationRequest),\n/* harmony export */   deviceCodeGrantRequest: () => (/* binding */ deviceCodeGrantRequest),\n/* harmony export */   discoveryRequest: () => (/* binding */ discoveryRequest),\n/* harmony export */   dynamicClientRegistrationRequest: () => (/* binding */ dynamicClientRegistrationRequest),\n/* harmony export */   expectNoNonce: () => (/* binding */ expectNoNonce),\n/* harmony export */   expectNoState: () => (/* binding */ expectNoState),\n/* harmony export */   formPostResponse: () => (/* binding */ formPostResponse),\n/* harmony export */   generateKeyPair: () => (/* binding */ generateKeyPair),\n/* harmony export */   generateRandomCodeVerifier: () => (/* binding */ generateRandomCodeVerifier),\n/* harmony export */   generateRandomNonce: () => (/* binding */ generateRandomNonce),\n/* harmony export */   generateRandomState: () => (/* binding */ generateRandomState),\n/* harmony export */   genericTokenEndpointRequest: () => (/* binding */ genericTokenEndpointRequest),\n/* harmony export */   getContentType: () => (/* binding */ getContentType),\n/* harmony export */   getValidatedIdTokenClaims: () => (/* binding */ getValidatedIdTokenClaims),\n/* harmony export */   introspectionRequest: () => (/* binding */ introspectionRequest),\n/* harmony export */   isDPoPNonceError: () => (/* binding */ isDPoPNonceError),\n/* harmony export */   issueRequestObject: () => (/* binding */ issueRequestObject),\n/* harmony export */   jweDecrypt: () => (/* binding */ jweDecrypt),\n/* harmony export */   jwksCache: () => (/* binding */ jwksCache),\n/* harmony export */   modifyAssertion: () => (/* binding */ modifyAssertion),\n/* harmony export */   nopkce: () => (/* binding */ nopkce),\n/* harmony export */   processAuthorizationCodeResponse: () => (/* binding */ processAuthorizationCodeResponse),\n/* harmony export */   processBackchannelAuthenticationGrantResponse: () => (/* binding */ processBackchannelAuthenticationGrantResponse),\n/* harmony export */   processBackchannelAuthenticationResponse: () => (/* binding */ processBackchannelAuthenticationResponse),\n/* harmony export */   processClientCredentialsResponse: () => (/* binding */ processClientCredentialsResponse),\n/* harmony export */   processDeviceAuthorizationResponse: () => (/* binding */ processDeviceAuthorizationResponse),\n/* harmony export */   processDeviceCodeResponse: () => (/* binding */ processDeviceCodeResponse),\n/* harmony export */   processDiscoveryResponse: () => (/* binding */ processDiscoveryResponse),\n/* harmony export */   processDynamicClientRegistrationResponse: () => (/* binding */ processDynamicClientRegistrationResponse),\n/* harmony export */   processGenericTokenEndpointResponse: () => (/* binding */ processGenericTokenEndpointResponse),\n/* harmony export */   processIntrospectionResponse: () => (/* binding */ processIntrospectionResponse),\n/* harmony export */   processPushedAuthorizationResponse: () => (/* binding */ processPushedAuthorizationResponse),\n/* harmony export */   processRefreshTokenResponse: () => (/* binding */ processRefreshTokenResponse),\n/* harmony export */   processResourceDiscoveryResponse: () => (/* binding */ processResourceDiscoveryResponse),\n/* harmony export */   processRevocationResponse: () => (/* binding */ processRevocationResponse),\n/* harmony export */   processUserInfoResponse: () => (/* binding */ processUserInfoResponse),\n/* harmony export */   protectedResourceRequest: () => (/* binding */ protectedResourceRequest),\n/* harmony export */   pushedAuthorizationRequest: () => (/* binding */ pushedAuthorizationRequest),\n/* harmony export */   refreshTokenGrantRequest: () => (/* binding */ refreshTokenGrantRequest),\n/* harmony export */   resolveEndpoint: () => (/* binding */ resolveEndpoint),\n/* harmony export */   resourceDiscoveryRequest: () => (/* binding */ resourceDiscoveryRequest),\n/* harmony export */   revocationRequest: () => (/* binding */ revocationRequest),\n/* harmony export */   skipAuthTimeCheck: () => (/* binding */ skipAuthTimeCheck),\n/* harmony export */   skipStateCheck: () => (/* binding */ skipStateCheck),\n/* harmony export */   skipSubjectCheck: () => (/* binding */ skipSubjectCheck),\n/* harmony export */   userInfoRequest: () => (/* binding */ userInfoRequest),\n/* harmony export */   validateApplicationLevelSignature: () => (/* binding */ validateApplicationLevelSignature),\n/* harmony export */   validateAuthResponse: () => (/* binding */ validateAuthResponse),\n/* harmony export */   validateCodeIdTokenResponse: () => (/* binding */ validateCodeIdTokenResponse),\n/* harmony export */   validateDetachedSignatureResponse: () => (/* binding */ validateDetachedSignatureResponse),\n/* harmony export */   validateJwtAccessToken: () => (/* binding */ validateJwtAccessToken),\n/* harmony export */   validateJwtAuthResponse: () => (/* binding */ validateJwtAuthResponse)\n/* harmony export */ });\nlet USER_AGENT;\nif (typeof navigator === 'undefined' || !navigator.userAgent?.startsWith?.('Mozilla/5.0 ')) {\n    const NAME = 'oauth4webapi';\n    const VERSION = 'v3.5.3';\n    USER_AGENT = `${NAME}/${VERSION}`;\n}\nfunction looseInstanceOf(input, expected) {\n    if (input == null) {\n        return false;\n    }\n    try {\n        return (input instanceof expected ||\n            Object.getPrototypeOf(input)[Symbol.toStringTag] === expected.prototype[Symbol.toStringTag]);\n    }\n    catch {\n        return false;\n    }\n}\nconst ERR_INVALID_ARG_VALUE = 'ERR_INVALID_ARG_VALUE';\nconst ERR_INVALID_ARG_TYPE = 'ERR_INVALID_ARG_TYPE';\nfunction CodedTypeError(message, code, cause) {\n    const err = new TypeError(message, { cause });\n    Object.assign(err, { code });\n    return err;\n}\nconst allowInsecureRequests = Symbol();\nconst clockSkew = Symbol();\nconst clockTolerance = Symbol();\nconst customFetch = Symbol();\nconst modifyAssertion = Symbol();\nconst jweDecrypt = Symbol();\nconst jwksCache = Symbol();\nconst encoder = new TextEncoder();\nconst decoder = new TextDecoder();\nfunction buf(input) {\n    if (typeof input === 'string') {\n        return encoder.encode(input);\n    }\n    return decoder.decode(input);\n}\nlet encodeBase64Url;\nif (Uint8Array.prototype.toBase64) {\n    encodeBase64Url = (input) => {\n        if (input instanceof ArrayBuffer) {\n            input = new Uint8Array(input);\n        }\n        return input.toBase64({ alphabet: 'base64url', omitPadding: true });\n    };\n}\nelse {\n    const CHUNK_SIZE = 0x8000;\n    encodeBase64Url = (input) => {\n        if (input instanceof ArrayBuffer) {\n            input = new Uint8Array(input);\n        }\n        const arr = [];\n        for (let i = 0; i < input.byteLength; i += CHUNK_SIZE) {\n            arr.push(String.fromCharCode.apply(null, input.subarray(i, i + CHUNK_SIZE)));\n        }\n        return btoa(arr.join('')).replace(/=/g, '').replace(/\\+/g, '-').replace(/\\//g, '_');\n    };\n}\nlet decodeBase64Url;\nif (Uint8Array.fromBase64) {\n    decodeBase64Url = (input) => {\n        try {\n            return Uint8Array.fromBase64(input, { alphabet: 'base64url' });\n        }\n        catch (cause) {\n            throw CodedTypeError('The input to be decoded is not correctly encoded.', ERR_INVALID_ARG_VALUE, cause);\n        }\n    };\n}\nelse {\n    decodeBase64Url = (input) => {\n        try {\n            const binary = atob(input.replace(/-/g, '+').replace(/_/g, '/').replace(/\\s/g, ''));\n            const bytes = new Uint8Array(binary.length);\n            for (let i = 0; i < binary.length; i++) {\n                bytes[i] = binary.charCodeAt(i);\n            }\n            return bytes;\n        }\n        catch (cause) {\n            throw CodedTypeError('The input to be decoded is not correctly encoded.', ERR_INVALID_ARG_VALUE, cause);\n        }\n    };\n}\nfunction b64u(input) {\n    if (typeof input === 'string') {\n        return decodeBase64Url(input);\n    }\n    return encodeBase64Url(input);\n}\nclass UnsupportedOperationError extends Error {\n    code;\n    constructor(message, options) {\n        super(message, options);\n        this.name = this.constructor.name;\n        this.code = UNSUPPORTED_OPERATION;\n        Error.captureStackTrace?.(this, this.constructor);\n    }\n}\nclass OperationProcessingError extends Error {\n    code;\n    constructor(message, options) {\n        super(message, options);\n        this.name = this.constructor.name;\n        if (options?.code) {\n            this.code = options?.code;\n        }\n        Error.captureStackTrace?.(this, this.constructor);\n    }\n}\nfunction OPE(message, code, cause) {\n    return new OperationProcessingError(message, { code, cause });\n}\nfunction assertCryptoKey(key, it) {\n    if (!(key instanceof CryptoKey)) {\n        throw CodedTypeError(`${it} must be a CryptoKey`, ERR_INVALID_ARG_TYPE);\n    }\n}\nfunction assertPrivateKey(key, it) {\n    assertCryptoKey(key, it);\n    if (key.type !== 'private') {\n        throw CodedTypeError(`${it} must be a private CryptoKey`, ERR_INVALID_ARG_VALUE);\n    }\n}\nfunction assertPublicKey(key, it) {\n    assertCryptoKey(key, it);\n    if (key.type !== 'public') {\n        throw CodedTypeError(`${it} must be a public CryptoKey`, ERR_INVALID_ARG_VALUE);\n    }\n}\nfunction normalizeTyp(value) {\n    return value.toLowerCase().replace(/^application\\//, '');\n}\nfunction isJsonObject(input) {\n    if (input === null || typeof input !== 'object' || Array.isArray(input)) {\n        return false;\n    }\n    return true;\n}\nfunction prepareHeaders(input) {\n    if (looseInstanceOf(input, Headers)) {\n        input = Object.fromEntries(input.entries());\n    }\n    const headers = new Headers(input ?? {});\n    if (USER_AGENT && !headers.has('user-agent')) {\n        headers.set('user-agent', USER_AGENT);\n    }\n    if (headers.has('authorization')) {\n        throw CodedTypeError('\"options.headers\" must not include the \"authorization\" header name', ERR_INVALID_ARG_VALUE);\n    }\n    return headers;\n}\nfunction signal(value) {\n    if (typeof value === 'function') {\n        value = value();\n    }\n    if (!(value instanceof AbortSignal)) {\n        throw CodedTypeError('\"options.signal\" must return or be an instance of AbortSignal', ERR_INVALID_ARG_TYPE);\n    }\n    return value;\n}\nfunction replaceDoubleSlash(pathname) {\n    if (pathname.includes('//')) {\n        return pathname.replace('//', '/');\n    }\n    return pathname;\n}\nfunction prependWellKnown(url, wellKnown) {\n    if (url.pathname === '/') {\n        url.pathname = wellKnown;\n    }\n    else {\n        url.pathname = replaceDoubleSlash(`${wellKnown}/${url.pathname}`);\n    }\n    return url;\n}\nfunction appendWellKnown(url, wellKnown) {\n    url.pathname = replaceDoubleSlash(`${url.pathname}/${wellKnown}`);\n    return url;\n}\nasync function performDiscovery(input, urlName, transform, options) {\n    if (!(input instanceof URL)) {\n        throw CodedTypeError(`\"${urlName}\" must be an instance of URL`, ERR_INVALID_ARG_TYPE);\n    }\n    checkProtocol(input, options?.[allowInsecureRequests] !== true);\n    const url = transform(new URL(input.href));\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    return (options?.[customFetch] || fetch)(url.href, {\n        body: undefined,\n        headers: Object.fromEntries(headers.entries()),\n        method: 'GET',\n        redirect: 'manual',\n        signal: options?.signal ? signal(options.signal) : undefined,\n    });\n}\nasync function discoveryRequest(issuerIdentifier, options) {\n    return performDiscovery(issuerIdentifier, 'issuerIdentifier', (url) => {\n        switch (options?.algorithm) {\n            case undefined:\n            case 'oidc':\n                appendWellKnown(url, '.well-known/openid-configuration');\n                break;\n            case 'oauth2':\n                prependWellKnown(url, '.well-known/oauth-authorization-server');\n                break;\n            default:\n                throw CodedTypeError('\"options.algorithm\" must be \"oidc\" (default), or \"oauth2\"', ERR_INVALID_ARG_VALUE);\n        }\n        return url;\n    }, options);\n}\nfunction assertNumber(input, allow0, it, code, cause) {\n    try {\n        if (typeof input !== 'number' || !Number.isFinite(input)) {\n            throw CodedTypeError(`${it} must be a number`, ERR_INVALID_ARG_TYPE, cause);\n        }\n        if (input > 0)\n            return;\n        if (allow0) {\n            if (input !== 0) {\n                throw CodedTypeError(`${it} must be a non-negative number`, ERR_INVALID_ARG_VALUE, cause);\n            }\n            return;\n        }\n        throw CodedTypeError(`${it} must be a positive number`, ERR_INVALID_ARG_VALUE, cause);\n    }\n    catch (err) {\n        if (code) {\n            throw OPE(err.message, code, cause);\n        }\n        throw err;\n    }\n}\nfunction assertString(input, it, code, cause) {\n    try {\n        if (typeof input !== 'string') {\n            throw CodedTypeError(`${it} must be a string`, ERR_INVALID_ARG_TYPE, cause);\n        }\n        if (input.length === 0) {\n            throw CodedTypeError(`${it} must not be empty`, ERR_INVALID_ARG_VALUE, cause);\n        }\n    }\n    catch (err) {\n        if (code) {\n            throw OPE(err.message, code, cause);\n        }\n        throw err;\n    }\n}\nasync function processDiscoveryResponse(expectedIssuerIdentifier, response) {\n    const expected = expectedIssuerIdentifier;\n    if (!(expected instanceof URL) && expected !== _nodiscoverycheck) {\n        throw CodedTypeError('\"expectedIssuerIdentifier\" must be an instance of URL', ERR_INVALID_ARG_TYPE);\n    }\n    if (!looseInstanceOf(response, Response)) {\n        throw CodedTypeError('\"response\" must be an instance of Response', ERR_INVALID_ARG_TYPE);\n    }\n    if (response.status !== 200) {\n        throw OPE('\"response\" is not a conform Authorization Server Metadata response (unexpected HTTP status code)', RESPONSE_IS_NOT_CONFORM, response);\n    }\n    assertReadableResponse(response);\n    const json = await getResponseJsonBody(response);\n    assertString(json.issuer, '\"response\" body \"issuer\" property', INVALID_RESPONSE, { body: json });\n    if (expected !== _nodiscoverycheck && new URL(json.issuer).href !== expected.href) {\n        throw OPE('\"response\" body \"issuer\" property does not match the expected value', JSON_ATTRIBUTE_COMPARISON, { expected: expected.href, body: json, attribute: 'issuer' });\n    }\n    return json;\n}\nfunction assertApplicationJson(response) {\n    assertContentType(response, 'application/json');\n}\nfunction notJson(response, ...types) {\n    let msg = '\"response\" content-type must be ';\n    if (types.length > 2) {\n        const last = types.pop();\n        msg += `${types.join(', ')}, or ${last}`;\n    }\n    else if (types.length === 2) {\n        msg += `${types[0]} or ${types[1]}`;\n    }\n    else {\n        msg += types[0];\n    }\n    return OPE(msg, RESPONSE_IS_NOT_JSON, response);\n}\nfunction assertContentTypes(response, ...types) {\n    if (!types.includes(getContentType(response))) {\n        throw notJson(response, ...types);\n    }\n}\nfunction assertContentType(response, contentType) {\n    if (getContentType(response) !== contentType) {\n        throw notJson(response, contentType);\n    }\n}\nfunction randomBytes() {\n    return b64u(crypto.getRandomValues(new Uint8Array(32)));\n}\nfunction generateRandomCodeVerifier() {\n    return randomBytes();\n}\nfunction generateRandomState() {\n    return randomBytes();\n}\nfunction generateRandomNonce() {\n    return randomBytes();\n}\nasync function calculatePKCECodeChallenge(codeVerifier) {\n    assertString(codeVerifier, 'codeVerifier');\n    return b64u(await crypto.subtle.digest('SHA-256', buf(codeVerifier)));\n}\nfunction getKeyAndKid(input) {\n    if (input instanceof CryptoKey) {\n        return { key: input };\n    }\n    if (!(input?.key instanceof CryptoKey)) {\n        return {};\n    }\n    if (input.kid !== undefined) {\n        assertString(input.kid, '\"kid\"');\n    }\n    return {\n        key: input.key,\n        kid: input.kid,\n    };\n}\nfunction psAlg(key) {\n    switch (key.algorithm.hash.name) {\n        case 'SHA-256':\n            return 'PS256';\n        case 'SHA-384':\n            return 'PS384';\n        case 'SHA-512':\n            return 'PS512';\n        default:\n            throw new UnsupportedOperationError('unsupported RsaHashedKeyAlgorithm hash name', {\n                cause: key,\n            });\n    }\n}\nfunction rsAlg(key) {\n    switch (key.algorithm.hash.name) {\n        case 'SHA-256':\n            return 'RS256';\n        case 'SHA-384':\n            return 'RS384';\n        case 'SHA-512':\n            return 'RS512';\n        default:\n            throw new UnsupportedOperationError('unsupported RsaHashedKeyAlgorithm hash name', {\n                cause: key,\n            });\n    }\n}\nfunction esAlg(key) {\n    switch (key.algorithm.namedCurve) {\n        case 'P-256':\n            return 'ES256';\n        case 'P-384':\n            return 'ES384';\n        case 'P-521':\n            return 'ES512';\n        default:\n            throw new UnsupportedOperationError('unsupported EcKeyAlgorithm namedCurve', { cause: key });\n    }\n}\nfunction keyToJws(key) {\n    switch (key.algorithm.name) {\n        case 'RSA-PSS':\n            return psAlg(key);\n        case 'RSASSA-PKCS1-v1_5':\n            return rsAlg(key);\n        case 'ECDSA':\n            return esAlg(key);\n        case 'Ed25519':\n        case 'EdDSA':\n            return 'Ed25519';\n        default:\n            throw new UnsupportedOperationError('unsupported CryptoKey algorithm name', { cause: key });\n    }\n}\nfunction getClockSkew(client) {\n    const skew = client?.[clockSkew];\n    return typeof skew === 'number' && Number.isFinite(skew) ? skew : 0;\n}\nfunction getClockTolerance(client) {\n    const tolerance = client?.[clockTolerance];\n    return typeof tolerance === 'number' && Number.isFinite(tolerance) && Math.sign(tolerance) !== -1\n        ? tolerance\n        : 30;\n}\nfunction epochTime() {\n    return Math.floor(Date.now() / 1000);\n}\nfunction assertAs(as) {\n    if (typeof as !== 'object' || as === null) {\n        throw CodedTypeError('\"as\" must be an object', ERR_INVALID_ARG_TYPE);\n    }\n    assertString(as.issuer, '\"as.issuer\"');\n}\nfunction assertClient(client) {\n    if (typeof client !== 'object' || client === null) {\n        throw CodedTypeError('\"client\" must be an object', ERR_INVALID_ARG_TYPE);\n    }\n    assertString(client.client_id, '\"client.client_id\"');\n}\nfunction formUrlEncode(token) {\n    return encodeURIComponent(token).replace(/(?:[-_.!~*'()]|%20)/g, (substring) => {\n        switch (substring) {\n            case '-':\n            case '_':\n            case '.':\n            case '!':\n            case '~':\n            case '*':\n            case \"'\":\n            case '(':\n            case ')':\n                return `%${substring.charCodeAt(0).toString(16).toUpperCase()}`;\n            case '%20':\n                return '+';\n            default:\n                throw new Error();\n        }\n    });\n}\nfunction ClientSecretPost(clientSecret) {\n    assertString(clientSecret, '\"clientSecret\"');\n    return (_as, client, body, _headers) => {\n        body.set('client_id', client.client_id);\n        body.set('client_secret', clientSecret);\n    };\n}\nfunction ClientSecretBasic(clientSecret) {\n    assertString(clientSecret, '\"clientSecret\"');\n    return (_as, client, _body, headers) => {\n        const username = formUrlEncode(client.client_id);\n        const password = formUrlEncode(clientSecret);\n        const credentials = btoa(`${username}:${password}`);\n        headers.set('authorization', `Basic ${credentials}`);\n    };\n}\nfunction clientAssertionPayload(as, client) {\n    const now = epochTime() + getClockSkew(client);\n    return {\n        jti: randomBytes(),\n        aud: as.issuer,\n        exp: now + 60,\n        iat: now,\n        nbf: now,\n        iss: client.client_id,\n        sub: client.client_id,\n    };\n}\nfunction PrivateKeyJwt(clientPrivateKey, options) {\n    const { key, kid } = getKeyAndKid(clientPrivateKey);\n    assertPrivateKey(key, '\"clientPrivateKey.key\"');\n    return async (as, client, body, _headers) => {\n        const header = { alg: keyToJws(key), kid };\n        const payload = clientAssertionPayload(as, client);\n        options?.[modifyAssertion]?.(header, payload);\n        body.set('client_id', client.client_id);\n        body.set('client_assertion_type', 'urn:ietf:params:oauth:client-assertion-type:jwt-bearer');\n        body.set('client_assertion', await signJwt(header, payload, key));\n    };\n}\nfunction ClientSecretJwt(clientSecret, options) {\n    assertString(clientSecret, '\"clientSecret\"');\n    const modify = options?.[modifyAssertion];\n    let key;\n    return async (as, client, body, _headers) => {\n        key ||= await crypto.subtle.importKey('raw', buf(clientSecret), { hash: 'SHA-256', name: 'HMAC' }, false, ['sign']);\n        const header = { alg: 'HS256' };\n        const payload = clientAssertionPayload(as, client);\n        modify?.(header, payload);\n        const data = `${b64u(buf(JSON.stringify(header)))}.${b64u(buf(JSON.stringify(payload)))}`;\n        const hmac = await crypto.subtle.sign(key.algorithm, key, buf(data));\n        body.set('client_id', client.client_id);\n        body.set('client_assertion_type', 'urn:ietf:params:oauth:client-assertion-type:jwt-bearer');\n        body.set('client_assertion', `${data}.${b64u(new Uint8Array(hmac))}`);\n    };\n}\nfunction None() {\n    return (_as, client, body, _headers) => {\n        body.set('client_id', client.client_id);\n    };\n}\nfunction TlsClientAuth() {\n    return None();\n}\nasync function signJwt(header, payload, key) {\n    if (!key.usages.includes('sign')) {\n        throw CodedTypeError('CryptoKey instances used for signing assertions must include \"sign\" in their \"usages\"', ERR_INVALID_ARG_VALUE);\n    }\n    const input = `${b64u(buf(JSON.stringify(header)))}.${b64u(buf(JSON.stringify(payload)))}`;\n    const signature = b64u(await crypto.subtle.sign(keyToSubtle(key), key, buf(input)));\n    return `${input}.${signature}`;\n}\nasync function issueRequestObject(as, client, parameters, privateKey, options) {\n    assertAs(as);\n    assertClient(client);\n    parameters = new URLSearchParams(parameters);\n    const { key, kid } = getKeyAndKid(privateKey);\n    assertPrivateKey(key, '\"privateKey.key\"');\n    parameters.set('client_id', client.client_id);\n    const now = epochTime() + getClockSkew(client);\n    const claims = {\n        ...Object.fromEntries(parameters.entries()),\n        jti: randomBytes(),\n        aud: as.issuer,\n        exp: now + 60,\n        iat: now,\n        nbf: now,\n        iss: client.client_id,\n    };\n    let resource;\n    if (parameters.has('resource') &&\n        (resource = parameters.getAll('resource')) &&\n        resource.length > 1) {\n        claims.resource = resource;\n    }\n    {\n        let value = parameters.get('max_age');\n        if (value !== null) {\n            claims.max_age = parseInt(value, 10);\n            assertNumber(claims.max_age, true, '\"max_age\" parameter');\n        }\n    }\n    {\n        let value = parameters.get('claims');\n        if (value !== null) {\n            try {\n                claims.claims = JSON.parse(value);\n            }\n            catch (cause) {\n                throw OPE('failed to parse the \"claims\" parameter as JSON', PARSE_ERROR, cause);\n            }\n            if (!isJsonObject(claims.claims)) {\n                throw CodedTypeError('\"claims\" parameter must be a JSON with a top level object', ERR_INVALID_ARG_VALUE);\n            }\n        }\n    }\n    {\n        let value = parameters.get('authorization_details');\n        if (value !== null) {\n            try {\n                claims.authorization_details = JSON.parse(value);\n            }\n            catch (cause) {\n                throw OPE('failed to parse the \"authorization_details\" parameter as JSON', PARSE_ERROR, cause);\n            }\n            if (!Array.isArray(claims.authorization_details)) {\n                throw CodedTypeError('\"authorization_details\" parameter must be a JSON with a top level array', ERR_INVALID_ARG_VALUE);\n            }\n        }\n    }\n    const header = {\n        alg: keyToJws(key),\n        typ: 'oauth-authz-req+jwt',\n        kid,\n    };\n    options?.[modifyAssertion]?.(header, claims);\n    return signJwt(header, claims, key);\n}\nlet jwkCache;\nasync function getSetPublicJwkCache(key) {\n    const { kty, e, n, x, y, crv } = await crypto.subtle.exportKey('jwk', key);\n    const jwk = { kty, e, n, x, y, crv };\n    jwkCache.set(key, jwk);\n    return jwk;\n}\nasync function publicJwk(key) {\n    jwkCache ||= new WeakMap();\n    return jwkCache.get(key) || getSetPublicJwkCache(key);\n}\nconst URLParse = URL.parse\n    ?\n        (url, base) => URL.parse(url, base)\n    : (url, base) => {\n        try {\n            return new URL(url, base);\n        }\n        catch {\n            return null;\n        }\n    };\nfunction checkProtocol(url, enforceHttps) {\n    if (enforceHttps && url.protocol !== 'https:') {\n        throw OPE('only requests to HTTPS are allowed', HTTP_REQUEST_FORBIDDEN, url);\n    }\n    if (url.protocol !== 'https:' && url.protocol !== 'http:') {\n        throw OPE('only HTTP and HTTPS requests are allowed', REQUEST_PROTOCOL_FORBIDDEN, url);\n    }\n}\nfunction validateEndpoint(value, endpoint, useMtlsAlias, enforceHttps) {\n    let url;\n    if (typeof value !== 'string' || !(url = URLParse(value))) {\n        throw OPE(`authorization server metadata does not contain a valid ${useMtlsAlias ? `\"as.mtls_endpoint_aliases.${endpoint}\"` : `\"as.${endpoint}\"`}`, value === undefined ? MISSING_SERVER_METADATA : INVALID_SERVER_METADATA, { attribute: useMtlsAlias ? `mtls_endpoint_aliases.${endpoint}` : endpoint });\n    }\n    checkProtocol(url, enforceHttps);\n    return url;\n}\nfunction resolveEndpoint(as, endpoint, useMtlsAlias, enforceHttps) {\n    if (useMtlsAlias && as.mtls_endpoint_aliases && endpoint in as.mtls_endpoint_aliases) {\n        return validateEndpoint(as.mtls_endpoint_aliases[endpoint], endpoint, useMtlsAlias, enforceHttps);\n    }\n    return validateEndpoint(as[endpoint], endpoint, useMtlsAlias, enforceHttps);\n}\nasync function pushedAuthorizationRequest(as, client, clientAuthentication, parameters, options) {\n    assertAs(as);\n    assertClient(client);\n    const url = resolveEndpoint(as, 'pushed_authorization_request_endpoint', client.use_mtls_endpoint_aliases, options?.[allowInsecureRequests] !== true);\n    const body = new URLSearchParams(parameters);\n    body.set('client_id', client.client_id);\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    if (options?.DPoP !== undefined) {\n        assertDPoP(options.DPoP);\n        await options.DPoP.addProof(url, headers, 'POST');\n    }\n    const response = await authenticatedRequest(as, client, clientAuthentication, url, body, headers, options);\n    options?.DPoP?.cacheNonce(response);\n    return response;\n}\nclass DPoPHandler {\n    #header;\n    #privateKey;\n    #publicKey;\n    #clockSkew;\n    #modifyAssertion;\n    #map;\n    #jkt;\n    constructor(client, keyPair, options) {\n        assertPrivateKey(keyPair?.privateKey, '\"DPoP.privateKey\"');\n        assertPublicKey(keyPair?.publicKey, '\"DPoP.publicKey\"');\n        if (!keyPair.publicKey.extractable) {\n            throw CodedTypeError('\"DPoP.publicKey.extractable\" must be true', ERR_INVALID_ARG_VALUE);\n        }\n        this.#modifyAssertion = options?.[modifyAssertion];\n        this.#clockSkew = getClockSkew(client);\n        this.#privateKey = keyPair.privateKey;\n        this.#publicKey = keyPair.publicKey;\n        branded.add(this);\n    }\n    #get(key) {\n        this.#map ||= new Map();\n        let item = this.#map.get(key);\n        if (item) {\n            this.#map.delete(key);\n            this.#map.set(key, item);\n        }\n        return item;\n    }\n    #set(key, val) {\n        this.#map ||= new Map();\n        this.#map.delete(key);\n        if (this.#map.size === 100) {\n            this.#map.delete(this.#map.keys().next().value);\n        }\n        this.#map.set(key, val);\n    }\n    async calculateThumbprint() {\n        if (!this.#jkt) {\n            const jwk = await crypto.subtle.exportKey('jwk', this.#publicKey);\n            let components;\n            switch (jwk.kty) {\n                case 'EC':\n                    components = { crv: jwk.crv, kty: jwk.kty, x: jwk.x, y: jwk.y };\n                    break;\n                case 'OKP':\n                    components = { crv: jwk.crv, kty: jwk.kty, x: jwk.x };\n                    break;\n                case 'RSA':\n                    components = { e: jwk.e, kty: jwk.kty, n: jwk.n };\n                    break;\n                default:\n                    throw new UnsupportedOperationError('unsupported JWK', { cause: { jwk } });\n            }\n            this.#jkt ||= b64u(await crypto.subtle.digest({ name: 'SHA-256' }, buf(JSON.stringify(components))));\n        }\n        return this.#jkt;\n    }\n    async addProof(url, headers, htm, accessToken) {\n        this.#header ||= {\n            alg: keyToJws(this.#privateKey),\n            typ: 'dpop+jwt',\n            jwk: await publicJwk(this.#publicKey),\n        };\n        const nonce = this.#get(url.origin);\n        const now = epochTime() + this.#clockSkew;\n        const payload = {\n            iat: now,\n            jti: randomBytes(),\n            htm,\n            nonce,\n            htu: `${url.origin}${url.pathname}`,\n            ath: accessToken ? b64u(await crypto.subtle.digest('SHA-256', buf(accessToken))) : undefined,\n        };\n        this.#modifyAssertion?.(this.#header, payload);\n        headers.set('dpop', await signJwt(this.#header, payload, this.#privateKey));\n    }\n    cacheNonce(response) {\n        try {\n            const nonce = response.headers.get('dpop-nonce');\n            if (nonce) {\n                this.#set(new URL(response.url).origin, nonce);\n            }\n        }\n        catch { }\n    }\n}\nfunction isDPoPNonceError(err) {\n    if (err instanceof WWWAuthenticateChallengeError) {\n        const { 0: challenge, length } = err.cause;\n        return (length === 1 && challenge.scheme === 'dpop' && challenge.parameters.error === 'use_dpop_nonce');\n    }\n    if (err instanceof ResponseBodyError) {\n        return err.error === 'use_dpop_nonce';\n    }\n    return false;\n}\nfunction DPoP(client, keyPair, options) {\n    return new DPoPHandler(client, keyPair, options);\n}\nclass ResponseBodyError extends Error {\n    cause;\n    code;\n    error;\n    status;\n    error_description;\n    response;\n    constructor(message, options) {\n        super(message, options);\n        this.name = this.constructor.name;\n        this.code = RESPONSE_BODY_ERROR;\n        this.cause = options.cause;\n        this.error = options.cause.error;\n        this.status = options.response.status;\n        this.error_description = options.cause.error_description;\n        Object.defineProperty(this, 'response', { enumerable: false, value: options.response });\n        Error.captureStackTrace?.(this, this.constructor);\n    }\n}\nclass AuthorizationResponseError extends Error {\n    cause;\n    code;\n    error;\n    error_description;\n    constructor(message, options) {\n        super(message, options);\n        this.name = this.constructor.name;\n        this.code = AUTHORIZATION_RESPONSE_ERROR;\n        this.cause = options.cause;\n        this.error = options.cause.get('error');\n        this.error_description = options.cause.get('error_description') ?? undefined;\n        Error.captureStackTrace?.(this, this.constructor);\n    }\n}\nclass WWWAuthenticateChallengeError extends Error {\n    cause;\n    code;\n    response;\n    status;\n    constructor(message, options) {\n        super(message, options);\n        this.name = this.constructor.name;\n        this.code = WWW_AUTHENTICATE_CHALLENGE;\n        this.cause = options.cause;\n        this.status = options.response.status;\n        this.response = options.response;\n        Object.defineProperty(this, 'response', { enumerable: false });\n        Error.captureStackTrace?.(this, this.constructor);\n    }\n}\nconst tokenMatch = \"[a-zA-Z0-9!#$%&\\\\'\\\\*\\\\+\\\\-\\\\.\\\\^_`\\\\|~]+\";\nconst token68Match = '[a-zA-Z0-9\\\\-\\\\._\\\\~\\\\+\\\\/]+[=]{0,2}';\nconst quotedMatch = '\"((?:[^\"\\\\\\\\]|\\\\\\\\.)*)\"';\nconst quotedParamMatcher = '(' + tokenMatch + ')\\\\s*=\\\\s*' + quotedMatch;\nconst paramMatcher = '(' + tokenMatch + ')\\\\s*=\\\\s*(' + tokenMatch + ')';\nconst schemeRE = new RegExp('^[,\\\\s]*(' + tokenMatch + ')\\\\s(.*)');\nconst quotedParamRE = new RegExp('^[,\\\\s]*' + quotedParamMatcher + '[,\\\\s]*(.*)');\nconst unquotedParamRE = new RegExp('^[,\\\\s]*' + paramMatcher + '[,\\\\s]*(.*)');\nconst token68ParamRE = new RegExp('^(' + token68Match + ')(?:$|[,\\\\s])(.*)');\nfunction parseWwwAuthenticateChallenges(response) {\n    if (!looseInstanceOf(response, Response)) {\n        throw CodedTypeError('\"response\" must be an instance of Response', ERR_INVALID_ARG_TYPE);\n    }\n    const header = response.headers.get('www-authenticate');\n    if (header === null) {\n        return undefined;\n    }\n    const challenges = [];\n    let rest = header;\n    while (rest) {\n        let match = rest.match(schemeRE);\n        const scheme = match?.['1'].toLowerCase();\n        rest = match?.['2'];\n        if (!scheme) {\n            return undefined;\n        }\n        const parameters = {};\n        let token68;\n        while (rest) {\n            let key;\n            let value;\n            if ((match = rest.match(quotedParamRE))) {\n                ;\n                [, key, value, rest] = match;\n                if (value.includes('\\\\')) {\n                    try {\n                        value = JSON.parse(`\"${value}\"`);\n                    }\n                    catch { }\n                }\n                parameters[key.toLowerCase()] = value;\n                continue;\n            }\n            if ((match = rest.match(unquotedParamRE))) {\n                ;\n                [, key, value, rest] = match;\n                parameters[key.toLowerCase()] = value;\n                continue;\n            }\n            if ((match = rest.match(token68ParamRE))) {\n                if (Object.keys(parameters).length) {\n                    break;\n                }\n                ;\n                [, token68, rest] = match;\n                break;\n            }\n            return undefined;\n        }\n        const challenge = { scheme, parameters };\n        if (token68) {\n            challenge.token68 = token68;\n        }\n        challenges.push(challenge);\n    }\n    if (!challenges.length) {\n        return undefined;\n    }\n    return challenges;\n}\nasync function processPushedAuthorizationResponse(as, client, response) {\n    assertAs(as);\n    assertClient(client);\n    if (!looseInstanceOf(response, Response)) {\n        throw CodedTypeError('\"response\" must be an instance of Response', ERR_INVALID_ARG_TYPE);\n    }\n    checkAuthenticationChallenges(response);\n    await checkOAuthBodyError(response, 201, 'Pushed Authorization Request Endpoint');\n    assertReadableResponse(response);\n    const json = await getResponseJsonBody(response);\n    assertString(json.request_uri, '\"response\" body \"request_uri\" property', INVALID_RESPONSE, {\n        body: json,\n    });\n    let expiresIn = typeof json.expires_in !== 'number' ? parseFloat(json.expires_in) : json.expires_in;\n    assertNumber(expiresIn, false, '\"response\" body \"expires_in\" property', INVALID_RESPONSE, {\n        body: json,\n    });\n    json.expires_in = expiresIn;\n    return json;\n}\nasync function parseOAuthResponseErrorBody(response) {\n    if (response.status > 399 && response.status < 500) {\n        assertReadableResponse(response);\n        assertApplicationJson(response);\n        try {\n            const json = await response.clone().json();\n            if (isJsonObject(json) && typeof json.error === 'string' && json.error.length) {\n                return json;\n            }\n        }\n        catch { }\n    }\n    return undefined;\n}\nasync function checkOAuthBodyError(response, expected, label) {\n    if (response.status !== expected) {\n        let err;\n        if ((err = await parseOAuthResponseErrorBody(response))) {\n            await response.body?.cancel();\n            throw new ResponseBodyError('server responded with an error in the response body', {\n                cause: err,\n                response,\n            });\n        }\n        throw OPE(`\"response\" is not a conform ${label} response (unexpected HTTP status code)`, RESPONSE_IS_NOT_CONFORM, response);\n    }\n}\nfunction assertDPoP(option) {\n    if (!branded.has(option)) {\n        throw CodedTypeError('\"options.DPoP\" is not a valid DPoPHandle', ERR_INVALID_ARG_VALUE);\n    }\n}\nasync function resourceRequest(accessToken, method, url, headers, body, options) {\n    assertString(accessToken, '\"accessToken\"');\n    if (!(url instanceof URL)) {\n        throw CodedTypeError('\"url\" must be an instance of URL', ERR_INVALID_ARG_TYPE);\n    }\n    checkProtocol(url, options?.[allowInsecureRequests] !== true);\n    headers = prepareHeaders(headers);\n    if (options?.DPoP) {\n        assertDPoP(options.DPoP);\n        await options.DPoP.addProof(url, headers, method.toUpperCase(), accessToken);\n    }\n    headers.set('authorization', `${headers.has('dpop') ? 'DPoP' : 'Bearer'} ${accessToken}`);\n    const response = await (options?.[customFetch] || fetch)(url.href, {\n        body,\n        headers: Object.fromEntries(headers.entries()),\n        method,\n        redirect: 'manual',\n        signal: options?.signal ? signal(options.signal) : undefined,\n    });\n    options?.DPoP?.cacheNonce(response);\n    return response;\n}\nasync function protectedResourceRequest(accessToken, method, url, headers, body, options) {\n    const response = await resourceRequest(accessToken, method, url, headers, body, options);\n    checkAuthenticationChallenges(response);\n    return response;\n}\nasync function userInfoRequest(as, client, accessToken, options) {\n    assertAs(as);\n    assertClient(client);\n    const url = resolveEndpoint(as, 'userinfo_endpoint', client.use_mtls_endpoint_aliases, options?.[allowInsecureRequests] !== true);\n    const headers = prepareHeaders(options?.headers);\n    if (client.userinfo_signed_response_alg) {\n        headers.set('accept', 'application/jwt');\n    }\n    else {\n        headers.set('accept', 'application/json');\n        headers.append('accept', 'application/jwt');\n    }\n    return resourceRequest(accessToken, 'GET', url, headers, null, {\n        ...options,\n        [clockSkew]: getClockSkew(client),\n    });\n}\nlet jwksMap;\nfunction setJwksCache(as, jwks, uat, cache) {\n    jwksMap ||= new WeakMap();\n    jwksMap.set(as, {\n        jwks,\n        uat,\n        get age() {\n            return epochTime() - this.uat;\n        },\n    });\n    if (cache) {\n        Object.assign(cache, { jwks: structuredClone(jwks), uat });\n    }\n}\nfunction isFreshJwksCache(input) {\n    if (typeof input !== 'object' || input === null) {\n        return false;\n    }\n    if (!('uat' in input) || typeof input.uat !== 'number' || epochTime() - input.uat >= 300) {\n        return false;\n    }\n    if (!('jwks' in input) ||\n        !isJsonObject(input.jwks) ||\n        !Array.isArray(input.jwks.keys) ||\n        !Array.prototype.every.call(input.jwks.keys, isJsonObject)) {\n        return false;\n    }\n    return true;\n}\nfunction clearJwksCache(as, cache) {\n    jwksMap?.delete(as);\n    delete cache?.jwks;\n    delete cache?.uat;\n}\nasync function getPublicSigKeyFromIssuerJwksUri(as, options, header) {\n    const { alg, kid } = header;\n    checkSupportedJwsAlg(header);\n    if (!jwksMap?.has(as) && isFreshJwksCache(options?.[jwksCache])) {\n        setJwksCache(as, options?.[jwksCache].jwks, options?.[jwksCache].uat);\n    }\n    let jwks;\n    let age;\n    if (jwksMap?.has(as)) {\n        ;\n        ({ jwks, age } = jwksMap.get(as));\n        if (age >= 300) {\n            clearJwksCache(as, options?.[jwksCache]);\n            return getPublicSigKeyFromIssuerJwksUri(as, options, header);\n        }\n    }\n    else {\n        jwks = await jwksRequest(as, options).then(processJwksResponse);\n        age = 0;\n        setJwksCache(as, jwks, epochTime(), options?.[jwksCache]);\n    }\n    let kty;\n    switch (alg.slice(0, 2)) {\n        case 'RS':\n        case 'PS':\n            kty = 'RSA';\n            break;\n        case 'ES':\n            kty = 'EC';\n            break;\n        case 'Ed':\n            kty = 'OKP';\n            break;\n        default:\n            throw new UnsupportedOperationError('unsupported JWS algorithm', { cause: { alg } });\n    }\n    const candidates = jwks.keys.filter((jwk) => {\n        if (jwk.kty !== kty) {\n            return false;\n        }\n        if (kid !== undefined && kid !== jwk.kid) {\n            return false;\n        }\n        if (jwk.alg !== undefined && alg !== jwk.alg) {\n            return false;\n        }\n        if (jwk.use !== undefined && jwk.use !== 'sig') {\n            return false;\n        }\n        if (jwk.key_ops?.includes('verify') === false) {\n            return false;\n        }\n        switch (true) {\n            case alg === 'ES256' && jwk.crv !== 'P-256':\n            case alg === 'ES384' && jwk.crv !== 'P-384':\n            case alg === 'ES512' && jwk.crv !== 'P-521':\n            case alg === 'Ed25519' && jwk.crv !== 'Ed25519':\n            case alg === 'EdDSA' && jwk.crv !== 'Ed25519':\n                return false;\n        }\n        return true;\n    });\n    const { 0: jwk, length } = candidates;\n    if (!length) {\n        if (age >= 60) {\n            clearJwksCache(as, options?.[jwksCache]);\n            return getPublicSigKeyFromIssuerJwksUri(as, options, header);\n        }\n        throw OPE('error when selecting a JWT verification key, no applicable keys found', KEY_SELECTION, { header, candidates, jwks_uri: new URL(as.jwks_uri) });\n    }\n    if (length !== 1) {\n        throw OPE('error when selecting a JWT verification key, multiple applicable keys found, a \"kid\" JWT Header Parameter is required', KEY_SELECTION, { header, candidates, jwks_uri: new URL(as.jwks_uri) });\n    }\n    return importJwk(alg, jwk);\n}\nconst skipSubjectCheck = Symbol();\nfunction getContentType(input) {\n    return input.headers.get('content-type')?.split(';')[0];\n}\nasync function processUserInfoResponse(as, client, expectedSubject, response, options) {\n    assertAs(as);\n    assertClient(client);\n    if (!looseInstanceOf(response, Response)) {\n        throw CodedTypeError('\"response\" must be an instance of Response', ERR_INVALID_ARG_TYPE);\n    }\n    checkAuthenticationChallenges(response);\n    if (response.status !== 200) {\n        throw OPE('\"response\" is not a conform UserInfo Endpoint response (unexpected HTTP status code)', RESPONSE_IS_NOT_CONFORM, response);\n    }\n    assertReadableResponse(response);\n    let json;\n    if (getContentType(response) === 'application/jwt') {\n        const { claims, jwt } = await validateJwt(await response.text(), checkSigningAlgorithm.bind(undefined, client.userinfo_signed_response_alg, as.userinfo_signing_alg_values_supported, undefined), getClockSkew(client), getClockTolerance(client), options?.[jweDecrypt])\n            .then(validateOptionalAudience.bind(undefined, client.client_id))\n            .then(validateOptionalIssuer.bind(undefined, as));\n        jwtRefs.set(response, jwt);\n        json = claims;\n    }\n    else {\n        if (client.userinfo_signed_response_alg) {\n            throw OPE('JWT UserInfo Response expected', JWT_USERINFO_EXPECTED, response);\n        }\n        json = await getResponseJsonBody(response);\n    }\n    assertString(json.sub, '\"response\" body \"sub\" property', INVALID_RESPONSE, { body: json });\n    switch (expectedSubject) {\n        case skipSubjectCheck:\n            break;\n        default:\n            assertString(expectedSubject, '\"expectedSubject\"');\n            if (json.sub !== expectedSubject) {\n                throw OPE('unexpected \"response\" body \"sub\" property value', JSON_ATTRIBUTE_COMPARISON, {\n                    expected: expectedSubject,\n                    body: json,\n                    attribute: 'sub',\n                });\n            }\n    }\n    return json;\n}\nasync function authenticatedRequest(as, client, clientAuthentication, url, body, headers, options) {\n    await clientAuthentication(as, client, body, headers);\n    headers.set('content-type', 'application/x-www-form-urlencoded;charset=UTF-8');\n    return (options?.[customFetch] || fetch)(url.href, {\n        body,\n        headers: Object.fromEntries(headers.entries()),\n        method: 'POST',\n        redirect: 'manual',\n        signal: options?.signal ? signal(options.signal) : undefined,\n    });\n}\nasync function tokenEndpointRequest(as, client, clientAuthentication, grantType, parameters, options) {\n    const url = resolveEndpoint(as, 'token_endpoint', client.use_mtls_endpoint_aliases, options?.[allowInsecureRequests] !== true);\n    parameters.set('grant_type', grantType);\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    if (options?.DPoP !== undefined) {\n        assertDPoP(options.DPoP);\n        await options.DPoP.addProof(url, headers, 'POST');\n    }\n    const response = await authenticatedRequest(as, client, clientAuthentication, url, parameters, headers, options);\n    options?.DPoP?.cacheNonce(response);\n    return response;\n}\nasync function refreshTokenGrantRequest(as, client, clientAuthentication, refreshToken, options) {\n    assertAs(as);\n    assertClient(client);\n    assertString(refreshToken, '\"refreshToken\"');\n    const parameters = new URLSearchParams(options?.additionalParameters);\n    parameters.set('refresh_token', refreshToken);\n    return tokenEndpointRequest(as, client, clientAuthentication, 'refresh_token', parameters, options);\n}\nconst idTokenClaims = new WeakMap();\nconst jwtRefs = new WeakMap();\nfunction getValidatedIdTokenClaims(ref) {\n    if (!ref.id_token) {\n        return undefined;\n    }\n    const claims = idTokenClaims.get(ref);\n    if (!claims) {\n        throw CodedTypeError('\"ref\" was already garbage collected or did not resolve from the proper sources', ERR_INVALID_ARG_VALUE);\n    }\n    return claims;\n}\nasync function validateApplicationLevelSignature(as, ref, options) {\n    assertAs(as);\n    if (!jwtRefs.has(ref)) {\n        throw CodedTypeError('\"ref\" does not contain a processed JWT Response to verify the signature of', ERR_INVALID_ARG_VALUE);\n    }\n    const { 0: protectedHeader, 1: payload, 2: encodedSignature } = jwtRefs.get(ref).split('.');\n    const header = JSON.parse(buf(b64u(protectedHeader)));\n    if (header.alg.startsWith('HS')) {\n        throw new UnsupportedOperationError('unsupported JWS algorithm', { cause: { alg: header.alg } });\n    }\n    let key;\n    key = await getPublicSigKeyFromIssuerJwksUri(as, options, header);\n    await validateJwsSignature(protectedHeader, payload, key, b64u(encodedSignature));\n}\nasync function processGenericAccessTokenResponse(as, client, response, additionalRequiredIdTokenClaims, options) {\n    assertAs(as);\n    assertClient(client);\n    if (!looseInstanceOf(response, Response)) {\n        throw CodedTypeError('\"response\" must be an instance of Response', ERR_INVALID_ARG_TYPE);\n    }\n    checkAuthenticationChallenges(response);\n    await checkOAuthBodyError(response, 200, 'Token Endpoint');\n    assertReadableResponse(response);\n    const json = await getResponseJsonBody(response);\n    assertString(json.access_token, '\"response\" body \"access_token\" property', INVALID_RESPONSE, {\n        body: json,\n    });\n    assertString(json.token_type, '\"response\" body \"token_type\" property', INVALID_RESPONSE, {\n        body: json,\n    });\n    json.token_type = json.token_type.toLowerCase();\n    if (json.token_type !== 'dpop' && json.token_type !== 'bearer') {\n        throw new UnsupportedOperationError('unsupported `token_type` value', { cause: { body: json } });\n    }\n    if (json.expires_in !== undefined) {\n        let expiresIn = typeof json.expires_in !== 'number' ? parseFloat(json.expires_in) : json.expires_in;\n        assertNumber(expiresIn, false, '\"response\" body \"expires_in\" property', INVALID_RESPONSE, {\n            body: json,\n        });\n        json.expires_in = expiresIn;\n    }\n    if (json.refresh_token !== undefined) {\n        assertString(json.refresh_token, '\"response\" body \"refresh_token\" property', INVALID_RESPONSE, {\n            body: json,\n        });\n    }\n    if (json.scope !== undefined && typeof json.scope !== 'string') {\n        throw OPE('\"response\" body \"scope\" property must be a string', INVALID_RESPONSE, { body: json });\n    }\n    if (json.id_token !== undefined) {\n        assertString(json.id_token, '\"response\" body \"id_token\" property', INVALID_RESPONSE, {\n            body: json,\n        });\n        const requiredClaims = ['aud', 'exp', 'iat', 'iss', 'sub'];\n        if (client.require_auth_time === true) {\n            requiredClaims.push('auth_time');\n        }\n        if (client.default_max_age !== undefined) {\n            assertNumber(client.default_max_age, false, '\"client.default_max_age\"');\n            requiredClaims.push('auth_time');\n        }\n        if (additionalRequiredIdTokenClaims?.length) {\n            requiredClaims.push(...additionalRequiredIdTokenClaims);\n        }\n        const { claims, jwt } = await validateJwt(json.id_token, checkSigningAlgorithm.bind(undefined, client.id_token_signed_response_alg, as.id_token_signing_alg_values_supported, 'RS256'), getClockSkew(client), getClockTolerance(client), options?.[jweDecrypt])\n            .then(validatePresence.bind(undefined, requiredClaims))\n            .then(validateIssuer.bind(undefined, as))\n            .then(validateAudience.bind(undefined, client.client_id));\n        if (Array.isArray(claims.aud) && claims.aud.length !== 1) {\n            if (claims.azp === undefined) {\n                throw OPE('ID Token \"aud\" (audience) claim includes additional untrusted audiences', JWT_CLAIM_COMPARISON, { claims, claim: 'aud' });\n            }\n            if (claims.azp !== client.client_id) {\n                throw OPE('unexpected ID Token \"azp\" (authorized party) claim value', JWT_CLAIM_COMPARISON, { expected: client.client_id, claims, claim: 'azp' });\n            }\n        }\n        if (claims.auth_time !== undefined) {\n            assertNumber(claims.auth_time, false, 'ID Token \"auth_time\" (authentication time)', INVALID_RESPONSE, { claims });\n        }\n        jwtRefs.set(response, jwt);\n        idTokenClaims.set(json, claims);\n    }\n    return json;\n}\nfunction checkAuthenticationChallenges(response) {\n    let challenges;\n    if ((challenges = parseWwwAuthenticateChallenges(response))) {\n        throw new WWWAuthenticateChallengeError('server responded with a challenge in the WWW-Authenticate HTTP Header', { cause: challenges, response });\n    }\n}\nasync function processRefreshTokenResponse(as, client, response, options) {\n    return processGenericAccessTokenResponse(as, client, response, undefined, options);\n}\nfunction validateOptionalAudience(expected, result) {\n    if (result.claims.aud !== undefined) {\n        return validateAudience(expected, result);\n    }\n    return result;\n}\nfunction validateAudience(expected, result) {\n    if (Array.isArray(result.claims.aud)) {\n        if (!result.claims.aud.includes(expected)) {\n            throw OPE('unexpected JWT \"aud\" (audience) claim value', JWT_CLAIM_COMPARISON, {\n                expected,\n                claims: result.claims,\n                claim: 'aud',\n            });\n        }\n    }\n    else if (result.claims.aud !== expected) {\n        throw OPE('unexpected JWT \"aud\" (audience) claim value', JWT_CLAIM_COMPARISON, {\n            expected,\n            claims: result.claims,\n            claim: 'aud',\n        });\n    }\n    return result;\n}\nfunction validateOptionalIssuer(as, result) {\n    if (result.claims.iss !== undefined) {\n        return validateIssuer(as, result);\n    }\n    return result;\n}\nfunction validateIssuer(as, result) {\n    const expected = as[_expectedIssuer]?.(result) ?? as.issuer;\n    if (result.claims.iss !== expected) {\n        throw OPE('unexpected JWT \"iss\" (issuer) claim value', JWT_CLAIM_COMPARISON, {\n            expected,\n            claims: result.claims,\n            claim: 'iss',\n        });\n    }\n    return result;\n}\nconst branded = new WeakSet();\nfunction brand(searchParams) {\n    branded.add(searchParams);\n    return searchParams;\n}\nconst nopkce = Symbol();\nasync function authorizationCodeGrantRequest(as, client, clientAuthentication, callbackParameters, redirectUri, codeVerifier, options) {\n    assertAs(as);\n    assertClient(client);\n    if (!branded.has(callbackParameters)) {\n        throw CodedTypeError('\"callbackParameters\" must be an instance of URLSearchParams obtained from \"validateAuthResponse()\", or \"validateJwtAuthResponse()', ERR_INVALID_ARG_VALUE);\n    }\n    assertString(redirectUri, '\"redirectUri\"');\n    const code = getURLSearchParameter(callbackParameters, 'code');\n    if (!code) {\n        throw OPE('no authorization code in \"callbackParameters\"', INVALID_RESPONSE);\n    }\n    const parameters = new URLSearchParams(options?.additionalParameters);\n    parameters.set('redirect_uri', redirectUri);\n    parameters.set('code', code);\n    if (codeVerifier !== nopkce) {\n        assertString(codeVerifier, '\"codeVerifier\"');\n        parameters.set('code_verifier', codeVerifier);\n    }\n    return tokenEndpointRequest(as, client, clientAuthentication, 'authorization_code', parameters, options);\n}\nconst jwtClaimNames = {\n    aud: 'audience',\n    c_hash: 'code hash',\n    client_id: 'client id',\n    exp: 'expiration time',\n    iat: 'issued at',\n    iss: 'issuer',\n    jti: 'jwt id',\n    nonce: 'nonce',\n    s_hash: 'state hash',\n    sub: 'subject',\n    ath: 'access token hash',\n    htm: 'http method',\n    htu: 'http uri',\n    cnf: 'confirmation',\n    auth_time: 'authentication time',\n};\nfunction validatePresence(required, result) {\n    for (const claim of required) {\n        if (result.claims[claim] === undefined) {\n            throw OPE(`JWT \"${claim}\" (${jwtClaimNames[claim]}) claim missing`, INVALID_RESPONSE, {\n                claims: result.claims,\n            });\n        }\n    }\n    return result;\n}\nconst expectNoNonce = Symbol();\nconst skipAuthTimeCheck = Symbol();\nasync function processAuthorizationCodeResponse(as, client, response, options) {\n    if (typeof options?.expectedNonce === 'string' ||\n        typeof options?.maxAge === 'number' ||\n        options?.requireIdToken) {\n        return processAuthorizationCodeOpenIDResponse(as, client, response, options.expectedNonce, options.maxAge, {\n            [jweDecrypt]: options[jweDecrypt],\n        });\n    }\n    return processAuthorizationCodeOAuth2Response(as, client, response, options);\n}\nasync function processAuthorizationCodeOpenIDResponse(as, client, response, expectedNonce, maxAge, options) {\n    const additionalRequiredClaims = [];\n    switch (expectedNonce) {\n        case undefined:\n            expectedNonce = expectNoNonce;\n            break;\n        case expectNoNonce:\n            break;\n        default:\n            assertString(expectedNonce, '\"expectedNonce\" argument');\n            additionalRequiredClaims.push('nonce');\n    }\n    maxAge ??= client.default_max_age;\n    switch (maxAge) {\n        case undefined:\n            maxAge = skipAuthTimeCheck;\n            break;\n        case skipAuthTimeCheck:\n            break;\n        default:\n            assertNumber(maxAge, false, '\"maxAge\" argument');\n            additionalRequiredClaims.push('auth_time');\n    }\n    const result = await processGenericAccessTokenResponse(as, client, response, additionalRequiredClaims, options);\n    assertString(result.id_token, '\"response\" body \"id_token\" property', INVALID_RESPONSE, {\n        body: result,\n    });\n    const claims = getValidatedIdTokenClaims(result);\n    if (maxAge !== skipAuthTimeCheck) {\n        const now = epochTime() + getClockSkew(client);\n        const tolerance = getClockTolerance(client);\n        if (claims.auth_time + maxAge < now - tolerance) {\n            throw OPE('too much time has elapsed since the last End-User authentication', JWT_TIMESTAMP_CHECK, { claims, now, tolerance, claim: 'auth_time' });\n        }\n    }\n    if (expectedNonce === expectNoNonce) {\n        if (claims.nonce !== undefined) {\n            throw OPE('unexpected ID Token \"nonce\" claim value', JWT_CLAIM_COMPARISON, {\n                expected: undefined,\n                claims,\n                claim: 'nonce',\n            });\n        }\n    }\n    else if (claims.nonce !== expectedNonce) {\n        throw OPE('unexpected ID Token \"nonce\" claim value', JWT_CLAIM_COMPARISON, {\n            expected: expectedNonce,\n            claims,\n            claim: 'nonce',\n        });\n    }\n    return result;\n}\nasync function processAuthorizationCodeOAuth2Response(as, client, response, options) {\n    const result = await processGenericAccessTokenResponse(as, client, response, undefined, options);\n    const claims = getValidatedIdTokenClaims(result);\n    if (claims) {\n        if (client.default_max_age !== undefined) {\n            assertNumber(client.default_max_age, false, '\"client.default_max_age\"');\n            const now = epochTime() + getClockSkew(client);\n            const tolerance = getClockTolerance(client);\n            if (claims.auth_time + client.default_max_age < now - tolerance) {\n                throw OPE('too much time has elapsed since the last End-User authentication', JWT_TIMESTAMP_CHECK, { claims, now, tolerance, claim: 'auth_time' });\n            }\n        }\n        if (claims.nonce !== undefined) {\n            throw OPE('unexpected ID Token \"nonce\" claim value', JWT_CLAIM_COMPARISON, {\n                expected: undefined,\n                claims,\n                claim: 'nonce',\n            });\n        }\n    }\n    return result;\n}\nconst WWW_AUTHENTICATE_CHALLENGE = 'OAUTH_WWW_AUTHENTICATE_CHALLENGE';\nconst RESPONSE_BODY_ERROR = 'OAUTH_RESPONSE_BODY_ERROR';\nconst UNSUPPORTED_OPERATION = 'OAUTH_UNSUPPORTED_OPERATION';\nconst AUTHORIZATION_RESPONSE_ERROR = 'OAUTH_AUTHORIZATION_RESPONSE_ERROR';\nconst JWT_USERINFO_EXPECTED = 'OAUTH_JWT_USERINFO_EXPECTED';\nconst PARSE_ERROR = 'OAUTH_PARSE_ERROR';\nconst INVALID_RESPONSE = 'OAUTH_INVALID_RESPONSE';\nconst INVALID_REQUEST = 'OAUTH_INVALID_REQUEST';\nconst RESPONSE_IS_NOT_JSON = 'OAUTH_RESPONSE_IS_NOT_JSON';\nconst RESPONSE_IS_NOT_CONFORM = 'OAUTH_RESPONSE_IS_NOT_CONFORM';\nconst HTTP_REQUEST_FORBIDDEN = 'OAUTH_HTTP_REQUEST_FORBIDDEN';\nconst REQUEST_PROTOCOL_FORBIDDEN = 'OAUTH_REQUEST_PROTOCOL_FORBIDDEN';\nconst JWT_TIMESTAMP_CHECK = 'OAUTH_JWT_TIMESTAMP_CHECK_FAILED';\nconst JWT_CLAIM_COMPARISON = 'OAUTH_JWT_CLAIM_COMPARISON_FAILED';\nconst JSON_ATTRIBUTE_COMPARISON = 'OAUTH_JSON_ATTRIBUTE_COMPARISON_FAILED';\nconst KEY_SELECTION = 'OAUTH_KEY_SELECTION_FAILED';\nconst MISSING_SERVER_METADATA = 'OAUTH_MISSING_SERVER_METADATA';\nconst INVALID_SERVER_METADATA = 'OAUTH_INVALID_SERVER_METADATA';\nfunction checkJwtType(expected, result) {\n    if (typeof result.header.typ !== 'string' || normalizeTyp(result.header.typ) !== expected) {\n        throw OPE('unexpected JWT \"typ\" header parameter value', INVALID_RESPONSE, {\n            header: result.header,\n        });\n    }\n    return result;\n}\nasync function clientCredentialsGrantRequest(as, client, clientAuthentication, parameters, options) {\n    assertAs(as);\n    assertClient(client);\n    return tokenEndpointRequest(as, client, clientAuthentication, 'client_credentials', new URLSearchParams(parameters), options);\n}\nasync function genericTokenEndpointRequest(as, client, clientAuthentication, grantType, parameters, options) {\n    assertAs(as);\n    assertClient(client);\n    assertString(grantType, '\"grantType\"');\n    return tokenEndpointRequest(as, client, clientAuthentication, grantType, new URLSearchParams(parameters), options);\n}\nasync function processGenericTokenEndpointResponse(as, client, response, options) {\n    return processGenericAccessTokenResponse(as, client, response, undefined, options);\n}\nasync function processClientCredentialsResponse(as, client, response, options) {\n    return processGenericAccessTokenResponse(as, client, response, undefined, options);\n}\nasync function revocationRequest(as, client, clientAuthentication, token, options) {\n    assertAs(as);\n    assertClient(client);\n    assertString(token, '\"token\"');\n    const url = resolveEndpoint(as, 'revocation_endpoint', client.use_mtls_endpoint_aliases, options?.[allowInsecureRequests] !== true);\n    const body = new URLSearchParams(options?.additionalParameters);\n    body.set('token', token);\n    const headers = prepareHeaders(options?.headers);\n    headers.delete('accept');\n    return authenticatedRequest(as, client, clientAuthentication, url, body, headers, options);\n}\nasync function processRevocationResponse(response) {\n    if (!looseInstanceOf(response, Response)) {\n        throw CodedTypeError('\"response\" must be an instance of Response', ERR_INVALID_ARG_TYPE);\n    }\n    checkAuthenticationChallenges(response);\n    await checkOAuthBodyError(response, 200, 'Revocation Endpoint');\n    return undefined;\n}\nfunction assertReadableResponse(response) {\n    if (response.bodyUsed) {\n        throw CodedTypeError('\"response\" body has been used already', ERR_INVALID_ARG_VALUE);\n    }\n}\nasync function introspectionRequest(as, client, clientAuthentication, token, options) {\n    assertAs(as);\n    assertClient(client);\n    assertString(token, '\"token\"');\n    const url = resolveEndpoint(as, 'introspection_endpoint', client.use_mtls_endpoint_aliases, options?.[allowInsecureRequests] !== true);\n    const body = new URLSearchParams(options?.additionalParameters);\n    body.set('token', token);\n    const headers = prepareHeaders(options?.headers);\n    if (options?.requestJwtResponse ?? client.introspection_signed_response_alg) {\n        headers.set('accept', 'application/token-introspection+jwt');\n    }\n    else {\n        headers.set('accept', 'application/json');\n    }\n    return authenticatedRequest(as, client, clientAuthentication, url, body, headers, options);\n}\nasync function processIntrospectionResponse(as, client, response, options) {\n    assertAs(as);\n    assertClient(client);\n    if (!looseInstanceOf(response, Response)) {\n        throw CodedTypeError('\"response\" must be an instance of Response', ERR_INVALID_ARG_TYPE);\n    }\n    checkAuthenticationChallenges(response);\n    await checkOAuthBodyError(response, 200, 'Introspection Endpoint');\n    let json;\n    if (getContentType(response) === 'application/token-introspection+jwt') {\n        assertReadableResponse(response);\n        const { claims, jwt } = await validateJwt(await response.text(), checkSigningAlgorithm.bind(undefined, client.introspection_signed_response_alg, as.introspection_signing_alg_values_supported, 'RS256'), getClockSkew(client), getClockTolerance(client), options?.[jweDecrypt])\n            .then(checkJwtType.bind(undefined, 'token-introspection+jwt'))\n            .then(validatePresence.bind(undefined, ['aud', 'iat', 'iss']))\n            .then(validateIssuer.bind(undefined, as))\n            .then(validateAudience.bind(undefined, client.client_id));\n        jwtRefs.set(response, jwt);\n        if (!isJsonObject(claims.token_introspection)) {\n            throw OPE('JWT \"token_introspection\" claim must be a JSON object', INVALID_RESPONSE, {\n                claims,\n            });\n        }\n        json = claims.token_introspection;\n    }\n    else {\n        assertReadableResponse(response);\n        json = await getResponseJsonBody(response);\n    }\n    if (typeof json.active !== 'boolean') {\n        throw OPE('\"response\" body \"active\" property must be a boolean', INVALID_RESPONSE, {\n            body: json,\n        });\n    }\n    return json;\n}\nasync function jwksRequest(as, options) {\n    assertAs(as);\n    const url = resolveEndpoint(as, 'jwks_uri', false, options?.[allowInsecureRequests] !== true);\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    headers.append('accept', 'application/jwk-set+json');\n    return (options?.[customFetch] || fetch)(url.href, {\n        body: undefined,\n        headers: Object.fromEntries(headers.entries()),\n        method: 'GET',\n        redirect: 'manual',\n        signal: options?.signal ? signal(options.signal) : undefined,\n    });\n}\nasync function processJwksResponse(response) {\n    if (!looseInstanceOf(response, Response)) {\n        throw CodedTypeError('\"response\" must be an instance of Response', ERR_INVALID_ARG_TYPE);\n    }\n    if (response.status !== 200) {\n        throw OPE('\"response\" is not a conform JSON Web Key Set response (unexpected HTTP status code)', RESPONSE_IS_NOT_CONFORM, response);\n    }\n    assertReadableResponse(response);\n    const json = await getResponseJsonBody(response, (response) => assertContentTypes(response, 'application/json', 'application/jwk-set+json'));\n    if (!Array.isArray(json.keys)) {\n        throw OPE('\"response\" body \"keys\" property must be an array', INVALID_RESPONSE, { body: json });\n    }\n    if (!Array.prototype.every.call(json.keys, isJsonObject)) {\n        throw OPE('\"response\" body \"keys\" property members must be JWK formatted objects', INVALID_RESPONSE, { body: json });\n    }\n    return json;\n}\nfunction supported(alg) {\n    switch (alg) {\n        case 'PS256':\n        case 'ES256':\n        case 'RS256':\n        case 'PS384':\n        case 'ES384':\n        case 'RS384':\n        case 'PS512':\n        case 'ES512':\n        case 'RS512':\n        case 'Ed25519':\n        case 'EdDSA':\n            return true;\n        default:\n            return false;\n    }\n}\nfunction checkSupportedJwsAlg(header) {\n    if (!supported(header.alg)) {\n        throw new UnsupportedOperationError('unsupported JWS \"alg\" identifier', {\n            cause: { alg: header.alg },\n        });\n    }\n}\nfunction checkRsaKeyAlgorithm(key) {\n    const { algorithm } = key;\n    if (typeof algorithm.modulusLength !== 'number' || algorithm.modulusLength < 2048) {\n        throw new UnsupportedOperationError(`unsupported ${algorithm.name} modulusLength`, {\n            cause: key,\n        });\n    }\n}\nfunction ecdsaHashName(key) {\n    const { algorithm } = key;\n    switch (algorithm.namedCurve) {\n        case 'P-256':\n            return 'SHA-256';\n        case 'P-384':\n            return 'SHA-384';\n        case 'P-521':\n            return 'SHA-512';\n        default:\n            throw new UnsupportedOperationError('unsupported ECDSA namedCurve', { cause: key });\n    }\n}\nfunction keyToSubtle(key) {\n    switch (key.algorithm.name) {\n        case 'ECDSA':\n            return {\n                name: key.algorithm.name,\n                hash: ecdsaHashName(key),\n            };\n        case 'RSA-PSS': {\n            checkRsaKeyAlgorithm(key);\n            switch (key.algorithm.hash.name) {\n                case 'SHA-256':\n                case 'SHA-384':\n                case 'SHA-512':\n                    return {\n                        name: key.algorithm.name,\n                        saltLength: parseInt(key.algorithm.hash.name.slice(-3), 10) >> 3,\n                    };\n                default:\n                    throw new UnsupportedOperationError('unsupported RSA-PSS hash name', { cause: key });\n            }\n        }\n        case 'RSASSA-PKCS1-v1_5':\n            checkRsaKeyAlgorithm(key);\n            return key.algorithm.name;\n        case 'Ed25519':\n            return key.algorithm.name;\n    }\n    throw new UnsupportedOperationError('unsupported CryptoKey algorithm name', { cause: key });\n}\nasync function validateJwsSignature(protectedHeader, payload, key, signature) {\n    const data = buf(`${protectedHeader}.${payload}`);\n    const algorithm = keyToSubtle(key);\n    const verified = await crypto.subtle.verify(algorithm, key, signature, data);\n    if (!verified) {\n        throw OPE('JWT signature verification failed', INVALID_RESPONSE, {\n            key,\n            data,\n            signature,\n            algorithm,\n        });\n    }\n}\nasync function validateJwt(jws, checkAlg, clockSkew, clockTolerance, decryptJwt) {\n    let { 0: protectedHeader, 1: payload, length } = jws.split('.');\n    if (length === 5) {\n        if (decryptJwt !== undefined) {\n            jws = await decryptJwt(jws);\n            ({ 0: protectedHeader, 1: payload, length } = jws.split('.'));\n        }\n        else {\n            throw new UnsupportedOperationError('JWE decryption is not configured', { cause: jws });\n        }\n    }\n    if (length !== 3) {\n        throw OPE('Invalid JWT', INVALID_RESPONSE, jws);\n    }\n    let header;\n    try {\n        header = JSON.parse(buf(b64u(protectedHeader)));\n    }\n    catch (cause) {\n        throw OPE('failed to parse JWT Header body as base64url encoded JSON', PARSE_ERROR, cause);\n    }\n    if (!isJsonObject(header)) {\n        throw OPE('JWT Header must be a top level object', INVALID_RESPONSE, jws);\n    }\n    checkAlg(header);\n    if (header.crit !== undefined) {\n        throw new UnsupportedOperationError('no JWT \"crit\" header parameter extensions are supported', {\n            cause: { header },\n        });\n    }\n    let claims;\n    try {\n        claims = JSON.parse(buf(b64u(payload)));\n    }\n    catch (cause) {\n        throw OPE('failed to parse JWT Payload body as base64url encoded JSON', PARSE_ERROR, cause);\n    }\n    if (!isJsonObject(claims)) {\n        throw OPE('JWT Payload must be a top level object', INVALID_RESPONSE, jws);\n    }\n    const now = epochTime() + clockSkew;\n    if (claims.exp !== undefined) {\n        if (typeof claims.exp !== 'number') {\n            throw OPE('unexpected JWT \"exp\" (expiration time) claim type', INVALID_RESPONSE, { claims });\n        }\n        if (claims.exp <= now - clockTolerance) {\n            throw OPE('unexpected JWT \"exp\" (expiration time) claim value, expiration is past current timestamp', JWT_TIMESTAMP_CHECK, { claims, now, tolerance: clockTolerance, claim: 'exp' });\n        }\n    }\n    if (claims.iat !== undefined) {\n        if (typeof claims.iat !== 'number') {\n            throw OPE('unexpected JWT \"iat\" (issued at) claim type', INVALID_RESPONSE, { claims });\n        }\n    }\n    if (claims.iss !== undefined) {\n        if (typeof claims.iss !== 'string') {\n            throw OPE('unexpected JWT \"iss\" (issuer) claim type', INVALID_RESPONSE, { claims });\n        }\n    }\n    if (claims.nbf !== undefined) {\n        if (typeof claims.nbf !== 'number') {\n            throw OPE('unexpected JWT \"nbf\" (not before) claim type', INVALID_RESPONSE, { claims });\n        }\n        if (claims.nbf > now + clockTolerance) {\n            throw OPE('unexpected JWT \"nbf\" (not before) claim value', JWT_TIMESTAMP_CHECK, {\n                claims,\n                now,\n                tolerance: clockTolerance,\n                claim: 'nbf',\n            });\n        }\n    }\n    if (claims.aud !== undefined) {\n        if (typeof claims.aud !== 'string' && !Array.isArray(claims.aud)) {\n            throw OPE('unexpected JWT \"aud\" (audience) claim type', INVALID_RESPONSE, { claims });\n        }\n    }\n    return { header, claims, jwt: jws };\n}\nasync function validateJwtAuthResponse(as, client, parameters, expectedState, options) {\n    assertAs(as);\n    assertClient(client);\n    if (parameters instanceof URL) {\n        parameters = parameters.searchParams;\n    }\n    if (!(parameters instanceof URLSearchParams)) {\n        throw CodedTypeError('\"parameters\" must be an instance of URLSearchParams, or URL', ERR_INVALID_ARG_TYPE);\n    }\n    const response = getURLSearchParameter(parameters, 'response');\n    if (!response) {\n        throw OPE('\"parameters\" does not contain a JARM response', INVALID_RESPONSE);\n    }\n    const { claims, header, jwt } = await validateJwt(response, checkSigningAlgorithm.bind(undefined, client.authorization_signed_response_alg, as.authorization_signing_alg_values_supported, 'RS256'), getClockSkew(client), getClockTolerance(client), options?.[jweDecrypt])\n        .then(validatePresence.bind(undefined, ['aud', 'exp', 'iss']))\n        .then(validateIssuer.bind(undefined, as))\n        .then(validateAudience.bind(undefined, client.client_id));\n    const { 0: protectedHeader, 1: payload, 2: encodedSignature } = jwt.split('.');\n    const signature = b64u(encodedSignature);\n    const key = await getPublicSigKeyFromIssuerJwksUri(as, options, header);\n    await validateJwsSignature(protectedHeader, payload, key, signature);\n    const result = new URLSearchParams();\n    for (const [key, value] of Object.entries(claims)) {\n        if (typeof value === 'string' && key !== 'aud') {\n            result.set(key, value);\n        }\n    }\n    return validateAuthResponse(as, client, result, expectedState);\n}\nasync function idTokenHash(data, header, claimName) {\n    let algorithm;\n    switch (header.alg) {\n        case 'RS256':\n        case 'PS256':\n        case 'ES256':\n            algorithm = 'SHA-256';\n            break;\n        case 'RS384':\n        case 'PS384':\n        case 'ES384':\n            algorithm = 'SHA-384';\n            break;\n        case 'RS512':\n        case 'PS512':\n        case 'ES512':\n        case 'Ed25519':\n        case 'EdDSA':\n            algorithm = 'SHA-512';\n            break;\n        default:\n            throw new UnsupportedOperationError(`unsupported JWS algorithm for ${claimName} calculation`, { cause: { alg: header.alg } });\n    }\n    const digest = await crypto.subtle.digest(algorithm, buf(data));\n    return b64u(digest.slice(0, digest.byteLength / 2));\n}\nasync function idTokenHashMatches(data, actual, header, claimName) {\n    const expected = await idTokenHash(data, header, claimName);\n    return actual === expected;\n}\nasync function validateDetachedSignatureResponse(as, client, parameters, expectedNonce, expectedState, maxAge, options) {\n    return validateHybridResponse(as, client, parameters, expectedNonce, expectedState, maxAge, options, true);\n}\nasync function validateCodeIdTokenResponse(as, client, parameters, expectedNonce, expectedState, maxAge, options) {\n    return validateHybridResponse(as, client, parameters, expectedNonce, expectedState, maxAge, options, false);\n}\nasync function consumeStream(request) {\n    if (request.bodyUsed) {\n        throw CodedTypeError('form_post Request instances must contain a readable body', ERR_INVALID_ARG_VALUE, { cause: request });\n    }\n    return request.text();\n}\nasync function formPostResponse(request) {\n    if (request.method !== 'POST') {\n        throw CodedTypeError('form_post responses are expected to use the POST method', ERR_INVALID_ARG_VALUE, { cause: request });\n    }\n    if (getContentType(request) !== 'application/x-www-form-urlencoded') {\n        throw CodedTypeError('form_post responses are expected to use the application/x-www-form-urlencoded content-type', ERR_INVALID_ARG_VALUE, { cause: request });\n    }\n    return consumeStream(request);\n}\nasync function validateHybridResponse(as, client, parameters, expectedNonce, expectedState, maxAge, options, fapi) {\n    assertAs(as);\n    assertClient(client);\n    if (parameters instanceof URL) {\n        if (!parameters.hash.length) {\n            throw CodedTypeError('\"parameters\" as an instance of URL must contain a hash (fragment) with the Authorization Response parameters', ERR_INVALID_ARG_VALUE);\n        }\n        parameters = new URLSearchParams(parameters.hash.slice(1));\n    }\n    else if (looseInstanceOf(parameters, Request)) {\n        parameters = new URLSearchParams(await formPostResponse(parameters));\n    }\n    else if (parameters instanceof URLSearchParams) {\n        parameters = new URLSearchParams(parameters);\n    }\n    else {\n        throw CodedTypeError('\"parameters\" must be an instance of URLSearchParams, URL, or Response', ERR_INVALID_ARG_TYPE);\n    }\n    const id_token = getURLSearchParameter(parameters, 'id_token');\n    parameters.delete('id_token');\n    switch (expectedState) {\n        case undefined:\n        case expectNoState:\n            break;\n        default:\n            assertString(expectedState, '\"expectedState\" argument');\n    }\n    const result = validateAuthResponse({\n        ...as,\n        authorization_response_iss_parameter_supported: false,\n    }, client, parameters, expectedState);\n    if (!id_token) {\n        throw OPE('\"parameters\" does not contain an ID Token', INVALID_RESPONSE);\n    }\n    const code = getURLSearchParameter(parameters, 'code');\n    if (!code) {\n        throw OPE('\"parameters\" does not contain an Authorization Code', INVALID_RESPONSE);\n    }\n    const requiredClaims = [\n        'aud',\n        'exp',\n        'iat',\n        'iss',\n        'sub',\n        'nonce',\n        'c_hash',\n    ];\n    const state = parameters.get('state');\n    if (fapi && (typeof expectedState === 'string' || state !== null)) {\n        requiredClaims.push('s_hash');\n    }\n    if (maxAge !== undefined) {\n        assertNumber(maxAge, false, '\"maxAge\" argument');\n    }\n    else if (client.default_max_age !== undefined) {\n        assertNumber(client.default_max_age, false, '\"client.default_max_age\"');\n    }\n    maxAge ??= client.default_max_age ?? skipAuthTimeCheck;\n    if (client.require_auth_time || maxAge !== skipAuthTimeCheck) {\n        requiredClaims.push('auth_time');\n    }\n    const { claims, header, jwt } = await validateJwt(id_token, checkSigningAlgorithm.bind(undefined, client.id_token_signed_response_alg, as.id_token_signing_alg_values_supported, 'RS256'), getClockSkew(client), getClockTolerance(client), options?.[jweDecrypt])\n        .then(validatePresence.bind(undefined, requiredClaims))\n        .then(validateIssuer.bind(undefined, as))\n        .then(validateAudience.bind(undefined, client.client_id));\n    const clockSkew = getClockSkew(client);\n    const now = epochTime() + clockSkew;\n    if (claims.iat < now - 3600) {\n        throw OPE('unexpected JWT \"iat\" (issued at) claim value, it is too far in the past', JWT_TIMESTAMP_CHECK, { now, claims, claim: 'iat' });\n    }\n    assertString(claims.c_hash, 'ID Token \"c_hash\" (code hash) claim value', INVALID_RESPONSE, {\n        claims,\n    });\n    if (claims.auth_time !== undefined) {\n        assertNumber(claims.auth_time, false, 'ID Token \"auth_time\" (authentication time)', INVALID_RESPONSE, { claims });\n    }\n    if (maxAge !== skipAuthTimeCheck) {\n        const now = epochTime() + getClockSkew(client);\n        const tolerance = getClockTolerance(client);\n        if (claims.auth_time + maxAge < now - tolerance) {\n            throw OPE('too much time has elapsed since the last End-User authentication', JWT_TIMESTAMP_CHECK, { claims, now, tolerance, claim: 'auth_time' });\n        }\n    }\n    assertString(expectedNonce, '\"expectedNonce\" argument');\n    if (claims.nonce !== expectedNonce) {\n        throw OPE('unexpected ID Token \"nonce\" claim value', JWT_CLAIM_COMPARISON, {\n            expected: expectedNonce,\n            claims,\n            claim: 'nonce',\n        });\n    }\n    if (Array.isArray(claims.aud) && claims.aud.length !== 1) {\n        if (claims.azp === undefined) {\n            throw OPE('ID Token \"aud\" (audience) claim includes additional untrusted audiences', JWT_CLAIM_COMPARISON, { claims, claim: 'aud' });\n        }\n        if (claims.azp !== client.client_id) {\n            throw OPE('unexpected ID Token \"azp\" (authorized party) claim value', JWT_CLAIM_COMPARISON, {\n                expected: client.client_id,\n                claims,\n                claim: 'azp',\n            });\n        }\n    }\n    const { 0: protectedHeader, 1: payload, 2: encodedSignature } = jwt.split('.');\n    const signature = b64u(encodedSignature);\n    const key = await getPublicSigKeyFromIssuerJwksUri(as, options, header);\n    await validateJwsSignature(protectedHeader, payload, key, signature);\n    if ((await idTokenHashMatches(code, claims.c_hash, header, 'c_hash')) !== true) {\n        throw OPE('invalid ID Token \"c_hash\" (code hash) claim value', JWT_CLAIM_COMPARISON, {\n            code,\n            alg: header.alg,\n            claim: 'c_hash',\n            claims,\n        });\n    }\n    if ((fapi && state !== null) || claims.s_hash !== undefined) {\n        assertString(claims.s_hash, 'ID Token \"s_hash\" (state hash) claim value', INVALID_RESPONSE, {\n            claims,\n        });\n        assertString(state, '\"state\" response parameter', INVALID_RESPONSE, { parameters });\n        if ((await idTokenHashMatches(state, claims.s_hash, header, 's_hash')) !== true) {\n            throw OPE('invalid ID Token \"s_hash\" (state hash) claim value', JWT_CLAIM_COMPARISON, {\n                state,\n                alg: header.alg,\n                claim: 's_hash',\n                claims,\n            });\n        }\n    }\n    return result;\n}\nfunction checkSigningAlgorithm(client, issuer, fallback, header) {\n    if (client !== undefined) {\n        if (typeof client === 'string' ? header.alg !== client : !client.includes(header.alg)) {\n            throw OPE('unexpected JWT \"alg\" header parameter', INVALID_RESPONSE, {\n                header,\n                expected: client,\n                reason: 'client configuration',\n            });\n        }\n        return;\n    }\n    if (Array.isArray(issuer)) {\n        if (!issuer.includes(header.alg)) {\n            throw OPE('unexpected JWT \"alg\" header parameter', INVALID_RESPONSE, {\n                header,\n                expected: issuer,\n                reason: 'authorization server metadata',\n            });\n        }\n        return;\n    }\n    if (fallback !== undefined) {\n        if (typeof fallback === 'string'\n            ? header.alg !== fallback\n            : typeof fallback === 'function'\n                ? !fallback(header.alg)\n                : !fallback.includes(header.alg)) {\n            throw OPE('unexpected JWT \"alg\" header parameter', INVALID_RESPONSE, {\n                header,\n                expected: fallback,\n                reason: 'default value',\n            });\n        }\n        return;\n    }\n    throw OPE('missing client or server configuration to verify used JWT \"alg\" header parameter', undefined, { client, issuer, fallback });\n}\nfunction getURLSearchParameter(parameters, name) {\n    const { 0: value, length } = parameters.getAll(name);\n    if (length > 1) {\n        throw OPE(`\"${name}\" parameter must be provided only once`, INVALID_RESPONSE);\n    }\n    return value;\n}\nconst skipStateCheck = Symbol();\nconst expectNoState = Symbol();\nfunction validateAuthResponse(as, client, parameters, expectedState) {\n    assertAs(as);\n    assertClient(client);\n    if (parameters instanceof URL) {\n        parameters = parameters.searchParams;\n    }\n    if (!(parameters instanceof URLSearchParams)) {\n        throw CodedTypeError('\"parameters\" must be an instance of URLSearchParams, or URL', ERR_INVALID_ARG_TYPE);\n    }\n    if (getURLSearchParameter(parameters, 'response')) {\n        throw OPE('\"parameters\" contains a JARM response, use validateJwtAuthResponse() instead of validateAuthResponse()', INVALID_RESPONSE, { parameters });\n    }\n    const iss = getURLSearchParameter(parameters, 'iss');\n    const state = getURLSearchParameter(parameters, 'state');\n    if (!iss && as.authorization_response_iss_parameter_supported) {\n        throw OPE('response parameter \"iss\" (issuer) missing', INVALID_RESPONSE, { parameters });\n    }\n    if (iss && iss !== as.issuer) {\n        throw OPE('unexpected \"iss\" (issuer) response parameter value', INVALID_RESPONSE, {\n            expected: as.issuer,\n            parameters,\n        });\n    }\n    switch (expectedState) {\n        case undefined:\n        case expectNoState:\n            if (state !== undefined) {\n                throw OPE('unexpected \"state\" response parameter encountered', INVALID_RESPONSE, {\n                    expected: undefined,\n                    parameters,\n                });\n            }\n            break;\n        case skipStateCheck:\n            break;\n        default:\n            assertString(expectedState, '\"expectedState\" argument');\n            if (state !== expectedState) {\n                throw OPE(state === undefined\n                    ? 'response parameter \"state\" missing'\n                    : 'unexpected \"state\" response parameter value', INVALID_RESPONSE, { expected: expectedState, parameters });\n            }\n    }\n    const error = getURLSearchParameter(parameters, 'error');\n    if (error) {\n        throw new AuthorizationResponseError('authorization response from the server is an error', {\n            cause: parameters,\n        });\n    }\n    const id_token = getURLSearchParameter(parameters, 'id_token');\n    const token = getURLSearchParameter(parameters, 'token');\n    if (id_token !== undefined || token !== undefined) {\n        throw new UnsupportedOperationError('implicit and hybrid flows are not supported');\n    }\n    return brand(new URLSearchParams(parameters));\n}\nfunction algToSubtle(alg) {\n    switch (alg) {\n        case 'PS256':\n        case 'PS384':\n        case 'PS512':\n            return { name: 'RSA-PSS', hash: `SHA-${alg.slice(-3)}` };\n        case 'RS256':\n        case 'RS384':\n        case 'RS512':\n            return { name: 'RSASSA-PKCS1-v1_5', hash: `SHA-${alg.slice(-3)}` };\n        case 'ES256':\n        case 'ES384':\n            return { name: 'ECDSA', namedCurve: `P-${alg.slice(-3)}` };\n        case 'ES512':\n            return { name: 'ECDSA', namedCurve: 'P-521' };\n        case 'Ed25519':\n        case 'EdDSA':\n            return 'Ed25519';\n        default:\n            throw new UnsupportedOperationError('unsupported JWS algorithm', { cause: { alg } });\n    }\n}\nasync function importJwk(alg, jwk) {\n    const { ext, key_ops, use, ...key } = jwk;\n    return crypto.subtle.importKey('jwk', key, algToSubtle(alg), true, ['verify']);\n}\nasync function deviceAuthorizationRequest(as, client, clientAuthentication, parameters, options) {\n    assertAs(as);\n    assertClient(client);\n    const url = resolveEndpoint(as, 'device_authorization_endpoint', client.use_mtls_endpoint_aliases, options?.[allowInsecureRequests] !== true);\n    const body = new URLSearchParams(parameters);\n    body.set('client_id', client.client_id);\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    return authenticatedRequest(as, client, clientAuthentication, url, body, headers, options);\n}\nasync function processDeviceAuthorizationResponse(as, client, response) {\n    assertAs(as);\n    assertClient(client);\n    if (!looseInstanceOf(response, Response)) {\n        throw CodedTypeError('\"response\" must be an instance of Response', ERR_INVALID_ARG_TYPE);\n    }\n    checkAuthenticationChallenges(response);\n    await checkOAuthBodyError(response, 200, 'Device Authorization Endpoint');\n    assertReadableResponse(response);\n    const json = await getResponseJsonBody(response);\n    assertString(json.device_code, '\"response\" body \"device_code\" property', INVALID_RESPONSE, {\n        body: json,\n    });\n    assertString(json.user_code, '\"response\" body \"user_code\" property', INVALID_RESPONSE, {\n        body: json,\n    });\n    assertString(json.verification_uri, '\"response\" body \"verification_uri\" property', INVALID_RESPONSE, { body: json });\n    let expiresIn = typeof json.expires_in !== 'number' ? parseFloat(json.expires_in) : json.expires_in;\n    assertNumber(expiresIn, false, '\"response\" body \"expires_in\" property', INVALID_RESPONSE, {\n        body: json,\n    });\n    json.expires_in = expiresIn;\n    if (json.verification_uri_complete !== undefined) {\n        assertString(json.verification_uri_complete, '\"response\" body \"verification_uri_complete\" property', INVALID_RESPONSE, { body: json });\n    }\n    if (json.interval !== undefined) {\n        assertNumber(json.interval, false, '\"response\" body \"interval\" property', INVALID_RESPONSE, {\n            body: json,\n        });\n    }\n    return json;\n}\nasync function deviceCodeGrantRequest(as, client, clientAuthentication, deviceCode, options) {\n    assertAs(as);\n    assertClient(client);\n    assertString(deviceCode, '\"deviceCode\"');\n    const parameters = new URLSearchParams(options?.additionalParameters);\n    parameters.set('device_code', deviceCode);\n    return tokenEndpointRequest(as, client, clientAuthentication, 'urn:ietf:params:oauth:grant-type:device_code', parameters, options);\n}\nasync function processDeviceCodeResponse(as, client, response, options) {\n    return processGenericAccessTokenResponse(as, client, response, undefined, options);\n}\nasync function generateKeyPair(alg, options) {\n    assertString(alg, '\"alg\"');\n    const algorithm = algToSubtle(alg);\n    if (alg.startsWith('PS') || alg.startsWith('RS')) {\n        Object.assign(algorithm, {\n            modulusLength: options?.modulusLength ?? 2048,\n            publicExponent: new Uint8Array([0x01, 0x00, 0x01]),\n        });\n    }\n    return crypto.subtle.generateKey(algorithm, options?.extractable ?? false, [\n        'sign',\n        'verify',\n    ]);\n}\nfunction normalizeHtu(htu) {\n    const url = new URL(htu);\n    url.search = '';\n    url.hash = '';\n    return url.href;\n}\nasync function validateDPoP(request, accessToken, accessTokenClaims, options) {\n    const headerValue = request.headers.get('dpop');\n    if (headerValue === null) {\n        throw OPE('operation indicated DPoP use but the request has no DPoP HTTP Header', INVALID_REQUEST, { headers: request.headers });\n    }\n    if (request.headers.get('authorization')?.toLowerCase().startsWith('dpop ') === false) {\n        throw OPE(`operation indicated DPoP use but the request's Authorization HTTP Header scheme is not DPoP`, INVALID_REQUEST, { headers: request.headers });\n    }\n    if (typeof accessTokenClaims.cnf?.jkt !== 'string') {\n        throw OPE('operation indicated DPoP use but the JWT Access Token has no jkt confirmation claim', INVALID_REQUEST, { claims: accessTokenClaims });\n    }\n    const clockSkew = getClockSkew(options);\n    const proof = await validateJwt(headerValue, checkSigningAlgorithm.bind(undefined, options?.signingAlgorithms, undefined, supported), clockSkew, getClockTolerance(options), undefined)\n        .then(checkJwtType.bind(undefined, 'dpop+jwt'))\n        .then(validatePresence.bind(undefined, ['iat', 'jti', 'ath', 'htm', 'htu']));\n    const now = epochTime() + clockSkew;\n    const diff = Math.abs(now - proof.claims.iat);\n    if (diff > 300) {\n        throw OPE('DPoP Proof iat is not recent enough', JWT_TIMESTAMP_CHECK, {\n            now,\n            claims: proof.claims,\n            claim: 'iat',\n        });\n    }\n    if (proof.claims.htm !== request.method) {\n        throw OPE('DPoP Proof htm mismatch', JWT_CLAIM_COMPARISON, {\n            expected: request.method,\n            claims: proof.claims,\n            claim: 'htm',\n        });\n    }\n    if (typeof proof.claims.htu !== 'string' ||\n        normalizeHtu(proof.claims.htu) !== normalizeHtu(request.url)) {\n        throw OPE('DPoP Proof htu mismatch', JWT_CLAIM_COMPARISON, {\n            expected: normalizeHtu(request.url),\n            claims: proof.claims,\n            claim: 'htu',\n        });\n    }\n    {\n        const expected = b64u(await crypto.subtle.digest('SHA-256', buf(accessToken)));\n        if (proof.claims.ath !== expected) {\n            throw OPE('DPoP Proof ath mismatch', JWT_CLAIM_COMPARISON, {\n                expected,\n                claims: proof.claims,\n                claim: 'ath',\n            });\n        }\n    }\n    {\n        let components;\n        switch (proof.header.jwk.kty) {\n            case 'EC':\n                components = {\n                    crv: proof.header.jwk.crv,\n                    kty: proof.header.jwk.kty,\n                    x: proof.header.jwk.x,\n                    y: proof.header.jwk.y,\n                };\n                break;\n            case 'OKP':\n                components = {\n                    crv: proof.header.jwk.crv,\n                    kty: proof.header.jwk.kty,\n                    x: proof.header.jwk.x,\n                };\n                break;\n            case 'RSA':\n                components = {\n                    e: proof.header.jwk.e,\n                    kty: proof.header.jwk.kty,\n                    n: proof.header.jwk.n,\n                };\n                break;\n            default:\n                throw new UnsupportedOperationError('unsupported JWK key type', { cause: proof.header.jwk });\n        }\n        const expected = b64u(await crypto.subtle.digest('SHA-256', buf(JSON.stringify(components))));\n        if (accessTokenClaims.cnf.jkt !== expected) {\n            throw OPE('JWT Access Token confirmation mismatch', JWT_CLAIM_COMPARISON, {\n                expected,\n                claims: accessTokenClaims,\n                claim: 'cnf.jkt',\n            });\n        }\n    }\n    const { 0: protectedHeader, 1: payload, 2: encodedSignature } = headerValue.split('.');\n    const signature = b64u(encodedSignature);\n    const { jwk, alg } = proof.header;\n    if (!jwk) {\n        throw OPE('DPoP Proof is missing the jwk header parameter', INVALID_REQUEST, {\n            header: proof.header,\n        });\n    }\n    const key = await importJwk(alg, jwk);\n    if (key.type !== 'public') {\n        throw OPE('DPoP Proof jwk header parameter must contain a public key', INVALID_REQUEST, {\n            header: proof.header,\n        });\n    }\n    await validateJwsSignature(protectedHeader, payload, key, signature);\n}\nasync function validateJwtAccessToken(as, request, expectedAudience, options) {\n    assertAs(as);\n    if (!looseInstanceOf(request, Request)) {\n        throw CodedTypeError('\"request\" must be an instance of Request', ERR_INVALID_ARG_TYPE);\n    }\n    assertString(expectedAudience, '\"expectedAudience\"');\n    const authorization = request.headers.get('authorization');\n    if (authorization === null) {\n        throw OPE('\"request\" is missing an Authorization HTTP Header', INVALID_REQUEST, {\n            headers: request.headers,\n        });\n    }\n    let { 0: scheme, 1: accessToken, length } = authorization.split(' ');\n    scheme = scheme.toLowerCase();\n    switch (scheme) {\n        case 'dpop':\n        case 'bearer':\n            break;\n        default:\n            throw new UnsupportedOperationError('unsupported Authorization HTTP Header scheme', {\n                cause: { headers: request.headers },\n            });\n    }\n    if (length !== 2) {\n        throw OPE('invalid Authorization HTTP Header format', INVALID_REQUEST, {\n            headers: request.headers,\n        });\n    }\n    const requiredClaims = [\n        'iss',\n        'exp',\n        'aud',\n        'sub',\n        'iat',\n        'jti',\n        'client_id',\n    ];\n    if (options?.requireDPoP || scheme === 'dpop' || request.headers.has('dpop')) {\n        requiredClaims.push('cnf');\n    }\n    const { claims, header } = await validateJwt(accessToken, checkSigningAlgorithm.bind(undefined, options?.signingAlgorithms, undefined, supported), getClockSkew(options), getClockTolerance(options), undefined)\n        .then(checkJwtType.bind(undefined, 'at+jwt'))\n        .then(validatePresence.bind(undefined, requiredClaims))\n        .then(validateIssuer.bind(undefined, as))\n        .then(validateAudience.bind(undefined, expectedAudience))\n        .catch(reassignRSCode);\n    for (const claim of ['client_id', 'jti', 'sub']) {\n        if (typeof claims[claim] !== 'string') {\n            throw OPE(`unexpected JWT \"${claim}\" claim type`, INVALID_REQUEST, { claims });\n        }\n    }\n    if ('cnf' in claims) {\n        if (!isJsonObject(claims.cnf)) {\n            throw OPE('unexpected JWT \"cnf\" (confirmation) claim value', INVALID_REQUEST, { claims });\n        }\n        const { 0: cnf, length } = Object.keys(claims.cnf);\n        if (length) {\n            if (length !== 1) {\n                throw new UnsupportedOperationError('multiple confirmation claims are not supported', {\n                    cause: { claims },\n                });\n            }\n            if (cnf !== 'jkt') {\n                throw new UnsupportedOperationError('unsupported JWT Confirmation method', {\n                    cause: { claims },\n                });\n            }\n        }\n    }\n    const { 0: protectedHeader, 1: payload, 2: encodedSignature } = accessToken.split('.');\n    const signature = b64u(encodedSignature);\n    const key = await getPublicSigKeyFromIssuerJwksUri(as, options, header);\n    await validateJwsSignature(protectedHeader, payload, key, signature);\n    if (options?.requireDPoP ||\n        scheme === 'dpop' ||\n        claims.cnf?.jkt !== undefined ||\n        request.headers.has('dpop')) {\n        await validateDPoP(request, accessToken, claims, options).catch(reassignRSCode);\n    }\n    return claims;\n}\nfunction reassignRSCode(err) {\n    if (err instanceof OperationProcessingError && err?.code === INVALID_REQUEST) {\n        err.code = INVALID_RESPONSE;\n    }\n    throw err;\n}\nasync function backchannelAuthenticationRequest(as, client, clientAuthentication, parameters, options) {\n    assertAs(as);\n    assertClient(client);\n    const url = resolveEndpoint(as, 'backchannel_authentication_endpoint', client.use_mtls_endpoint_aliases, options?.[allowInsecureRequests] !== true);\n    const body = new URLSearchParams(parameters);\n    body.set('client_id', client.client_id);\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    return authenticatedRequest(as, client, clientAuthentication, url, body, headers, options);\n}\nasync function processBackchannelAuthenticationResponse(as, client, response) {\n    assertAs(as);\n    assertClient(client);\n    if (!looseInstanceOf(response, Response)) {\n        throw CodedTypeError('\"response\" must be an instance of Response', ERR_INVALID_ARG_TYPE);\n    }\n    checkAuthenticationChallenges(response);\n    await checkOAuthBodyError(response, 200, 'Backchannel Authentication Endpoint');\n    assertReadableResponse(response);\n    const json = await getResponseJsonBody(response);\n    assertString(json.auth_req_id, '\"response\" body \"auth_req_id\" property', INVALID_RESPONSE, {\n        body: json,\n    });\n    let expiresIn = typeof json.expires_in !== 'number' ? parseFloat(json.expires_in) : json.expires_in;\n    assertNumber(expiresIn, false, '\"response\" body \"expires_in\" property', INVALID_RESPONSE, {\n        body: json,\n    });\n    json.expires_in = expiresIn;\n    if (json.interval !== undefined) {\n        assertNumber(json.interval, false, '\"response\" body \"interval\" property', INVALID_RESPONSE, {\n            body: json,\n        });\n    }\n    return json;\n}\nasync function backchannelAuthenticationGrantRequest(as, client, clientAuthentication, authReqId, options) {\n    assertAs(as);\n    assertClient(client);\n    assertString(authReqId, '\"authReqId\"');\n    const parameters = new URLSearchParams(options?.additionalParameters);\n    parameters.set('auth_req_id', authReqId);\n    return tokenEndpointRequest(as, client, clientAuthentication, 'urn:openid:params:grant-type:ciba', parameters, options);\n}\nasync function processBackchannelAuthenticationGrantResponse(as, client, response, options) {\n    return processGenericAccessTokenResponse(as, client, response, undefined, options);\n}\nasync function dynamicClientRegistrationRequest(as, metadata, options) {\n    assertAs(as);\n    const url = resolveEndpoint(as, 'registration_endpoint', metadata.use_mtls_endpoint_aliases, options?.[allowInsecureRequests] !== true);\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    headers.set('content-type', 'application/json');\n    const method = 'POST';\n    if (options?.DPoP) {\n        assertDPoP(options.DPoP);\n        await options.DPoP.addProof(url, headers, method, options.initialAccessToken);\n    }\n    if (options?.initialAccessToken) {\n        headers.set('authorization', `${headers.has('dpop') ? 'DPoP' : 'Bearer'} ${options.initialAccessToken}`);\n    }\n    const response = await (options?.[customFetch] || fetch)(url.href, {\n        body: JSON.stringify(metadata),\n        headers: Object.fromEntries(headers.entries()),\n        method,\n        redirect: 'manual',\n        signal: options?.signal ? signal(options.signal) : undefined,\n    });\n    options?.DPoP?.cacheNonce(response);\n    return response;\n}\nasync function processDynamicClientRegistrationResponse(response) {\n    if (!looseInstanceOf(response, Response)) {\n        throw CodedTypeError('\"response\" must be an instance of Response', ERR_INVALID_ARG_TYPE);\n    }\n    checkAuthenticationChallenges(response);\n    await checkOAuthBodyError(response, 201, 'Dynamic Client Registration Endpoint');\n    assertReadableResponse(response);\n    const json = await getResponseJsonBody(response);\n    assertString(json.client_id, '\"response\" body \"client_id\" property', INVALID_RESPONSE, {\n        body: json,\n    });\n    if (json.client_secret !== undefined) {\n        assertString(json.client_secret, '\"response\" body \"client_secret\" property', INVALID_RESPONSE, {\n            body: json,\n        });\n    }\n    if (json.client_secret) {\n        assertNumber(json.client_secret_expires_at, true, '\"response\" body \"client_secret_expires_at\" property', INVALID_RESPONSE, {\n            body: json,\n        });\n    }\n    return json;\n}\nasync function resourceDiscoveryRequest(resourceIdentifier, options) {\n    return performDiscovery(resourceIdentifier, 'resourceIdentifier', (url) => {\n        prependWellKnown(url, '.well-known/oauth-protected-resource');\n        return url;\n    }, options);\n}\nasync function processResourceDiscoveryResponse(expectedResourceIdentifier, response) {\n    const expected = expectedResourceIdentifier;\n    if (!(expected instanceof URL) && expected !== _nodiscoverycheck) {\n        throw CodedTypeError('\"expectedResourceIdentifier\" must be an instance of URL', ERR_INVALID_ARG_TYPE);\n    }\n    if (!looseInstanceOf(response, Response)) {\n        throw CodedTypeError('\"response\" must be an instance of Response', ERR_INVALID_ARG_TYPE);\n    }\n    if (response.status !== 200) {\n        throw OPE('\"response\" is not a conform Resource Server Metadata response (unexpected HTTP status code)', RESPONSE_IS_NOT_CONFORM, response);\n    }\n    assertReadableResponse(response);\n    const json = await getResponseJsonBody(response);\n    assertString(json.resource, '\"response\" body \"resource\" property', INVALID_RESPONSE, {\n        body: json,\n    });\n    if (expected !== _nodiscoverycheck && new URL(json.resource).href !== expected.href) {\n        throw OPE('\"response\" body \"resource\" property does not match the expected value', JSON_ATTRIBUTE_COMPARISON, { expected: expected.href, body: json, attribute: 'resource' });\n    }\n    return json;\n}\nasync function getResponseJsonBody(response, check = assertApplicationJson) {\n    let json;\n    try {\n        json = await response.json();\n    }\n    catch (cause) {\n        check(response);\n        throw OPE('failed to parse \"response\" body as JSON', PARSE_ERROR, cause);\n    }\n    if (!isJsonObject(json)) {\n        throw OPE('\"response\" body must be a top level object', INVALID_RESPONSE, { body: json });\n    }\n    return json;\n}\nconst _nopkce = nopkce;\nconst _nodiscoverycheck = Symbol();\nconst _expectedIssuer = Symbol();\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/oauth4webapi/build/index.js\n");

/***/ })

};
;