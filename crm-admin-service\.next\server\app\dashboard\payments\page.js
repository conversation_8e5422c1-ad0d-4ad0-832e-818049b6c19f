(()=>{var e={};e.id=889,e.ids=[889],e.modules={777:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Innovative Centre\\\\crm-admin-service\\\\src\\\\app\\\\dashboard\\\\payments\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-admin-service\\src\\app\\dashboard\\payments\\page.tsx","default")},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1676:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>A});var t=s(687),a=s(3210),n=s(9208),i=s(2688);let d=(0,i.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),l=(0,i.A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]),o=(0,i.A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);var c=s(3928);let m=(0,i.A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]),u=(0,i.A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);var x=s(3861),h=s(9523),p=s(9667),v=s(4493),g=s(1391),b=s(4224),f=s(4780);let j=(0,b.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function y({className:e,variant:r,asChild:s=!1,...a}){let n=s?g.DX:"span";return(0,t.jsx)(n,{"data-slot":"badge",className:(0,f.cn)(j({variant:r}),e),...a})}function N({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,t.jsx)("table",{"data-slot":"table",className:(0,f.cn)("w-full caption-bottom text-sm",e),...r})})}function w({className:e,...r}){return(0,t.jsx)("thead",{"data-slot":"table-header",className:(0,f.cn)("[&_tr]:border-b",e),...r})}function k({className:e,...r}){return(0,t.jsx)("tbody",{"data-slot":"table-body",className:(0,f.cn)("[&_tr:last-child]:border-0",e),...r})}function P({className:e,...r}){return(0,t.jsx)("tr",{"data-slot":"table-row",className:(0,f.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...r})}function C({className:e,...r}){return(0,t.jsx)("th",{"data-slot":"table-head",className:(0,f.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...r})}function _({className:e,...r}){return(0,t.jsx)("td",{"data-slot":"table-cell",className:(0,f.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...r})}function A(){let{data:e}=(0,n.wV)(),[r,s]=(0,a.useState)([]),[i,g]=(0,a.useState)(!0),[b,f]=(0,a.useState)(""),[j,A]=(0,a.useState)(""),[S,D]=(0,a.useState)(1),[z,M]=(0,a.useState)(1);(0,a.useCallback)(async()=>{try{g(!0);let e=new URLSearchParams({page:S.toString(),limit:"10"});j&&e.append("status",j);let r=await fetch(`/api/payments?${e}`),t=await r.json();t.success&&(s(t.data.payments),M(t.data.pagination.pages))}catch(e){console.error("Error fetching payments:",e)}finally{g(!1)}},[S,j]);let R=e=>{switch(e){case"verified":return(0,t.jsxs)(y,{className:"bg-green-100 text-green-800",children:[(0,t.jsx)(d,{className:"w-3 h-3 mr-1"}),"Verified"]});case"disputed":return(0,t.jsxs)(y,{className:"bg-red-100 text-red-800",children:[(0,t.jsx)(l,{className:"w-3 h-3 mr-1"}),"Disputed"]});case"recorded":return(0,t.jsxs)(y,{className:"bg-yellow-100 text-yellow-800",children:[(0,t.jsx)(o,{className:"w-3 h-3 mr-1"}),"Recorded"]});default:return(0,t.jsx)(y,{variant:"secondary",children:e})}},q=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e),I=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"});return e?(0,t.jsxs)("div",{className:"p-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Payment Management"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Record and track payment information"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,t.jsx)(v.Zp,{children:(0,t.jsx)(v.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(c.A,{className:"h-8 w-8 text-green-600"}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Revenue"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"$0"})]})]})})}),(0,t.jsx)(v.Zp,{children:(0,t.jsx)(v.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(d,{className:"h-8 w-8 text-green-600"}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Verified"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"0"})]})]})})}),(0,t.jsx)(v.Zp,{children:(0,t.jsx)(v.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(o,{className:"h-8 w-8 text-yellow-600"}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Pending"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"0"})]})]})})}),(0,t.jsx)(v.Zp,{children:(0,t.jsx)(v.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(l,{className:"h-8 w-8 text-red-600"}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Disputed"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"0"})]})]})})})]}),(0,t.jsxs)(v.Zp,{className:"mb-6",children:[(0,t.jsxs)(v.aR,{children:[(0,t.jsx)(v.ZB,{children:"Payment Records"}),(0,t.jsx)(v.BT,{children:"Manage and track all payment records"})]}),(0,t.jsxs)(v.Wu,{children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 mb-6",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(m,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,t.jsx)(p.p,{placeholder:"Search payments...",value:b,onChange:e=>f(e.target.value),className:"pl-10"})]})}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)("select",{value:j,onChange:e=>A(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)("option",{value:"",children:"All Status"}),(0,t.jsx)("option",{value:"recorded",children:"Recorded"}),(0,t.jsx)("option",{value:"verified",children:"Verified"}),(0,t.jsx)("option",{value:"disputed",children:"Disputed"})]}),(0,t.jsxs)(h.$,{children:[(0,t.jsx)(u,{className:"h-4 w-4 mr-2"}),"New Payment"]})]})]}),(0,t.jsx)("div",{className:"border rounded-lg",children:(0,t.jsxs)(N,{children:[(0,t.jsx)(w,{children:(0,t.jsxs)(P,{children:[(0,t.jsx)(C,{children:"Student ID"}),(0,t.jsx)(C,{children:"Amount"}),(0,t.jsx)(C,{children:"Date"}),(0,t.jsx)(C,{children:"Method"}),(0,t.jsx)(C,{children:"Status"}),(0,t.jsx)(C,{children:"Recorded By"}),(0,t.jsx)(C,{children:"Actions"})]})}),(0,t.jsx)(k,{children:i?(0,t.jsx)(P,{children:(0,t.jsxs)(_,{colSpan:7,className:"text-center py-8",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,t.jsx)("p",{className:"mt-2 text-gray-600",children:"Loading payments..."})]})}):0===r.length?(0,t.jsx)(P,{children:(0,t.jsx)(_,{colSpan:7,className:"text-center py-8",children:(0,t.jsx)("p",{className:"text-gray-600",children:"No payment records found"})})}):r.map(e=>(0,t.jsxs)(P,{children:[(0,t.jsx)(_,{className:"font-medium",children:e.studentId}),(0,t.jsx)(_,{children:q(e.amount)}),(0,t.jsx)(_,{children:I(e.paymentDate)}),(0,t.jsx)(_,{className:"capitalize",children:e.paymentMethod.replace("_"," ")}),(0,t.jsx)(_,{children:R(e.status)}),(0,t.jsxs)(_,{children:[e.recordedByUser.firstName," ",e.recordedByUser.lastName]}),(0,t.jsx)(_,{children:(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(h.$,{variant:"outline",size:"sm",children:(0,t.jsx)(x.A,{className:"h-4 w-4"})}),"recorded"===e.status&&(0,t.jsx)(h.$,{variant:"outline",size:"sm",children:"Verify"})]})})]},e.id))})]})}),z>1&&(0,t.jsx)("div",{className:"flex justify-center mt-6",children:(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(h.$,{variant:"outline",onClick:()=>D(e=>Math.max(e-1,1)),disabled:1===S,children:"Previous"}),(0,t.jsxs)("span",{className:"px-4 py-2 text-sm text-gray-600",children:["Page ",S," of ",z]}),(0,t.jsx)(h.$,{variant:"outline",onClick:()=>D(e=>Math.min(e+1,z)),disabled:S===z,children:"Next"})]})})]})]})]}):(0,t.jsx)("div",{children:"Please sign in to access this page."})}},1777:(e,r,s)=>{Promise.resolve().then(s.t.bind(s,6444,23)),Promise.resolve().then(s.t.bind(s,6042,23)),Promise.resolve().then(s.t.bind(s,8170,23)),Promise.resolve().then(s.t.bind(s,9477,23)),Promise.resolve().then(s.t.bind(s,9345,23)),Promise.resolve().then(s.t.bind(s,2089,23)),Promise.resolve().then(s.t.bind(s,6577,23)),Promise.resolve().then(s.t.bind(s,1307,23))},2306:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>o});var t=s(5239),a=s(8088),n=s(8170),i=s.n(n),d=s(893),l={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>d[e]);s.d(r,l);let o={children:["",{children:["dashboard",{children:["payments",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,777)),"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-admin-service\\src\\app\\dashboard\\payments\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-admin-service\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-admin-service\\src\\app\\dashboard\\payments\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/payments/page",pathname:"/dashboard/payments",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3535:(e,r,s)=>{Promise.resolve().then(s.bind(s,2175))},3783:(e,r,s)=>{Promise.resolve().then(s.bind(s,9208))},3861:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(2688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3873:e=>{"use strict";e.exports=require("path")},3928:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(2688).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},4431:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>i,metadata:()=>n});var t=s(7413),a=s(2175);s(5692);let n={title:"Admin Portal - Innovative Centre CRM",description:"Enhanced security admin portal for financial data management"};function i({children:e}){return(0,t.jsx)("html",{lang:"en",children:(0,t.jsx)("body",{className:"min-h-screen bg-gray-50",children:(0,t.jsx)(a.SessionProvider,{children:(0,t.jsx)("div",{className:"flex min-h-screen",children:(0,t.jsx)("main",{className:"flex-1",children:e})})})})})}},4493:(e,r,s)=>{"use strict";s.d(r,{BT:()=>l,Wu:()=>o,ZB:()=>d,Zp:()=>n,aR:()=>i});var t=s(687);s(3210);var a=s(4780);function n({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...r})}function i({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...r})}function d({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...r})}function l({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...r})}function o({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...r})}},4780:(e,r,s)=>{"use strict";s.d(r,{cn:()=>n});var t=s(9384),a=s(2348);function n(...e){return(0,a.QP)((0,t.$)(e))}},5607:(e,r,s)=>{Promise.resolve().then(s.bind(s,1676))},5692:()=>{},8225:(e,r,s)=>{Promise.resolve().then(s.t.bind(s,6346,23)),Promise.resolve().then(s.t.bind(s,7924,23)),Promise.resolve().then(s.t.bind(s,5656,23)),Promise.resolve().then(s.t.bind(s,99,23)),Promise.resolve().then(s.t.bind(s,8243,23)),Promise.resolve().then(s.t.bind(s,8827,23)),Promise.resolve().then(s.t.bind(s,2763,23)),Promise.resolve().then(s.t.bind(s,7173,23))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9523:(e,r,s)=>{"use strict";s.d(r,{$:()=>l});var t=s(687);s(3210);var a=s(1391),n=s(4224),i=s(4780);let d=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:r,size:s,asChild:n=!1,...l}){let o=n?a.DX:"button";return(0,t.jsx)(o,{"data-slot":"button",className:(0,i.cn)(d({variant:r,size:s,className:e})),...l})}},9667:(e,r,s)=>{"use strict";s.d(r,{p:()=>i});var t=s(687),a=s(3210),n=s(4780);let i=a.forwardRef(({className:e,type:r,...s},a)=>(0,t.jsx)("input",{type:r,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...s}));i.displayName="Input"},9740:(e,r,s)=>{Promise.resolve().then(s.bind(s,777))}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[447,542,108],()=>s(2306));module.exports=t})();