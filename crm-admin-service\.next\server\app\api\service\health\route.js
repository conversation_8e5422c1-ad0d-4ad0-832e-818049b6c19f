"use strict";(()=>{var e={};e.id=232,e.ids=[232],e.modules={846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6943:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>l,routeModule:()=>u,serverHooks:()=>v,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>c});var s={};t.r(s),t.d(s,{GET:()=>p});var a=t(6559),n=t(8088),o=t(7719),i=t(8731);let p=(0,i.A0)(async e=>(0,i._q)()),u=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/service/health/route",pathname:"/api/service/health",filename:"route",bundlePath:"app/api/service/health/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-admin-service\\src\\app\\api\\service\\health\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:c,serverHooks:v}=u;function l(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:c})}},9294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,199,399],()=>t(6943));module.exports=s})();