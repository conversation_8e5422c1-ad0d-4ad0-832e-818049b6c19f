
export declare const VERSION: string;
export declare const PACKAGE_NAME: string;

export interface ServiceAuthConfig {
  serviceName: string;
  apiKey: string;
  allowedServices: string[];
}

export declare class ServiceClient {
  constructor(baseUrl: string, serviceName: string, apiKey: string);
  get<T>(endpoint: string): Promise<T>;
  post<T>(endpoint: string, data: any): Promise<T>;
  put<T>(endpoint: string, data: any): Promise<T>;
  delete<T>(endpoint: string): Promise<T>;
}

export declare class AdminServiceClient extends ServiceClient {
  getPayments(page?: number, limit?: number): Promise<any>;
  createPayment(data: any): Promise<any>;
  createUser(userData: any): Promise<any>;
}

export declare class StaffServiceClient extends ServiceClient {
  getLeads(status?: string, page?: number, limit?: number): Promise<any>;
  getCourses(page?: number, limit?: number): Promise<any>;
  getCourse(courseId: string): Promise<any>;
  getGroups(courseId?: string): Promise<any>;
  getAssignments(studentId?: string): Promise<any>;
  getResources(courseId?: string, groupId?: string): Promise<any>;
}

export declare class StudentServiceClient extends ServiceClient {
  getStudent(studentId: string): Promise<any>;
  createStudent(studentData: any): Promise<any>;
  getProgress(studentId: string): Promise<any>;
  updateProgress(progressData: any): Promise<any>;
  syncSchedule(studentId: string): Promise<any>;
}

export declare class ServiceClientFactory {
  constructor(config: {
    adminServiceUrl: string;
    staffServiceUrl: string;
    studentServiceUrl: string;
    serviceName: string;
    apiKey: string;
  });
  getAdminClient(): AdminServiceClient;
  getStaffClient(): StaffServiceClient;
  getStudentClient(): StudentServiceClient;
}

export declare function createServiceAuthMiddleware(config: ServiceAuthConfig): (request: any) => any;
