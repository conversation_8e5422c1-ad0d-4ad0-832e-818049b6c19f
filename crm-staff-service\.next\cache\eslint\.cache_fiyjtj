[{"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\health\\route.ts": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\leads\\route.ts": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\layout.tsx": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\page.tsx": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\lib\\db.ts": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\leads\\[id]\\convert\\route.ts": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\service\\assignments\\route.ts": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\service\\health\\route.ts": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\lib\\service-auth.ts": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\lib\\service-clients.ts": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\announcements\\route.ts": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\assignments\\route.ts": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\assignments\\[id]\\grade\\route.ts": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\assignments\\[id]\\submissions\\route.ts": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\auth\\[...nextauth]\\route.ts": "15", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\cabinets\\route.ts": "16", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\courses\\route.ts": "17", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\groups\\route.ts": "18", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\messages\\route.ts": "19", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\messages\\[threadId]\\route.ts": "20", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\notifications\\route.ts": "21", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\resources\\route.ts": "22", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\teachers\\route.ts": "23", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\teachers\\[id]\\kpis\\route.ts": "24", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\webhooks\\receive\\route.ts": "25", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\webhooks\\route.ts": "26", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\auth\\signin\\page.tsx": "27", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\dashboard\\assignments\\page.tsx": "28", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\dashboard\\courses\\page.tsx": "29", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\dashboard\\page.tsx": "30", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\components\\dashboard\\MessagingWidget.tsx": "31", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\components\\dashboard\\NotificationCenter.tsx": "32", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\components\\layout\\ResponsiveDashboard.tsx": "33", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\components\\ui\\alert.tsx": "34", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\components\\ui\\badge.tsx": "35", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\components\\ui\\button.tsx": "36", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\components\\ui\\card.tsx": "37", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\components\\ui\\input.tsx": "38", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\components\\ui\\label.tsx": "39", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\lib\\auth.ts": "40", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\lib\\utils.ts": "41", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\__tests__\\api\\leads.test.ts": "42"}, {"size": 642, "mtime": 1750450381543, "results": "43", "hashOfConfig": "44"}, {"size": 4785, "mtime": 1750488362115, "results": "45", "hashOfConfig": "44"}, {"size": 2929, "mtime": 1750489588195, "results": "46", "hashOfConfig": "44"}, {"size": 12293, "mtime": 1750450365816, "results": "47", "hashOfConfig": "44"}, {"size": 322, "mtime": 1750450374105, "results": "48", "hashOfConfig": "44"}, {"size": 4505, "mtime": 1750480478087, "results": "49", "hashOfConfig": "44"}, {"size": 2417, "mtime": 1750479547163, "results": "50", "hashOfConfig": "44"}, {"size": 234, "mtime": 1750479534363, "results": "51", "hashOfConfig": "44"}, {"size": 2327, "mtime": 1750479527055, "results": "52", "hashOfConfig": "44"}, {"size": 3521, "mtime": 1750479510583, "results": "53", "hashOfConfig": "44"}, {"size": 2953, "mtime": 1750491242788, "results": "54", "hashOfConfig": "44"}, {"size": 6324, "mtime": 1750488801390, "results": "55", "hashOfConfig": "44"}, {"size": 2854, "mtime": 1750488856645, "results": "56", "hashOfConfig": "44"}, {"size": 2757, "mtime": 1750488842285, "results": "57", "hashOfConfig": "44"}, {"size": 134, "mtime": 1750489528372, "results": "58", "hashOfConfig": "44"}, {"size": 4590, "mtime": 1750488467062, "results": "59", "hashOfConfig": "44"}, {"size": 4522, "mtime": 1750488386953, "results": "60", "hashOfConfig": "44"}, {"size": 6875, "mtime": 1750488413858, "results": "61", "hashOfConfig": "44"}, {"size": 3900, "mtime": 1750491260044, "results": "62", "hashOfConfig": "44"}, {"size": 5458, "mtime": 1750491281040, "results": "63", "hashOfConfig": "44"}, {"size": 5376, "mtime": 1750491226851, "results": "64", "hashOfConfig": "44"}, {"size": 6169, "mtime": 1750488826086, "results": "65", "hashOfConfig": "44"}, {"size": 6875, "mtime": 1750488446570, "results": "66", "hashOfConfig": "44"}, {"size": 7350, "mtime": 1750488500747, "results": "67", "hashOfConfig": "44"}, {"size": 2475, "mtime": 1750491025548, "results": "68", "hashOfConfig": "44"}, {"size": 2221, "mtime": 1750491012960, "results": "69", "hashOfConfig": "44"}, {"size": 4390, "mtime": 1750489368505, "results": "70", "hashOfConfig": "44"}, {"size": 11857, "mtime": 1750488896121, "results": "71", "hashOfConfig": "44"}, {"size": 10559, "mtime": 1750489453304, "results": "72", "hashOfConfig": "44"}, {"size": 11265, "mtime": 1750488540096, "results": "73", "hashOfConfig": "44"}, {"size": 11730, "mtime": 1750491607368, "results": "74", "hashOfConfig": "44"}, {"size": 8973, "mtime": 1750491568530, "results": "75", "hashOfConfig": "44"}, {"size": 9234, "mtime": 1750492398429, "results": "76", "hashOfConfig": "44"}, {"size": 1584, "mtime": 1750489408596, "results": "77", "hashOfConfig": "44"}, {"size": 1128, "mtime": 1750489478418, "results": "78", "hashOfConfig": "44"}, {"size": 1835, "mtime": 1750489381382, "results": "79", "hashOfConfig": "44"}, {"size": 1849, "mtime": 1750489469169, "results": "80", "hashOfConfig": "44"}, {"size": 823, "mtime": 1750489389981, "results": "81", "hashOfConfig": "44"}, {"size": 724, "mtime": 1750489397720, "results": "82", "hashOfConfig": "44"}, {"size": 3393, "mtime": 1750485405999, "results": "83", "hashOfConfig": "44"}, {"size": 166, "mtime": 1750489416334, "results": "84", "hashOfConfig": "44"}, {"size": 7120, "mtime": 1750492265014, "results": "85", "hashOfConfig": "44"}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "abwas5", {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\health\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\leads\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\lib\\db.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\leads\\[id]\\convert\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\service\\assignments\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\service\\health\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\lib\\service-auth.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\lib\\service-clients.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\announcements\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\assignments\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\assignments\\[id]\\grade\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\assignments\\[id]\\submissions\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\auth\\[...nextauth]\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\cabinets\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\courses\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\groups\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\messages\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\messages\\[threadId]\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\notifications\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\resources\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\teachers\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\teachers\\[id]\\kpis\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\webhooks\\receive\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\webhooks\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\auth\\signin\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\dashboard\\assignments\\page.tsx", ["212"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\dashboard\\courses\\page.tsx", ["213"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\components\\dashboard\\MessagingWidget.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\components\\dashboard\\NotificationCenter.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\components\\layout\\ResponsiveDashboard.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\components\\ui\\alert.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\components\\ui\\input.tsx", ["214"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\lib\\auth.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\__tests__\\api\\leads.test.ts", [], [], {"ruleId": "215", "severity": 1, "message": "216", "line": 64, "column": 6, "nodeType": "217", "endLine": 64, "endColumn": 33, "suggestions": "218"}, {"ruleId": "215", "severity": 1, "message": "219", "line": 58, "column": 6, "nodeType": "217", "endLine": 58, "endColumn": 20, "suggestions": "220"}, {"ruleId": "221", "severity": 2, "message": "222", "line": 4, "column": 18, "nodeType": "223", "messageId": "224", "endLine": 4, "endColumn": 28, "suggestions": "225"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchAssignments'. Either include it or remove the dependency array.", "ArrayExpression", ["226"], "React Hook useEffect has a missing dependency: 'fetchCourses'. Either include it or remove the dependency array.", ["227"], "@typescript-eslint/no-empty-object-type", "An interface declaring no members is equivalent to its supertype.", "Identifier", "noEmptyInterfaceWithSuper", ["228"], {"desc": "229", "fix": "230"}, {"desc": "231", "fix": "232"}, {"messageId": "233", "fix": "234", "desc": "235"}, "Update the dependencies array to be: [currentPage, fetchAssignments, statusFilter]", {"range": "236", "text": "237"}, "Update the dependencies array to be: [fetchCourses, statusFilter]", {"range": "238", "text": "239"}, "replaceEmptyInterfaceWithSuper", {"range": "240", "text": "241"}, "Replace empty interface with a type alias.", [1562, 1589], "[currentPage, fetchAssignments, statusFilter]", [1307, 1321], "[fetchCourses, statusFilter]", [72, 149], "type InputProps = React.InputHTMLAttributes<HTMLInputElement>"]