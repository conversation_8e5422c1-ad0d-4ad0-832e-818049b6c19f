(()=>{var e={};e.id=131,e.ids=[131],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2909:(e,t,r)=>{"use strict";r.d(t,{Y9:()=>d,j2:()=>u});var s=r(8643),a=r(189),n=r(6467),i=r(5069),o=r(5663);let{handlers:d,auth:u,signIn:p,signOut:c}=(0,s.Ay)({adapter:(0,n.y)(i.z),session:{strategy:"jwt",maxAge:1800},pages:{signIn:"/auth/signin",error:"/auth/error"},providers:[(0,a.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"},mfaCode:{label:"MFA Code",type:"text"}},async authorize(e){if(!e?.email||!e?.password)return null;try{let t=await i.z.adminUser.findUnique({where:{email:e.email}});if(!t||!t.isActive||!await o.Ay.compare(e.password,t.passwordHash))return null;return t.mfaEnabled&&!e.mfaCode&&console.warn("MFA required but not implemented yet"),await i.z.adminUser.update({where:{id:t.id},data:{lastLogin:new Date}}),await i.z.auditLog.create({data:{userId:t.id,action:"LOGIN",resourceType:"AUTH",resourceId:t.id,newValues:{timestamp:new Date,success:!0},ipAddress:"127.0.0.1",userAgent:"Unknown"}}),{id:t.id,email:t.email,name:`${t.firstName} ${t.lastName}`,role:t.role}}catch(e){return console.error("Authentication error:",e),null}}})],callbacks:{jwt:async({token:e,user:t})=>(t&&(e.role=t.role,e.id=t.id),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.id,e.user.role=t.role),e),async authorized({auth:e,request:{nextUrl:t}}){let r=!!e?.user,s=t.pathname.startsWith("/dashboard"),a=t.pathname.startsWith("/auth");return s?!!r:!a||!r||Response.redirect(new URL("/dashboard",t))}},events:{async signOut(e){let t="token"in e&&e.token?.id?e.token.id:"session"in e&&e.session?.user?.id?e.session.user.id:null;t&&await i.z.auditLog.create({data:{userId:t,action:"LOGOUT",resourceType:"AUTH",resourceId:t,newValues:{timestamp:new Date},ipAddress:"127.0.0.1",userAgent:"Unknown"}})}}}),{GET:l,POST:m}=d},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5069:(e,t,r)=>{"use strict";r.d(t,{db:()=>n,z:()=>a});var s=r(6330);let a=globalThis.prisma??new s.PrismaClient({log:["query","error","warn"]}),n=a},5511:e=>{"use strict";e.exports=require("crypto")},6330:e=>{"use strict";e.exports=require("@prisma/client")},6487:()=>{},8335:()=>{},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9574:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>z,routeModule:()=>g,serverHooks:()=>x,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>h});var s={};r.r(s),r.d(s,{GET:()=>m,POST:()=>y});var a=r(6559),n=r(8088),i=r(7719),o=r(2190),d=r(2909),u=r(5069),p=r(6330),c=r(5697);let l=c.z.object({studentId:c.z.string().min(1,"Student ID is required"),amount:c.z.number().positive("Amount must be positive"),paymentDate:c.z.string().transform(e=>new Date(e)),paymentMethod:c.z.nativeEnum(p.PaymentMethod),description:c.z.string().optional(),notes:c.z.string().optional()});async function m(e){try{let t=await (0,d.j2)();if(!t?.user)return o.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:r}=new URL(e.url),s=parseInt(r.get("page")||"1"),a=parseInt(r.get("limit")||"10"),n=r.get("studentId"),i=r.get("status"),p=(s-1)*a,c={};n&&(c.studentId=n),i&&(c.status=i);let[l,m]=await Promise.all([u.z.paymentRecord.findMany({where:c,skip:p,take:a,include:{recordedByUser:{select:{firstName:!0,lastName:!0,email:!0}},verifiedByUser:{select:{firstName:!0,lastName:!0,email:!0}}},orderBy:{createdAt:"desc"}}),u.z.paymentRecord.count({where:c})]);return await u.z.auditLog.create({data:{userId:t.user.id,action:"VIEW_PAYMENTS",resourceType:"PAYMENT_RECORD",newValues:{page:s,limit:a,filters:{studentId:n,status:i}},ipAddress:"127.0.0.1",userAgent:e.headers.get("user-agent")||"Unknown"}}),o.NextResponse.json({success:!0,data:{payments:l,pagination:{page:s,limit:a,total:m,pages:Math.ceil(m/a)}}})}catch(e){return console.error("Error fetching payments:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}async function y(e){try{let t=await (0,d.j2)();if(!t?.user)return o.NextResponse.json({error:"Unauthorized"},{status:401});let r=await e.json(),s=l.parse(r),a=await u.z.paymentRecord.create({data:{...s,recordedBy:t.user.id,status:p.PaymentStatus.recorded},include:{recordedByUser:{select:{firstName:!0,lastName:!0,email:!0}}}});return await u.z.financialTransaction.create({data:{paymentRecordId:a.id,transactionType:p.TransactionType.payment,amount:s.amount,description:`Payment recorded: ${s.description||"No description"}`,performedBy:t.user.id,ipAddress:"127.0.0.1"}}),await u.z.auditLog.create({data:{userId:t.user.id,action:"CREATE_PAYMENT",resourceType:"PAYMENT_RECORD",resourceId:a.id,newValues:s,ipAddress:"127.0.0.1",userAgent:e.headers.get("user-agent")||"Unknown"}}),o.NextResponse.json({success:!0,data:a})}catch(e){if(e instanceof c.z.ZodError)return o.NextResponse.json({error:"Validation error",details:e.errors},{status:400});return console.error("Error creating payment:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}c.z.object({amount:c.z.number().positive().optional(),paymentDate:c.z.string().transform(e=>new Date(e)).optional(),paymentMethod:c.z.nativeEnum(p.PaymentMethod).optional(),description:c.z.string().optional(),notes:c.z.string().optional(),status:c.z.nativeEnum(p.PaymentStatus).optional()});let g=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/payments/route",pathname:"/api/payments",filename:"route",bundlePath:"app/api/payments/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-admin-service\\src\\app\\api\\payments\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:w,workUnitAsyncStorage:h,serverHooks:x}=g;function z(){return(0,i.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:h})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,199,828,697],()=>r(9574));module.exports=s})();