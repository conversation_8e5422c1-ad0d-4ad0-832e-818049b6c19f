"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/next-auth";
exports.ids = ["vendor-chunks/next-auth"];
exports.modules = {

/***/ "(rsc)/./node_modules/next-auth/react.js":
/*!*****************************************!*\
  !*** ./node_modules/next-auth/react.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionContext: () => (/* binding */ SessionContext),\n/* harmony export */   SessionProvider: () => (/* binding */ SessionProvider),\n/* harmony export */   __NEXTAUTH: () => (/* binding */ __NEXTAUTH),\n/* harmony export */   getCsrfToken: () => (/* binding */ getCsrfToken),\n/* harmony export */   getProviders: () => (/* binding */ getProviders),\n/* harmony export */   getSession: () => (/* binding */ getSession),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut),\n/* harmony export */   useSession: () => (/* binding */ useSession)\n/* harmony export */ });\n/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js\");\n\nconst __NEXTAUTH = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call __NEXTAUTH() from the server but __NEXTAUTH is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Innovative Centre\\\\crm-admin-service\\\\node_modules\\\\next-auth\\\\react.js\",\n\"__NEXTAUTH\",\n);const SessionContext = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call SessionContext() from the server but SessionContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Innovative Centre\\\\crm-admin-service\\\\node_modules\\\\next-auth\\\\react.js\",\n\"SessionContext\",\n);const useSession = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call useSession() from the server but useSession is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Innovative Centre\\\\crm-admin-service\\\\node_modules\\\\next-auth\\\\react.js\",\n\"useSession\",\n);const getSession = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call getSession() from the server but getSession is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Innovative Centre\\\\crm-admin-service\\\\node_modules\\\\next-auth\\\\react.js\",\n\"getSession\",\n);const getCsrfToken = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call getCsrfToken() from the server but getCsrfToken is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Innovative Centre\\\\crm-admin-service\\\\node_modules\\\\next-auth\\\\react.js\",\n\"getCsrfToken\",\n);const getProviders = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call getProviders() from the server but getProviders is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Innovative Centre\\\\crm-admin-service\\\\node_modules\\\\next-auth\\\\react.js\",\n\"getProviders\",\n);const signIn = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call signIn() from the server but signIn is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Innovative Centre\\\\crm-admin-service\\\\node_modules\\\\next-auth\\\\react.js\",\n\"signIn\",\n);const signOut = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call signOut() from the server but signOut is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Innovative Centre\\\\crm-admin-service\\\\node_modules\\\\next-auth\\\\react.js\",\n\"signOut\",\n);const SessionProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call SessionProvider() from the server but SessionProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Innovative Centre\\\\crm-admin-service\\\\node_modules\\\\next-auth\\\\react.js\",\n\"SessionProvider\",\n);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/react.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-auth/lib/client.js":
/*!**********************************************!*\
  !*** ./node_modules/next-auth/lib/client.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClientSessionError: () => (/* binding */ ClientSessionError),\n/* harmony export */   apiBaseUrl: () => (/* binding */ apiBaseUrl),\n/* harmony export */   fetchData: () => (/* binding */ fetchData),\n/* harmony export */   now: () => (/* binding */ now),\n/* harmony export */   parseUrl: () => (/* binding */ parseUrl),\n/* harmony export */   useOnline: () => (/* binding */ useOnline)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _auth_core_errors__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @auth/core/errors */ \"(ssr)/./node_modules/@auth/core/errors.js\");\n/* __next_internal_client_entry_do_not_use__ ClientSessionError,fetchData,apiBaseUrl,useOnline,now,parseUrl auto */ \n\n/** @todo */ class ClientFetchError extends _auth_core_errors__WEBPACK_IMPORTED_MODULE_1__.AuthError {\n}\n/** @todo */ class ClientSessionError extends _auth_core_errors__WEBPACK_IMPORTED_MODULE_1__.AuthError {\n}\n// ------------------------ Internal ------------------------\n/**\n * If passed 'appContext' via getInitialProps() in _app.js\n * then get the req object from ctx and use that for the\n * req value to allow `fetchData` to\n * work seemlessly in getInitialProps() on server side\n * pages *and* in _app.js.\n * @internal\n */ async function fetchData(path, __NEXTAUTH, logger, req = {}) {\n    const url = `${apiBaseUrl(__NEXTAUTH)}/${path}`;\n    try {\n        const options = {\n            headers: {\n                \"Content-Type\": \"application/json\",\n                ...req?.headers?.cookie ? {\n                    cookie: req.headers.cookie\n                } : {}\n            }\n        };\n        if (req?.body) {\n            options.body = JSON.stringify(req.body);\n            options.method = \"POST\";\n        }\n        const res = await fetch(url, options);\n        const data = await res.json();\n        if (!res.ok) throw data;\n        return data;\n    } catch (error) {\n        logger.error(new ClientFetchError(error.message, error));\n        return null;\n    }\n}\n/** @internal */ function apiBaseUrl(__NEXTAUTH) {\n    if (true) {\n        // Return absolute path when called server side\n        return `${__NEXTAUTH.baseUrlServer}${__NEXTAUTH.basePathServer}`;\n    }\n    // Return relative path when called client side\n    return __NEXTAUTH.basePath;\n}\n/** @internal  */ function useOnline() {\n    const [isOnline, setIsOnline] = react__WEBPACK_IMPORTED_MODULE_0__.useState(typeof navigator !== \"undefined\" ? navigator.onLine : false);\n    const setOnline = ()=>setIsOnline(true);\n    const setOffline = ()=>setIsOnline(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useOnline.useEffect\": ()=>{\n            window.addEventListener(\"online\", setOnline);\n            window.addEventListener(\"offline\", setOffline);\n            return ({\n                \"useOnline.useEffect\": ()=>{\n                    window.removeEventListener(\"online\", setOnline);\n                    window.removeEventListener(\"offline\", setOffline);\n                }\n            })[\"useOnline.useEffect\"];\n        }\n    }[\"useOnline.useEffect\"], []);\n    return isOnline;\n}\n/**\n * Returns the number of seconds elapsed since January 1, 1970 00:00:00 UTC.\n * @internal\n */ function now() {\n    return Math.floor(Date.now() / 1000);\n}\n/**\n * Returns an `URL` like object to make requests/redirects from server-side\n * @internal\n */ function parseUrl(url) {\n    const defaultUrl = new URL(\"http://localhost:3000/api/auth\");\n    if (url && !url.startsWith(\"http\")) {\n        url = `https://${url}`;\n    }\n    const _url = new URL(url || defaultUrl);\n    const path = (_url.pathname === \"/\" ? defaultUrl.pathname : _url.pathname)// Remove trailing slash\n    .replace(/\\/$/, \"\");\n    const base = `${_url.origin}${path}`;\n    return {\n        origin: _url.origin,\n        host: _url.host,\n        path,\n        base,\n        toString: ()=>base\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-auth/lib/client.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-auth/react.js":
/*!*****************************************!*\
  !*** ./node_modules/next-auth/react.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionContext: () => (/* binding */ SessionContext),\n/* harmony export */   SessionProvider: () => (/* binding */ SessionProvider),\n/* harmony export */   __NEXTAUTH: () => (/* binding */ __NEXTAUTH),\n/* harmony export */   getCsrfToken: () => (/* binding */ getCsrfToken),\n/* harmony export */   getProviders: () => (/* binding */ getProviders),\n/* harmony export */   getSession: () => (/* binding */ getSession),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut),\n/* harmony export */   useSession: () => (/* binding */ useSession)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _lib_client_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/client.js */ \"(ssr)/./node_modules/next-auth/lib/client.js\");\n/**\n *\n * NextAuth.js is the official integration of Auth.js for Next.js applications. It supports both\n * [Client Components](https://nextjs.org/docs/app/building-your-application/rendering/client-components) and the\n * [Pages Router](https://nextjs.org/docs/pages). It includes methods for signing in, signing out, hooks, and a React\n * Context provider to wrap your application and make session data available anywhere.\n *\n * For use in [Server Actions](https://nextjs.org/docs/app/api-reference/functions/server-actions), check out [these methods](https://authjs.dev/guides/upgrade-to-v5#methods)\n *\n * @module react\n */ /* __next_internal_client_entry_do_not_use__ __NEXTAUTH,SessionContext,useSession,getSession,getCsrfToken,getProviders,signIn,signOut,SessionProvider auto */ \n\n\n// This behaviour mirrors the default behaviour for getting the site name that\n// happens server side in server/index.js\n// 1. An empty value is legitimate when the code is being invoked client side as\n//    relative URLs are valid in that context and so defaults to empty.\n// 2. When invoked server side the value is picked up from an environment\n//    variable and defaults to 'http://localhost:3000'.\nconst __NEXTAUTH = {\n    baseUrl: (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.parseUrl)(process.env.NEXTAUTH_URL ?? process.env.VERCEL_URL).origin,\n    basePath: (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.parseUrl)(process.env.NEXTAUTH_URL).path,\n    baseUrlServer: (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.parseUrl)(process.env.NEXTAUTH_URL_INTERNAL ?? process.env.NEXTAUTH_URL ?? process.env.VERCEL_URL).origin,\n    basePathServer: (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.parseUrl)(process.env.NEXTAUTH_URL_INTERNAL ?? process.env.NEXTAUTH_URL).path,\n    _lastSync: 0,\n    _session: undefined,\n    _getSession: ()=>{}\n};\nlet broadcastChannel = null;\nfunction getNewBroadcastChannel() {\n    return new BroadcastChannel(\"next-auth\");\n}\nfunction broadcast() {\n    if (typeof BroadcastChannel === \"undefined\") {\n        return {\n            postMessage: ()=>{},\n            addEventListener: ()=>{},\n            removeEventListener: ()=>{}\n        };\n    }\n    if (broadcastChannel === null) {\n        broadcastChannel = getNewBroadcastChannel();\n    }\n    return broadcastChannel;\n}\n// TODO:\nconst logger = {\n    debug: console.debug,\n    error: console.error,\n    warn: console.warn\n};\nconst SessionContext = react__WEBPACK_IMPORTED_MODULE_1__.createContext?.(undefined);\n/**\n * React Hook that gives you access to the logged in user's session data and lets you modify it.\n *\n * :::info\n * `useSession` is for client-side use only and when using [Next.js App Router (`app/`)](https://nextjs.org/blog/next-13-4#nextjs-app-router) you should prefer the `auth()` export.\n * :::\n */ function useSession(options) {\n    if (!SessionContext) {\n        throw new Error(\"React Context is unavailable in Server Components\");\n    }\n    // @ts-expect-error Satisfy TS if branch on line below\n    const value = react__WEBPACK_IMPORTED_MODULE_1__.useContext(SessionContext);\n    if (!value && \"development\" !== \"production\") {\n        throw new Error(\"[next-auth]: `useSession` must be wrapped in a <SessionProvider />\");\n    }\n    const { required, onUnauthenticated } = options ?? {};\n    const requiredAndNotLoading = required && value.status === \"unauthenticated\";\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"useSession.useEffect\": ()=>{\n            if (requiredAndNotLoading) {\n                const url = `${__NEXTAUTH.basePath}/signin?${new URLSearchParams({\n                    error: \"SessionRequired\",\n                    callbackUrl: window.location.href\n                })}`;\n                if (onUnauthenticated) onUnauthenticated();\n                else window.location.href = url;\n            }\n        }\n    }[\"useSession.useEffect\"], [\n        requiredAndNotLoading,\n        onUnauthenticated\n    ]);\n    if (requiredAndNotLoading) {\n        return {\n            data: value.data,\n            update: value.update,\n            status: \"loading\"\n        };\n    }\n    return value;\n}\nasync function getSession(params) {\n    const session = await (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.fetchData)(\"session\", __NEXTAUTH, logger, params);\n    if (params?.broadcast ?? true) {\n        const broadcastChannel = getNewBroadcastChannel();\n        broadcastChannel.postMessage({\n            event: \"session\",\n            data: {\n                trigger: \"getSession\"\n            }\n        });\n    }\n    return session;\n}\n/**\n * Returns the current Cross-Site Request Forgery Token (CSRF Token)\n * required to make requests that changes state. (e.g. signing in or out, or updating the session).\n *\n * [CSRF Prevention: Double Submit Cookie](https://cheatsheetseries.owasp.org/cheatsheets/Cross-Site_Request_Forgery_Prevention_Cheat_Sheet.html#double-submit-cookie)\n */ async function getCsrfToken() {\n    const response = await (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.fetchData)(\"csrf\", __NEXTAUTH, logger);\n    return response?.csrfToken ?? \"\";\n}\nasync function getProviders() {\n    return (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.fetchData)(\"providers\", __NEXTAUTH, logger);\n}\nasync function signIn(provider, options, authorizationParams) {\n    const { callbackUrl, ...rest } = options ?? {};\n    const { redirect = true, redirectTo = callbackUrl ?? window.location.href, ...signInParams } = rest;\n    const baseUrl = (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.apiBaseUrl)(__NEXTAUTH);\n    const providers = await getProviders();\n    if (!providers) {\n        const url = `${baseUrl}/error`;\n        window.location.href = url;\n        return; // TODO: Return error if `redirect: false`\n    }\n    if (!provider || !providers[provider]) {\n        const url = `${baseUrl}/signin?${new URLSearchParams({\n            callbackUrl: redirectTo\n        })}`;\n        window.location.href = url;\n        return; // TODO: Return error if `redirect: false`\n    }\n    const providerType = providers[provider].type;\n    if (providerType === \"webauthn\") {\n        // TODO: Add docs link with explanation\n        throw new TypeError([\n            `Provider id \"${provider}\" refers to a WebAuthn provider.`,\n            'Please use `import { signIn } from \"next-auth/webauthn\"` instead.'\n        ].join(\"\\n\"));\n    }\n    const signInUrl = `${baseUrl}/${providerType === \"credentials\" ? \"callback\" : \"signin\"}/${provider}`;\n    const csrfToken = await getCsrfToken();\n    const res = await fetch(`${signInUrl}?${new URLSearchParams(authorizationParams)}`, {\n        method: \"post\",\n        headers: {\n            \"Content-Type\": \"application/x-www-form-urlencoded\",\n            \"X-Auth-Return-Redirect\": \"1\"\n        },\n        body: new URLSearchParams({\n            ...signInParams,\n            csrfToken,\n            callbackUrl: redirectTo\n        })\n    });\n    const data = await res.json();\n    if (redirect) {\n        const url = data.url ?? redirectTo;\n        window.location.href = url;\n        // If url contains a hash, the browser does not reload the page. We reload manually\n        if (url.includes(\"#\")) window.location.reload();\n        return;\n    }\n    const error = new URL(data.url).searchParams.get(\"error\") ?? undefined;\n    const code = new URL(data.url).searchParams.get(\"code\") ?? undefined;\n    if (res.ok) {\n        await __NEXTAUTH._getSession({\n            event: \"storage\"\n        });\n    }\n    return {\n        error,\n        code,\n        status: res.status,\n        ok: res.ok,\n        url: error ? null : data.url\n    };\n}\nasync function signOut(options) {\n    const { redirect = true, redirectTo = options?.callbackUrl ?? window.location.href } = options ?? {};\n    const baseUrl = (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.apiBaseUrl)(__NEXTAUTH);\n    const csrfToken = await getCsrfToken();\n    const res = await fetch(`${baseUrl}/signout`, {\n        method: \"post\",\n        headers: {\n            \"Content-Type\": \"application/x-www-form-urlencoded\",\n            \"X-Auth-Return-Redirect\": \"1\"\n        },\n        body: new URLSearchParams({\n            csrfToken,\n            callbackUrl: redirectTo\n        })\n    });\n    const data = await res.json();\n    broadcast().postMessage({\n        event: \"session\",\n        data: {\n            trigger: \"signout\"\n        }\n    });\n    if (redirect) {\n        const url = data.url ?? redirectTo;\n        window.location.href = url;\n        // If url contains a hash, the browser does not reload the page. We reload manually\n        if (url.includes(\"#\")) window.location.reload();\n        return;\n    }\n    await __NEXTAUTH._getSession({\n        event: \"storage\"\n    });\n    return data;\n}\n/**\n * [React Context](https://react.dev/learn/passing-data-deeply-with-context) provider to wrap the app (`pages/`) to make session data available anywhere.\n *\n * When used, the session state is automatically synchronized across all open tabs/windows and they are all updated whenever they gain or lose focus\n * or the state changes (e.g. a user signs in or out) when {@link SessionProviderProps.refetchOnWindowFocus} is `true`.\n *\n * :::info\n * `SessionProvider` is for client-side use only and when using [Next.js App Router (`app/`)](https://nextjs.org/blog/next-13-4#nextjs-app-router) you should prefer the `auth()` export.\n * :::\n */ function SessionProvider(props) {\n    if (!SessionContext) {\n        throw new Error(\"React Context is unavailable in Server Components\");\n    }\n    const { children, basePath, refetchInterval, refetchWhenOffline } = props;\n    if (basePath) __NEXTAUTH.basePath = basePath;\n    /**\n     * If session was `null`, there was an attempt to fetch it,\n     * but it failed, but we still treat it as a valid initial value.\n     */ const hasInitialSession = props.session !== undefined;\n    /** If session was passed, initialize as already synced */ __NEXTAUTH._lastSync = hasInitialSession ? (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.now)() : 0;\n    const [session, setSession] = react__WEBPACK_IMPORTED_MODULE_1__.useState({\n        \"SessionProvider.useState\": ()=>{\n            if (hasInitialSession) __NEXTAUTH._session = props.session;\n            return props.session;\n        }\n    }[\"SessionProvider.useState\"]);\n    /** If session was passed, initialize as not loading */ const [loading, setLoading] = react__WEBPACK_IMPORTED_MODULE_1__.useState(!hasInitialSession);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"SessionProvider.useEffect\": ()=>{\n            __NEXTAUTH._getSession = ({\n                \"SessionProvider.useEffect\": async ({ event } = {})=>{\n                    try {\n                        const storageEvent = event === \"storage\";\n                        // We should always update if we don't have a client session yet\n                        // or if there are events from other tabs/windows\n                        if (storageEvent || __NEXTAUTH._session === undefined) {\n                            __NEXTAUTH._lastSync = (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.now)();\n                            __NEXTAUTH._session = await getSession({\n                                broadcast: !storageEvent\n                            });\n                            setSession(__NEXTAUTH._session);\n                            return;\n                        }\n                        if (// If there is no time defined for when a session should be considered\n                        // stale, then it's okay to use the value we have until an event is\n                        // triggered which updates it\n                        !event || // If the client doesn't have a session then we don't need to call\n                        // the server to check if it does (if they have signed in via another\n                        // tab or window that will come through as a \"stroage\" event\n                        // event anyway)\n                        __NEXTAUTH._session === null || // Bail out early if the client session is not stale yet\n                        (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.now)() < __NEXTAUTH._lastSync) {\n                            return;\n                        }\n                        // An event or session staleness occurred, update the client session.\n                        __NEXTAUTH._lastSync = (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.now)();\n                        __NEXTAUTH._session = await getSession();\n                        setSession(__NEXTAUTH._session);\n                    } catch (error) {\n                        logger.error(new _lib_client_js__WEBPACK_IMPORTED_MODULE_2__.ClientSessionError(error.message, error));\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            })[\"SessionProvider.useEffect\"];\n            __NEXTAUTH._getSession();\n            return ({\n                \"SessionProvider.useEffect\": ()=>{\n                    __NEXTAUTH._lastSync = 0;\n                    __NEXTAUTH._session = undefined;\n                    __NEXTAUTH._getSession = ({\n                        \"SessionProvider.useEffect\": ()=>{}\n                    })[\"SessionProvider.useEffect\"];\n                }\n            })[\"SessionProvider.useEffect\"];\n        }\n    }[\"SessionProvider.useEffect\"], []);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"SessionProvider.useEffect\": ()=>{\n            const handle = {\n                \"SessionProvider.useEffect.handle\": ()=>__NEXTAUTH._getSession({\n                        event: \"storage\"\n                    })\n            }[\"SessionProvider.useEffect.handle\"];\n            // Listen for storage events and update session if event fired from\n            // another window (but suppress firing another event to avoid a loop)\n            // Fetch new session data but tell it to not to fire another event to\n            // avoid an infinite loop.\n            // Note: We could pass session data through and do something like\n            // `setData(message.data)` but that can cause problems depending\n            // on how the session object is being used in the client; it is\n            // more robust to have each window/tab fetch it's own copy of the\n            // session object rather than share it across instances.\n            broadcast().addEventListener(\"message\", handle);\n            return ({\n                \"SessionProvider.useEffect\": ()=>broadcast().removeEventListener(\"message\", handle)\n            })[\"SessionProvider.useEffect\"];\n        }\n    }[\"SessionProvider.useEffect\"], []);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"SessionProvider.useEffect\": ()=>{\n            const { refetchOnWindowFocus = true } = props;\n            // Listen for when the page is visible, if the user switches tabs\n            // and makes our tab visible again, re-fetch the session, but only if\n            // this feature is not disabled.\n            const visibilityHandler = {\n                \"SessionProvider.useEffect.visibilityHandler\": ()=>{\n                    if (refetchOnWindowFocus && document.visibilityState === \"visible\") __NEXTAUTH._getSession({\n                        event: \"visibilitychange\"\n                    });\n                }\n            }[\"SessionProvider.useEffect.visibilityHandler\"];\n            document.addEventListener(\"visibilitychange\", visibilityHandler, false);\n            return ({\n                \"SessionProvider.useEffect\": ()=>document.removeEventListener(\"visibilitychange\", visibilityHandler, false)\n            })[\"SessionProvider.useEffect\"];\n        }\n    }[\"SessionProvider.useEffect\"], [\n        props.refetchOnWindowFocus\n    ]);\n    const isOnline = (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.useOnline)();\n    // TODO: Flip this behavior in next major version\n    const shouldRefetch = refetchWhenOffline !== false || isOnline;\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"SessionProvider.useEffect\": ()=>{\n            if (refetchInterval && shouldRefetch) {\n                const refetchIntervalTimer = setInterval({\n                    \"SessionProvider.useEffect.refetchIntervalTimer\": ()=>{\n                        if (__NEXTAUTH._session) {\n                            __NEXTAUTH._getSession({\n                                event: \"poll\"\n                            });\n                        }\n                    }\n                }[\"SessionProvider.useEffect.refetchIntervalTimer\"], refetchInterval * 1000);\n                return ({\n                    \"SessionProvider.useEffect\": ()=>clearInterval(refetchIntervalTimer)\n                })[\"SessionProvider.useEffect\"];\n            }\n        }\n    }[\"SessionProvider.useEffect\"], [\n        refetchInterval,\n        shouldRefetch\n    ]);\n    const value = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"SessionProvider.useMemo[value]\": ()=>({\n                data: session,\n                status: loading ? \"loading\" : session ? \"authenticated\" : \"unauthenticated\",\n                async update (data) {\n                    if (loading) return;\n                    setLoading(true);\n                    const newSession = await (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.fetchData)(\"session\", __NEXTAUTH, logger, typeof data === \"undefined\" ? undefined : {\n                        body: {\n                            csrfToken: await getCsrfToken(),\n                            data\n                        }\n                    });\n                    setLoading(false);\n                    if (newSession) {\n                        setSession(newSession);\n                        broadcast().postMessage({\n                            event: \"session\",\n                            data: {\n                                trigger: \"getSession\"\n                            }\n                        });\n                    }\n                    return newSession;\n                }\n            })\n    }[\"SessionProvider.useMemo[value]\"], [\n        session,\n        loading\n    ]);\n    return(// @ts-expect-error\n    (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SessionContext.Provider, {\n        value: value,\n        children: children\n    }));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-auth/react.js\n");

/***/ })

};
;