import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { prisma } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get lead statistics
    const totalLeads = await prisma.lead.count();
    const activeLeads = await prisma.lead.count({
      where: {
        status: {
          in: ['new', 'contacted', 'interested']
        }
      }
    });

    const leadsByStatus = await prisma.lead.groupBy({
      by: ['status'],
      _count: {
        status: true,
      },
    });

    // Get course statistics
    const totalCourses = await prisma.course.count();
    const activeCourses = await prisma.course.count({
      where: {
        isActive: true,
      },
    });

    // Get teacher statistics
    const totalTeachers = await prisma.teacher.count();
    const activeTeachers = await prisma.teacher.count({
      where: {
        isActive: true,
      },
    });

    // Get group statistics
    const totalGroups = await prisma.group.count();
    const activeGroups = await prisma.group.count({
      where: {
        isActive: true,
      },
    });

    // Get recent leads
    const recentLeads = await prisma.lead.findMany({
      take: 5,
      orderBy: {
        createdAt: 'desc',
      },
      include: {
        assignedToUser: {
          select: {
            firstName: true,
            lastName: true,
          },
        },
      },
    });

    // Get recent courses
    const recentCourses = await prisma.course.findMany({
      take: 3,
      orderBy: {
        createdAt: 'desc',
      },
      where: {
        isActive: true,
      },
    });

    const stats = {
      totalLeads,
      activeLeads,
      totalCourses: activeCourses,
      totalTeachers: activeTeachers,
      totalGroups: activeGroups,
      leadsByStatus: leadsByStatus.reduce((acc, item) => {
        acc[item.status] = item._count.status;
        return acc;
      }, {} as Record<string, number>),
      recentLeads: recentLeads.map(lead => ({
        id: lead.id,
        firstName: lead.firstName,
        lastName: lead.lastName,
        email: lead.email,
        phone: lead.phone,
        status: lead.status,
        source: lead.source,
        assignedTo: lead.assignedToUser ? 
          `${lead.assignedToUser.firstName} ${lead.assignedToUser.lastName}` : 
          'Unassigned',
        createdAt: lead.createdAt,
      })),
      recentCourses: recentCourses.map(course => ({
        id: course.id,
        title: course.title,
        level: course.level,
        duration: course.duration,
        price: course.price,
        description: course.description,
      })),
    };

    return NextResponse.json({
      success: true,
      data: stats,
    });
  } catch (error) {
    console.error('Dashboard stats error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch dashboard statistics' 
      },
      { status: 500 }
    );
  }
}
