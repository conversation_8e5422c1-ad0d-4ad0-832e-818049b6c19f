(()=>{var e={};e.id=705,e.ids=[705],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2909:(e,r,t)=>{"use strict";t.d(r,{Y9:()=>u,j2:()=>d});var s=t(8643),a=t(189),n=t(6467),i=t(5069),o=t(5663);let{handlers:u,auth:d,signIn:c,signOut:l}=(0,s.Ay)({adapter:(0,n.y)(i.z),session:{strategy:"jwt",maxAge:1800},pages:{signIn:"/auth/signin",error:"/auth/error"},providers:[(0,a.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"},mfaCode:{label:"MFA Code",type:"text"}},async authorize(e){if(!e?.email||!e?.password)return null;try{let r=await i.z.adminUser.findUnique({where:{email:e.email}});if(!r||!r.isActive||!await o.Ay.compare(e.password,r.passwordHash))return null;return r.mfaEnabled&&!e.mfaCode&&console.warn("MFA required but not implemented yet"),await i.z.adminUser.update({where:{id:r.id},data:{lastLogin:new Date}}),await i.z.auditLog.create({data:{userId:r.id,action:"LOGIN",resourceType:"AUTH",resourceId:r.id,newValues:{timestamp:new Date,success:!0},ipAddress:"127.0.0.1",userAgent:"Unknown"}}),{id:r.id,email:r.email,name:`${r.firstName} ${r.lastName}`,role:r.role}}catch(e){return console.error("Authentication error:",e),null}}})],callbacks:{jwt:async({token:e,user:r})=>(r&&(e.role=r.role,e.id=r.id),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.id,e.user.role=r.role),e),async authorized({auth:e,request:{nextUrl:r}}){let t=!!e?.user,s=r.pathname.startsWith("/dashboard"),a=r.pathname.startsWith("/auth");return s?!!t:!a||!t||Response.redirect(new URL("/dashboard",r))}},events:{async signOut(e){let r="token"in e&&e.token?.id?e.token.id:"session"in e&&e.session?.user?.id?e.session.user.id:null;r&&await i.z.auditLog.create({data:{userId:r,action:"LOGOUT",resourceType:"AUTH",resourceId:r,newValues:{timestamp:new Date},ipAddress:"127.0.0.1",userAgent:"Unknown"}})}}}),{GET:p,POST:m}=u},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5069:(e,r,t)=>{"use strict";t.d(r,{db:()=>n,z:()=>a});var s=t(6330);let a=globalThis.prisma??new s.PrismaClient({log:["query","error","warn"]}),n=a},5511:e=>{"use strict";e.exports=require("crypto")},6089:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>w,serverHooks:()=>h,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>y});var s={};t.r(s),t.d(s,{GET:()=>m,POST:()=>g});var a=t(6559),n=t(8088),i=t(7719),o=t(2190),u=t(2909),d=t(5069),c=t(6330),l=t(5697);let p=l.z.object({courseId:l.z.string().min(1,"Course ID is required"),feeType:l.z.nativeEnum(c.FeeType),amount:l.z.number().positive("Amount must be positive"),currency:l.z.string().length(3,"Currency must be 3 characters").default("USD"),effectiveDate:l.z.string().transform(e=>new Date(e)),expiryDate:l.z.string().transform(e=>new Date(e)).optional()});async function m(e){try{let r=await (0,u.j2)();if(!r?.user)return o.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:t}=new URL(e.url),s=parseInt(t.get("page")||"1"),a=parseInt(t.get("limit")||"10"),n=t.get("courseId"),i=t.get("feeType"),c=t.get("isActive"),l=(s-1)*a,p={};n&&(p.courseId=n),i&&(p.feeType=i),null!==c&&(p.isActive="true"===c);let[m,g]=await Promise.all([d.z.feeStructure.findMany({where:p,skip:l,take:a,include:{createdByUser:{select:{firstName:!0,lastName:!0,email:!0}}},orderBy:{createdAt:"desc"}}),d.z.feeStructure.count({where:p})]);return await d.z.auditLog.create({data:{userId:r.user.id,action:"VIEW_FEE_STRUCTURES",resourceType:"FEE_STRUCTURE",newValues:{page:s,limit:a,filters:{courseId:n,feeType:i,isActive:c}},ipAddress:"127.0.0.1",userAgent:e.headers.get("user-agent")||"Unknown"}}),o.NextResponse.json({success:!0,data:{feeStructures:m,pagination:{page:s,limit:a,total:g,pages:Math.ceil(g/a)}}})}catch(e){return console.error("Error fetching fee structures:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}async function g(e){try{let r=await (0,u.j2)();if(!r?.user)return o.NextResponse.json({error:"Unauthorized"},{status:401});if(!["admin","accounting"].includes(r.user.role))return o.NextResponse.json({error:"Forbidden"},{status:403});let t=await e.json(),s=p.parse(t),a=await d.z.feeStructure.create({data:{...s,createdBy:r.user.id},include:{createdByUser:{select:{firstName:!0,lastName:!0,email:!0}}}});return await d.z.auditLog.create({data:{userId:r.user.id,action:"CREATE_FEE_STRUCTURE",resourceType:"FEE_STRUCTURE",resourceId:a.id,newValues:s,ipAddress:"127.0.0.1",userAgent:e.headers.get("user-agent")||"Unknown"}}),o.NextResponse.json({success:!0,data:a})}catch(e){if(e instanceof l.z.ZodError)return o.NextResponse.json({error:"Validation error",details:e.errors},{status:400});return console.error("Error creating fee structure:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}l.z.object({amount:l.z.number().positive().optional(),currency:l.z.string().length(3).optional(),effectiveDate:l.z.string().transform(e=>new Date(e)).optional(),expiryDate:l.z.string().transform(e=>new Date(e)).optional(),isActive:l.z.boolean().optional()});let w=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/fee-structures/route",pathname:"/api/fee-structures",filename:"route",bundlePath:"app/api/fee-structures/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-admin-service\\src\\app\\api\\fee-structures\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:y,serverHooks:h}=w;function x(){return(0,i.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:y})}},6330:e=>{"use strict";e.exports=require("@prisma/client")},6487:()=>{},8335:()=>{},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,199,828,697],()=>t(6089));module.exports=s})();