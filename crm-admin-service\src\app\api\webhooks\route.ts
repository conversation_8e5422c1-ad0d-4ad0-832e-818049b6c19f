import { NextRequest, NextResponse } from 'next/server';
import { withServiceAuth } from '@/lib/service-auth';
import { createWebhookManager, createDataSyncManager, ServiceClientFactory } from 'crm-shared-types';

// Initialize service client factory
const serviceClientFactory = new ServiceClientFactory({
  adminServiceUrl: process.env.ADMIN_SERVICE_URL || 'http://localhost:3001',
  staffServiceUrl: process.env.STAFF_SERVICE_URL || 'http://localhost:3002',
  studentServiceUrl: process.env.STUDENT_SERVICE_URL || 'http://localhost:3003',
  serviceName: 'crm-admin-service',
  apiKey: process.env.SERVICE_API_KEY || 'default-api-key',
});

// Initialize webhook manager
const syncManager = createDataSyncManager(serviceClientFactory);
const webhookManager = createWebhookManager(serviceClientFactory, syncManager);

// Register webhook subscriptions for admin service
webhookManager.registerWebhook({
  url: `${process.env.ADMIN_SERVICE_URL}/api/webhooks/receive`,
  events: [
    'lead.created',
    'lead.updated',
    'student.created',
    'student.updated',
    'course.created',
    'course.updated',
  ],
  service: 'crm-admin-service',
  isActive: true,
  secret: process.env.WEBHOOK_SECRET,
});

export const POST = withServiceAuth(async (request: NextRequest) => {
  try {
    const body = await request.json();
    const { type, action, entityId, data } = body;

    // Emit webhook event for inter-service communication
    await webhookManager.emitEvent({
      type: `${type}.${action}`,
      source: 'crm-admin-service',
      data: {
        id: entityId,
        ...data,
      },
    });

    return NextResponse.json({
      success: true,
      message: 'Webhook event emitted successfully',
    });
  } catch (error) {
    console.error('Webhook emission failed:', error);
    return NextResponse.json({
      success: false,
      error: {
        code: 'WEBHOOK_FAILED',
        message: 'Failed to emit webhook event',
      },
    }, { status: 500 });
  }
});

export const GET = withServiceAuth(async () => {
  try {
    const stats = webhookManager.getWebhookStats();
    const subscriptions = webhookManager.listSubscriptions();
    const recentDeliveries = webhookManager.listRecentDeliveries(10);

    return NextResponse.json({
      success: true,
      data: {
        stats,
        subscriptions,
        recentDeliveries,
      },
    });
  } catch (error) {
    console.error('Failed to get webhook status:', error);
    return NextResponse.json({
      success: false,
      error: {
        code: 'WEBHOOK_STATUS_FAILED',
        message: 'Failed to get webhook status',
      },
    }, { status: 500 });
  }
});
