(()=>{var e={};e.id=513,e.ids=[513],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},864:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>p,serverHooks:()=>g,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{GET:()=>l});var a=t(6559),i=t(8088),n=t(7719),o=t(2190),u=t(2909),d=t(5069);async function l(e){try{let r=await (0,u.j2)();if(!r?.user)return o.NextResponse.json({error:"Unauthorized"},{status:401});if("admin"!==r.user.role)return o.NextResponse.json({error:"Forbidden"},{status:403});let{searchParams:t}=new URL(e.url),s=parseInt(t.get("page")||"1"),a=parseInt(t.get("limit")||"20"),i=t.get("userId"),n=t.get("action"),l=t.get("resourceType"),p=t.get("startDate"),c=t.get("endDate"),m=(s-1)*a,g={};i&&(g.userId=i),n&&(g.action={contains:n,mode:"insensitive"}),l&&(g.resourceType=l),(p||c)&&(g.timestamp={},p&&(g.timestamp.gte=new Date(p)),c&&(g.timestamp.lte=new Date(c)));let[w,y]=await Promise.all([d.z.auditLog.findMany({where:g,skip:m,take:a,include:{user:{select:{firstName:!0,lastName:!0,email:!0,role:!0}}},orderBy:{timestamp:"desc"}}),d.z.auditLog.count({where:g})]);return await d.z.auditLog.create({data:{userId:r.user.id,action:"VIEW_AUDIT_LOGS",resourceType:"AUDIT_LOG",newValues:{page:s,limit:a,filters:{userId:i,action:n,resourceType:l,startDate:p,endDate:c}},ipAddress:"127.0.0.1",userAgent:e.headers.get("user-agent")||"Unknown"}}),o.NextResponse.json({success:!0,data:{auditLogs:w,pagination:{page:s,limit:a,total:y,pages:Math.ceil(y/a)}}})}catch(e){return console.error("Error fetching audit logs:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/audit-logs/route",pathname:"/api/audit-logs",filename:"route",bundlePath:"app/api/audit-logs/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-admin-service\\src\\app\\api\\audit-logs\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:m,serverHooks:g}=p;function w(){return(0,n.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:m})}},2909:(e,r,t)=>{"use strict";t.d(r,{Y9:()=>u,j2:()=>d});var s=t(8643),a=t(189),i=t(6467),n=t(5069),o=t(5663);let{handlers:u,auth:d,signIn:l,signOut:p}=(0,s.Ay)({adapter:(0,i.y)(n.z),session:{strategy:"jwt",maxAge:1800},pages:{signIn:"/auth/signin",error:"/auth/error"},providers:[(0,a.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"},mfaCode:{label:"MFA Code",type:"text"}},async authorize(e){if(!e?.email||!e?.password)return null;try{let r=await n.z.adminUser.findUnique({where:{email:e.email}});if(!r||!r.isActive||!await o.Ay.compare(e.password,r.passwordHash))return null;return r.mfaEnabled&&!e.mfaCode&&console.warn("MFA required but not implemented yet"),await n.z.adminUser.update({where:{id:r.id},data:{lastLogin:new Date}}),await n.z.auditLog.create({data:{userId:r.id,action:"LOGIN",resourceType:"AUTH",resourceId:r.id,newValues:{timestamp:new Date,success:!0},ipAddress:"127.0.0.1",userAgent:"Unknown"}}),{id:r.id,email:r.email,name:`${r.firstName} ${r.lastName}`,role:r.role}}catch(e){return console.error("Authentication error:",e),null}}})],callbacks:{jwt:async({token:e,user:r})=>(r&&(e.role=r.role,e.id=r.id),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.id,e.user.role=r.role),e),async authorized({auth:e,request:{nextUrl:r}}){let t=!!e?.user,s=r.pathname.startsWith("/dashboard"),a=r.pathname.startsWith("/auth");return s?!!t:!a||!t||Response.redirect(new URL("/dashboard",r))}},events:{async signOut(e){let r="token"in e&&e.token?.id?e.token.id:"session"in e&&e.session?.user?.id?e.session.user.id:null;r&&await n.z.auditLog.create({data:{userId:r,action:"LOGOUT",resourceType:"AUTH",resourceId:r,newValues:{timestamp:new Date},ipAddress:"127.0.0.1",userAgent:"Unknown"}})}}}),{GET:c,POST:m}=u},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5069:(e,r,t)=>{"use strict";t.d(r,{db:()=>i,z:()=>a});var s=t(6330);let a=globalThis.prisma??new s.PrismaClient({log:["query","error","warn"]}),i=a},5511:e=>{"use strict";e.exports=require("crypto")},6330:e=>{"use strict";e.exports=require("@prisma/client")},6487:()=>{},8335:()=>{},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,199,828],()=>t(864));module.exports=s})();