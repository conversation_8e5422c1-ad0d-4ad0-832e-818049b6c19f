"use client";

import { useSession, signOut } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { 
  Users, 
  BookOpen, 
  UserCheck, 
  Calendar, 
  TrendingUp, 
  LogOut,
  Plus,
  Eye
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

interface DashboardStats {
  totalLeads: number;
  activeLeads: number;
  totalCourses: number;
  totalTeachers: number;
  totalGroups: number;
  leadsByStatus: Record<string, number>;
  recentLeads: Array<{
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    status: string;
    source: string;
    assignedTo: string;
    createdAt: string;
  }>;
  recentCourses: Array<{
    id: string;
    title: string;
    level: string;
    duration: number;
    price: number;
    description: string;
  }>;
}

export default function StaffDashboard() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (session) {
      fetchDashboardStats();
    }
  }, [session]);

  const fetchDashboardStats = async () => {
    try {
      const response = await fetch('/api/dashboard/stats');
      const data = await response.json();

      if (data.success) {
        setStats(data.data);
      }
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
    } finally {
      setLoading(false);
    }
  };

  if (status === "loading" || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  if (!session) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-gray-600">Please sign in to access the staff dashboard.</p>
        </div>
      </div>
    );
  }

  const handleSignOut = () => {
    signOut({ callbackUrl: "/auth/signin" });
  };

  const getRoleDisplayName = (role: string) => {
    switch (role) {
      case "reception": return "Reception";
      case "manager": return "Manager";
      case "test_checker": return "Test Checker";
      case "teacher": return "Teacher";
      default: return role;
    }
  };

  return (
    <div className="p-8">
      {/* Header */}
      <div className="mb-8 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Staff Dashboard</h1>
          <p className="text-gray-600">Lead management, course catalog, and teacher tools</p>
          <p className="text-sm text-gray-500 mt-1">
            Welcome back, {session.user?.name} ({getRoleDisplayName(session.user?.role)})
            {session.user?.employeeId && ` - ID: ${session.user.employeeId}`}
          </p>
        </div>
        <Button onClick={handleSignOut} variant="outline" className="flex items-center gap-2">
          <LogOut className="h-4 w-4" />
          Sign Out
        </Button>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Leads</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats ? stats.activeLeads : 0}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <BookOpen className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Courses</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats ? stats.totalCourses : 0}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <UserCheck className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Teachers</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats ? stats.totalTeachers : 0}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Calendar className="h-8 w-8 text-orange-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Groups</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats ? stats.totalGroups : 0}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Features */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Lead Management */}
        {["reception", "manager"].includes(session.user?.role) && (
          <Card>
            <CardHeader>
              <CardTitle>Lead Management</CardTitle>
              <CardDescription>Capture and manage potential students</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Button 
                  variant="outline" 
                  className="w-full justify-start"
                  onClick={() => router.push("/dashboard/leads")}
                >
                  <div className="text-left">
                    <div className="font-medium">View All Leads</div>
                    <div className="text-sm text-gray-600">Manage and track lead progress</div>
                  </div>
                </Button>
                <Button 
                  variant="outline" 
                  className="w-full justify-start"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  <div className="text-left">
                    <div className="font-medium">Add New Lead</div>
                    <div className="text-sm text-gray-600">Capture new potential student</div>
                  </div>
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Course Management */}
        {["manager", "teacher"].includes(session.user?.role) && (
          <Card>
            <CardHeader>
              <CardTitle>Course Management</CardTitle>
              <CardDescription>Manage courses and curriculum</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Button 
                  variant="outline" 
                  className="w-full justify-start"
                  onClick={() => router.push("/dashboard/courses")}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  <div className="text-left">
                    <div className="font-medium">View All Courses</div>
                    <div className="text-sm text-gray-600">Browse course catalog</div>
                  </div>
                </Button>
                {session.user?.role === "manager" && (
                  <Button 
                    variant="outline" 
                    className="w-full justify-start"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    <div className="text-left">
                      <div className="font-medium">Create New Course</div>
                      <div className="text-sm text-gray-600">Add course to catalog</div>
                    </div>
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Group Management */}
        {["manager", "teacher"].includes(session.user?.role) && (
          <Card>
            <CardHeader>
              <CardTitle>Group Management</CardTitle>
              <CardDescription>Organize students into learning groups</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Button 
                  variant="outline" 
                  className="w-full justify-start"
                  onClick={() => router.push("/dashboard/groups")}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  <div className="text-left">
                    <div className="font-medium">View All Groups</div>
                    <div className="text-sm text-gray-600">Manage student groups</div>
                  </div>
                </Button>
                {session.user?.role === "manager" && (
                  <Button 
                    variant="outline" 
                    className="w-full justify-start"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    <div className="text-left">
                      <div className="font-medium">Create New Group</div>
                      <div className="text-sm text-gray-600">Form new learning group</div>
                    </div>
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Teacher Management */}
        {session.user?.role === "manager" && (
          <Card>
            <CardHeader>
              <CardTitle>Teacher Management</CardTitle>
              <CardDescription>Manage teaching staff and performance</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Button 
                  variant="outline" 
                  className="w-full justify-start"
                  onClick={() => router.push("/dashboard/teachers")}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  <div className="text-left">
                    <div className="font-medium">View All Teachers</div>
                    <div className="text-sm text-gray-600">Manage teaching staff</div>
                  </div>
                </Button>
                <Button 
                  variant="outline" 
                  className="w-full justify-start"
                >
                  <TrendingUp className="h-4 w-4 mr-2" />
                  <div className="text-left">
                    <div className="font-medium">Teacher KPIs</div>
                    <div className="text-sm text-gray-600">View performance metrics</div>
                  </div>
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Teacher Dashboard */}
        {session.user?.role === "teacher" && (
          <Card>
            <CardHeader>
              <CardTitle>My Teaching</CardTitle>
              <CardDescription>Your classes and performance</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Button 
                  variant="outline" 
                  className="w-full justify-start"
                >
                  <Calendar className="h-4 w-4 mr-2" />
                  <div className="text-left">
                    <div className="font-medium">My Classes</div>
                    <div className="text-sm text-gray-600">View assigned groups</div>
                  </div>
                </Button>
                <Button 
                  variant="outline" 
                  className="w-full justify-start"
                >
                  <TrendingUp className="h-4 w-4 mr-2" />
                  <div className="text-left">
                    <div className="font-medium">My Performance</div>
                    <div className="text-sm text-gray-600">View KPI metrics</div>
                  </div>
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
