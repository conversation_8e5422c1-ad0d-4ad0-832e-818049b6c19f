// NextAuth.js v5 Configuration for Staff Service

import NextAuth from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import { PrismaAdapter } from "@auth/prisma-adapter";
import { prisma } from "./db";
import bcrypt from "bcryptjs";
import { StaffRole } from "@prisma/client";

const nextAuth = NextAuth({
  adapter: PrismaAdapter(prisma),
  session: {
    strategy: "jwt",
    maxAge: 8 * 60 * 60, // 8 hours for staff users
  },
  pages: {
    signIn: "/auth/signin",
    error: "/auth/error",
  },
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          // Find staff user
          const staffUser = await prisma.staffUser.findUnique({
            where: { email: credentials.email as string },
            include: {
              teacher: true, // Include teacher info if user is a teacher
            },
          });

          if (!staffUser || !staffUser.isActive) {
            return null;
          }

          // Verify password
          const isValidPassword = await bcrypt.compare(
            credentials.password as string,
            staffUser.passwordHash
          );

          if (!isValidPassword) {
            return null;
          }

          return {
            id: staffUser.id,
            email: staffUser.email,
            name: `${staffUser.firstName} ${staffUser.lastName}`,
            role: staffUser.role,
            employeeId: staffUser.employeeId,
            department: staffUser.department,
            isTeacher: !!staffUser.teacher,
            teacherId: staffUser.teacher?.id,
          };
        } catch (error) {
          console.error("Authentication error:", error);
          return null;
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role;
        token.id = user.id;
        token.employeeId = user.employeeId;
        token.department = user.department;
        token.isTeacher = user.isTeacher;
        token.teacherId = user.teacherId;
      }
      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id as string;
        session.user.role = token.role as StaffRole;
        session.user.employeeId = token.employeeId as string;
        session.user.department = token.department as string;
        session.user.isTeacher = token.isTeacher as boolean;
        session.user.teacherId = token.teacherId as string;
      }
      return session;
    },
    async authorized({ auth, request: { nextUrl } }) {
      const isLoggedIn = !!auth?.user;
      const isOnDashboard = nextUrl.pathname.startsWith("/dashboard");
      const isOnAuth = nextUrl.pathname.startsWith("/auth");

      if (isOnDashboard) {
        if (isLoggedIn) return true;
        return false; // Redirect unauthenticated users to login page
      } else if (isOnAuth) {
        if (isLoggedIn) return Response.redirect(new URL("/dashboard", nextUrl));
        return true;
      }

      return true;
    },
  },
});

export const { handlers, auth, signIn, signOut } = nextAuth;
export const { GET, POST } = handlers;
