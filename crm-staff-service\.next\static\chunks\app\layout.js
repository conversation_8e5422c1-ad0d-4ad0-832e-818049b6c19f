/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/layout"],{

/***/ "(app-pages-browser)/./node_modules/@auth/core/errors.js":
/*!*******************************************!*\
  !*** ./node_modules/@auth/core/errors.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AccessDenied: () => (/* binding */ AccessDenied),\n/* harmony export */   AccountNotLinked: () => (/* binding */ AccountNotLinked),\n/* harmony export */   AdapterError: () => (/* binding */ AdapterError),\n/* harmony export */   AuthError: () => (/* binding */ AuthError),\n/* harmony export */   CallbackRouteError: () => (/* binding */ CallbackRouteError),\n/* harmony export */   CredentialsSignin: () => (/* binding */ CredentialsSignin),\n/* harmony export */   DuplicateConditionalUI: () => (/* binding */ DuplicateConditionalUI),\n/* harmony export */   EmailSignInError: () => (/* binding */ EmailSignInError),\n/* harmony export */   ErrorPageLoop: () => (/* binding */ ErrorPageLoop),\n/* harmony export */   EventError: () => (/* binding */ EventError),\n/* harmony export */   ExperimentalFeatureNotEnabled: () => (/* binding */ ExperimentalFeatureNotEnabled),\n/* harmony export */   InvalidCallbackUrl: () => (/* binding */ InvalidCallbackUrl),\n/* harmony export */   InvalidCheck: () => (/* binding */ InvalidCheck),\n/* harmony export */   InvalidEndpoints: () => (/* binding */ InvalidEndpoints),\n/* harmony export */   InvalidProvider: () => (/* binding */ InvalidProvider),\n/* harmony export */   JWTSessionError: () => (/* binding */ JWTSessionError),\n/* harmony export */   MissingAdapter: () => (/* binding */ MissingAdapter),\n/* harmony export */   MissingAdapterMethods: () => (/* binding */ MissingAdapterMethods),\n/* harmony export */   MissingAuthorize: () => (/* binding */ MissingAuthorize),\n/* harmony export */   MissingCSRF: () => (/* binding */ MissingCSRF),\n/* harmony export */   MissingSecret: () => (/* binding */ MissingSecret),\n/* harmony export */   MissingWebAuthnAutocomplete: () => (/* binding */ MissingWebAuthnAutocomplete),\n/* harmony export */   OAuthAccountNotLinked: () => (/* binding */ OAuthAccountNotLinked),\n/* harmony export */   OAuthCallbackError: () => (/* binding */ OAuthCallbackError),\n/* harmony export */   OAuthProfileParseError: () => (/* binding */ OAuthProfileParseError),\n/* harmony export */   OAuthSignInError: () => (/* binding */ OAuthSignInError),\n/* harmony export */   SessionTokenError: () => (/* binding */ SessionTokenError),\n/* harmony export */   SignInError: () => (/* binding */ SignInError),\n/* harmony export */   SignOutError: () => (/* binding */ SignOutError),\n/* harmony export */   UnknownAction: () => (/* binding */ UnknownAction),\n/* harmony export */   UnsupportedStrategy: () => (/* binding */ UnsupportedStrategy),\n/* harmony export */   UntrustedHost: () => (/* binding */ UntrustedHost),\n/* harmony export */   Verification: () => (/* binding */ Verification),\n/* harmony export */   WebAuthnVerificationError: () => (/* binding */ WebAuthnVerificationError),\n/* harmony export */   isClientError: () => (/* binding */ isClientError)\n/* harmony export */ });\n/**\n * Base error class for all Auth.js errors.\n * It's optimized to be printed in the server logs in a nicely formatted way\n * via the [`logger.error`](https://authjs.dev/reference/core#logger) option.\n * @noInheritDoc\n */\nclass AuthError extends Error {\n    /** @internal */\n    constructor(message, errorOptions) {\n        if (message instanceof Error) {\n            super(undefined, {\n                cause: { err: message, ...message.cause, ...errorOptions },\n            });\n        }\n        else if (typeof message === \"string\") {\n            if (errorOptions instanceof Error) {\n                errorOptions = { err: errorOptions, ...errorOptions.cause };\n            }\n            super(message, errorOptions);\n        }\n        else {\n            super(undefined, message);\n        }\n        this.name = this.constructor.name;\n        // @ts-expect-error https://github.com/microsoft/TypeScript/issues/3841\n        this.type = this.constructor.type ?? \"AuthError\";\n        // @ts-expect-error https://github.com/microsoft/TypeScript/issues/3841\n        this.kind = this.constructor.kind ?? \"error\";\n        Error.captureStackTrace?.(this, this.constructor);\n        const url = `https://errors.authjs.dev#${this.type.toLowerCase()}`;\n        this.message += `${this.message ? \". \" : \"\"}Read more at ${url}`;\n    }\n}\n/**\n * Thrown when the user's sign-in attempt failed.\n * @noInheritDoc\n */\nclass SignInError extends AuthError {\n}\n/** @internal */\nSignInError.kind = \"signIn\";\n/**\n * One of the database [`Adapter` methods](https://authjs.dev/reference/core/adapters#methods)\n * failed during execution.\n *\n * :::tip\n * If `debug: true` is set, you can check out `[auth][debug]` in the logs to learn more about the failed adapter method execution.\n * @example\n * ```sh\n * [auth][debug]: adapter_getUserByEmail\n * { \"args\": [undefined] }\n * ```\n * :::\n * @noInheritDoc\n */\nclass AdapterError extends AuthError {\n}\nAdapterError.type = \"AdapterError\";\n/**\n * Thrown when the execution of the [`signIn` callback](https://authjs.dev/reference/core/types#signin) fails\n * or if it returns `false`.\n * @noInheritDoc\n */\nclass AccessDenied extends AuthError {\n}\nAccessDenied.type = \"AccessDenied\";\n/**\n * This error occurs when the user cannot finish login.\n * Depending on the provider type, this could have happened for multiple reasons.\n *\n * :::tip\n * Check out `[auth][details]` in the logs to know which provider failed.\n * @example\n * ```sh\n * [auth][details]: { \"provider\": \"github\" }\n * ```\n * :::\n *\n * For an [OAuth provider](https://authjs.dev/getting-started/authentication/oauth), possible causes are:\n * - The user denied access to the application\n * - There was an error parsing the OAuth Profile:\n *   Check out the provider's `profile` or `userinfo.request` method to make sure\n *   it correctly fetches the user's profile.\n * - The `signIn` or `jwt` callback methods threw an uncaught error:\n *   Check the callback method implementations.\n *\n * For an [Email provider](https://authjs.dev/getting-started/authentication/email), possible causes are:\n * - The provided email/token combination was invalid/missing:\n *   Check if the provider's `sendVerificationRequest` method correctly sends the email.\n * - The provided email/token combination has expired:\n *   Ask the user to log in again.\n * - There was an error with the database:\n *   Check the database logs.\n *\n * For a [Credentials provider](https://authjs.dev/getting-started/authentication/credentials), possible causes are:\n * - The `authorize` method threw an uncaught error:\n *   Check the provider's `authorize` method.\n * - The `signIn` or `jwt` callback methods threw an uncaught error:\n *   Check the callback method implementations.\n *\n * :::tip\n * Check out `[auth][cause]` in the error message for more details.\n * It will show the original stack trace.\n * :::\n * @noInheritDoc\n */\nclass CallbackRouteError extends AuthError {\n}\nCallbackRouteError.type = \"CallbackRouteError\";\n/**\n * Thrown when Auth.js is misconfigured and accidentally tried to require authentication on a custom error page.\n * To prevent an infinite loop, Auth.js will instead render its default error page.\n *\n * To fix this, make sure that the `error` page does not require authentication.\n *\n * Learn more at [Guide: Error pages](https://authjs.dev/guides/pages/error)\n * @noInheritDoc\n */\nclass ErrorPageLoop extends AuthError {\n}\nErrorPageLoop.type = \"ErrorPageLoop\";\n/**\n * One of the [`events` methods](https://authjs.dev/reference/core/types#eventcallbacks)\n * failed during execution.\n *\n * Make sure that the `events` methods are implemented correctly and uncaught errors are handled.\n *\n * Learn more at [`events`](https://authjs.dev/reference/core/types#eventcallbacks)\n * @noInheritDoc\n */\nclass EventError extends AuthError {\n}\nEventError.type = \"EventError\";\n/**\n * Thrown when Auth.js is unable to verify a `callbackUrl` value.\n * The browser either disabled cookies or the `callbackUrl` is not a valid URL.\n *\n * Somebody might have tried to manipulate the callback URL that Auth.js uses to redirect the user back to the configured `callbackUrl`/page.\n * This could be a malicious hacker trying to redirect the user to a phishing site.\n * To prevent this, Auth.js checks if the callback URL is valid and throws this error if it is not.\n *\n * There is no action required, but it might be an indicator that somebody is trying to attack your application.\n * @noInheritDoc\n */\nclass InvalidCallbackUrl extends AuthError {\n}\nInvalidCallbackUrl.type = \"InvalidCallbackUrl\";\n/**\n * Can be thrown from the `authorize` callback of the Credentials provider.\n * When an error occurs during the `authorize` callback, two things can happen:\n * 1. The user is redirected to the signin page, with `error=CredentialsSignin&code=credentials` in the URL. `code` is configurable.\n * 2. If you throw this error in a framework that handles form actions server-side, this error is thrown, instead of redirecting the user, so you'll need to handle.\n * @noInheritDoc\n */\nclass CredentialsSignin extends SignInError {\n    constructor() {\n        super(...arguments);\n        /**\n         * The error code that is set in the `code` query parameter of the redirect URL.\n         *\n         *\n         * ⚠ NOTE: This property is going to be included in the URL, so make sure it does not hint at sensitive errors.\n         *\n         * The full error is always logged on the server, if you need to debug.\n         *\n         * Generally, we don't recommend hinting specifically if the user had either a wrong username or password specifically,\n         * try rather something like \"Invalid credentials\".\n         */\n        this.code = \"credentials\";\n    }\n}\nCredentialsSignin.type = \"CredentialsSignin\";\n/**\n * One of the configured OAuth or OIDC providers is missing the `authorization`, `token` or `userinfo`, or `issuer` configuration.\n * To perform OAuth or OIDC sign in, at least one of these endpoints is required.\n *\n * Learn more at [`OAuth2Config`](https://authjs.dev/reference/core/providers#oauth2configprofile) or [Guide: OAuth Provider](https://authjs.dev/guides/configuring-oauth-providers)\n * @noInheritDoc\n */\nclass InvalidEndpoints extends AuthError {\n}\nInvalidEndpoints.type = \"InvalidEndpoints\";\n/**\n * Thrown when a PKCE, state or nonce OAuth check could not be performed.\n * This could happen if the OAuth provider is configured incorrectly or if the browser is blocking cookies.\n *\n * Learn more at [`checks`](https://authjs.dev/reference/core/providers#checks)\n * @noInheritDoc\n */\nclass InvalidCheck extends AuthError {\n}\nInvalidCheck.type = \"InvalidCheck\";\n/**\n * Logged on the server when Auth.js could not decode or encode a JWT-based (`strategy: \"jwt\"`) session.\n *\n * Possible causes are either a misconfigured `secret` or a malformed JWT or `encode/decode` methods.\n *\n * :::note\n * When this error is logged, the session cookie is destroyed.\n * :::\n *\n * Learn more at [`secret`](https://authjs.dev/reference/core#secret), [`jwt.encode`](https://authjs.dev/reference/core/jwt#encode-1) or [`jwt.decode`](https://authjs.dev/reference/core/jwt#decode-2) for more information.\n * @noInheritDoc\n */\nclass JWTSessionError extends AuthError {\n}\nJWTSessionError.type = \"JWTSessionError\";\n/**\n * Thrown if Auth.js is misconfigured. This could happen if you configured an Email provider but did not set up a database adapter,\n * or tried using a `strategy: \"database\"` session without a database adapter.\n * In both cases, make sure you either remove the configuration or add the missing adapter.\n *\n * Learn more at [Database Adapters](https://authjs.dev/getting-started/database), [Email provider](https://authjs.dev/getting-started/authentication/email) or [Concept: Database session strategy](https://authjs.dev/concepts/session-strategies#database-session)\n * @noInheritDoc\n */\nclass MissingAdapter extends AuthError {\n}\nMissingAdapter.type = \"MissingAdapter\";\n/**\n * Thrown similarily to [`MissingAdapter`](https://authjs.dev/reference/core/errors#missingadapter), but only some required methods were missing.\n *\n * Make sure you either remove the configuration or add the missing methods to the adapter.\n *\n * Learn more at [Database Adapters](https://authjs.dev/getting-started/database)\n * @noInheritDoc\n */\nclass MissingAdapterMethods extends AuthError {\n}\nMissingAdapterMethods.type = \"MissingAdapterMethods\";\n/**\n * Thrown when a Credentials provider is missing the `authorize` configuration.\n * To perform credentials sign in, the `authorize` method is required.\n *\n * Learn more at [Credentials provider](https://authjs.dev/getting-started/authentication/credentials)\n * @noInheritDoc\n */\nclass MissingAuthorize extends AuthError {\n}\nMissingAuthorize.type = \"MissingAuthorize\";\n/**\n * Auth.js requires a secret or multiple secrets to be set, but none was not found. This is used to encrypt cookies, JWTs and other sensitive data.\n *\n * :::note\n * If you are using a framework like Next.js, we try to automatically infer the secret from the `AUTH_SECRET`, `AUTH_SECRET_1`, etc. environment variables.\n * Alternatively, you can also explicitly set the [`AuthConfig.secret`](https://authjs.dev/reference/core#secret) option.\n * :::\n *\n *\n * :::tip\n * To generate a random string, you can use the Auth.js CLI: `npx auth secret`\n * :::\n * @noInheritDoc\n */\nclass MissingSecret extends AuthError {\n}\nMissingSecret.type = \"MissingSecret\";\n/**\n * Thrown when an Email address is already associated with an account\n * but the user is trying an OAuth account that is not linked to it.\n *\n * For security reasons, Auth.js does not automatically link OAuth accounts to existing accounts if the user is not signed in.\n *\n * :::tip\n * If you trust the OAuth provider to have verified the user's email address,\n * you can enable automatic account linking by setting [`allowDangerousEmailAccountLinking: true`](https://authjs.dev/reference/core/providers#allowdangerousemailaccountlinking)\n * in the provider configuration.\n * :::\n * @noInheritDoc\n */\nclass OAuthAccountNotLinked extends SignInError {\n}\nOAuthAccountNotLinked.type = \"OAuthAccountNotLinked\";\n/**\n * Thrown when an OAuth provider returns an error during the sign in process.\n * This could happen for example if the user denied access to the application or there was a configuration error.\n *\n * For a full list of possible reasons, check out the specification [Authorization Code Grant: Error Response](https://www.rfc-editor.org/rfc/rfc6749#section-*******)\n * @noInheritDoc\n */\nclass OAuthCallbackError extends SignInError {\n}\nOAuthCallbackError.type = \"OAuthCallbackError\";\n/**\n * This error occurs during an OAuth sign in attempt when the provider's\n * response could not be parsed. This could for example happen if the provider's API\n * changed, or the [`OAuth2Config.profile`](https://authjs.dev/reference/core/providers#oauth2configprofile) method is not implemented correctly.\n * @noInheritDoc\n */\nclass OAuthProfileParseError extends AuthError {\n}\nOAuthProfileParseError.type = \"OAuthProfileParseError\";\n/**\n * Logged on the server when Auth.js could not retrieve a session from the database (`strategy: \"database\"`).\n *\n * The database adapter might be misconfigured or the database is not reachable.\n *\n * Learn more at [Concept: Database session strategy](https://authjs.dev/concepts/session-strategies#database)\n * @noInheritDoc\n */\nclass SessionTokenError extends AuthError {\n}\nSessionTokenError.type = \"SessionTokenError\";\n/**\n * Happens when login by [OAuth](https://authjs.dev/getting-started/authentication/oauth) could not be started.\n *\n * Possible causes are:\n * - The Authorization Server is not compliant with the [OAuth 2.0](https://www.ietf.org/rfc/rfc6749.html) or the [OIDC](https://openid.net/specs/openid-connect-core-1_0.html) specification.\n *   Check the details in the error message.\n *\n * :::tip\n * Check out `[auth][details]` in the logs to know which provider failed.\n * @example\n * ```sh\n * [auth][details]: { \"provider\": \"github\" }\n * ```\n * :::\n * @noInheritDoc\n */\nclass OAuthSignInError extends SignInError {\n}\nOAuthSignInError.type = \"OAuthSignInError\";\n/**\n * Happens when the login by an [Email provider](https://authjs.dev/getting-started/authentication/email) could not be started.\n *\n * Possible causes are:\n * - The email sent from the client is invalid, could not be normalized by [`EmailConfig.normalizeIdentifier`](https://authjs.dev/reference/core/providers/email#normalizeidentifier)\n * - The provided email/token combination has expired:\n *   Ask the user to log in again.\n * - There was an error with the database:\n *   Check the database logs.\n * @noInheritDoc\n */\nclass EmailSignInError extends SignInError {\n}\nEmailSignInError.type = \"EmailSignInError\";\n/**\n * Represents an error that occurs during the sign-out process. This error\n * is logged when there are issues in terminating a user's session, either\n * by failing to delete the session from the database (in database session\n * strategies) or encountering issues during other parts of the sign-out\n * process, such as emitting sign-out events or clearing session cookies.\n *\n * The session cookie(s) are emptied even if this error is logged.\n * @noInheritDoc\n */\nclass SignOutError extends AuthError {\n}\nSignOutError.type = \"SignOutError\";\n/**\n * Auth.js was requested to handle an operation that it does not support.\n *\n * See [`AuthAction`](https://authjs.dev/reference/core/types#authaction) for the supported actions.\n * @noInheritDoc\n */\nclass UnknownAction extends AuthError {\n}\nUnknownAction.type = \"UnknownAction\";\n/**\n * Thrown when a Credentials provider is present but the JWT strategy (`strategy: \"jwt\"`) is not enabled.\n *\n * Learn more at [`strategy`](https://authjs.dev/reference/core#strategy) or [Credentials provider](https://authjs.dev/getting-started/authentication/credentials)\n * @noInheritDoc\n */\nclass UnsupportedStrategy extends AuthError {\n}\nUnsupportedStrategy.type = \"UnsupportedStrategy\";\n/**\n * Thrown when an endpoint was incorrectly called without a provider, or with an unsupported provider.\n * @noInheritDoc\n */\nclass InvalidProvider extends AuthError {\n}\nInvalidProvider.type = \"InvalidProvider\";\n/**\n * Thrown when the `trustHost` option was not set to `true`.\n *\n * Auth.js requires the `trustHost` option to be set to `true` since it's relying on the request headers' `host` value.\n *\n * :::note\n * Official Auth.js libraries might attempt to automatically set the `trustHost` option to `true` if the request is coming from a trusted host on a trusted platform.\n * :::\n *\n * Learn more at [`trustHost`](https://authjs.dev/reference/core#trusthost) or [Guide: Deployment](https://authjs.dev/getting-started/deployment)\n * @noInheritDoc\n */\nclass UntrustedHost extends AuthError {\n}\nUntrustedHost.type = \"UntrustedHost\";\n/**\n * The user's email/token combination was invalid.\n * This could be because the email/token combination was not found in the database,\n * or because the token has expired. Ask the user to log in again.\n * @noInheritDoc\n */\nclass Verification extends AuthError {\n}\nVerification.type = \"Verification\";\n/**\n * Error for missing CSRF tokens in client-side actions (`signIn`, `signOut`, `useSession#update`).\n * Thrown when actions lack the double submit cookie, essential for CSRF protection.\n *\n * CSRF ([Cross-Site Request Forgery](https://owasp.org/www-community/attacks/csrf))\n * is an attack leveraging authenticated user credentials for unauthorized actions.\n *\n * Double submit cookie pattern, a CSRF defense, requires matching values in a cookie\n * and request parameter. More on this at [MDN Web Docs](https://developer.mozilla.org/en-US/docs/Glossary/CSRF).\n * @noInheritDoc\n */\nclass MissingCSRF extends SignInError {\n}\nMissingCSRF.type = \"MissingCSRF\";\nconst clientErrors = new Set([\n    \"CredentialsSignin\",\n    \"OAuthAccountNotLinked\",\n    \"OAuthCallbackError\",\n    \"AccessDenied\",\n    \"Verification\",\n    \"MissingCSRF\",\n    \"AccountNotLinked\",\n    \"WebAuthnVerificationError\",\n]);\n/**\n * Used to only allow sending a certain subset of errors to the client.\n * Errors are always logged on the server, but to prevent leaking sensitive information,\n * only a subset of errors are sent to the client as-is.\n * @internal\n */\nfunction isClientError(error) {\n    if (error instanceof AuthError)\n        return clientErrors.has(error.type);\n    return false;\n}\n/**\n * Thrown when multiple providers have `enableConditionalUI` set to `true`.\n * Only one provider can have this option enabled at a time.\n * @noInheritDoc\n */\nclass DuplicateConditionalUI extends AuthError {\n}\nDuplicateConditionalUI.type = \"DuplicateConditionalUI\";\n/**\n * Thrown when a WebAuthn provider has `enableConditionalUI` set to `true` but no formField has `webauthn` in its autocomplete param.\n *\n * The `webauthn` autocomplete param is required for conditional UI to work.\n * @noInheritDoc\n */\nclass MissingWebAuthnAutocomplete extends AuthError {\n}\nMissingWebAuthnAutocomplete.type = \"MissingWebAuthnAutocomplete\";\n/**\n * Thrown when a WebAuthn provider fails to verify a client response.\n * @noInheritDoc\n */\nclass WebAuthnVerificationError extends AuthError {\n}\nWebAuthnVerificationError.type = \"WebAuthnVerificationError\";\n/**\n * Thrown when an Email address is already associated with an account\n * but the user is trying an account that is not linked to it.\n *\n * For security reasons, Auth.js does not automatically link accounts to existing accounts if the user is not signed in.\n * @noInheritDoc\n */\nclass AccountNotLinked extends SignInError {\n}\nAccountNotLinked.type = \"AccountNotLinked\";\n/**\n * Thrown when an experimental feature is used but not enabled.\n * @noInheritDoc\n */\nclass ExperimentalFeatureNotEnabled extends AuthError {\n}\nExperimentalFeatureNotEnabled.type = \"ExperimentalFeatureNotEnabled\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@auth/core/errors.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next-auth/lib/client.js":
/*!**********************************************!*\
  !*** ./node_modules/next-auth/lib/client.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClientSessionError: () => (/* binding */ ClientSessionError),\n/* harmony export */   apiBaseUrl: () => (/* binding */ apiBaseUrl),\n/* harmony export */   fetchData: () => (/* binding */ fetchData),\n/* harmony export */   now: () => (/* binding */ now),\n/* harmony export */   parseUrl: () => (/* binding */ parseUrl),\n/* harmony export */   useOnline: () => (/* binding */ useOnline)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _auth_core_errors__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @auth/core/errors */ \"(app-pages-browser)/./node_modules/@auth/core/errors.js\");\n/* __next_internal_client_entry_do_not_use__ ClientSessionError,fetchData,apiBaseUrl,useOnline,now,parseUrl auto */ var _s = $RefreshSig$();\n\n\n/** @todo */ class ClientFetchError extends _auth_core_errors__WEBPACK_IMPORTED_MODULE_1__.AuthError {\n}\n/** @todo */ class ClientSessionError extends _auth_core_errors__WEBPACK_IMPORTED_MODULE_1__.AuthError {\n}\n// ------------------------ Internal ------------------------\n/**\n * If passed 'appContext' via getInitialProps() in _app.js\n * then get the req object from ctx and use that for the\n * req value to allow `fetchData` to\n * work seemlessly in getInitialProps() on server side\n * pages *and* in _app.js.\n * @internal\n */ async function fetchData(path, __NEXTAUTH, logger) {\n    let req = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : {};\n    const url = \"\".concat(apiBaseUrl(__NEXTAUTH), \"/\").concat(path);\n    try {\n        var _req_headers;\n        const options = {\n            headers: {\n                \"Content-Type\": \"application/json\",\n                ...(req === null || req === void 0 ? void 0 : (_req_headers = req.headers) === null || _req_headers === void 0 ? void 0 : _req_headers.cookie) ? {\n                    cookie: req.headers.cookie\n                } : {}\n            }\n        };\n        if (req === null || req === void 0 ? void 0 : req.body) {\n            options.body = JSON.stringify(req.body);\n            options.method = \"POST\";\n        }\n        const res = await fetch(url, options);\n        const data = await res.json();\n        if (!res.ok) throw data;\n        return data;\n    } catch (error) {\n        logger.error(new ClientFetchError(error.message, error));\n        return null;\n    }\n}\n/** @internal */ function apiBaseUrl(__NEXTAUTH) {\n    if (false) {}\n    // Return relative path when called client side\n    return __NEXTAUTH.basePath;\n}\n/** @internal  */ function useOnline() {\n    _s();\n    const [isOnline, setIsOnline] = react__WEBPACK_IMPORTED_MODULE_0__.useState(typeof navigator !== \"undefined\" ? navigator.onLine : false);\n    const setOnline = ()=>setIsOnline(true);\n    const setOffline = ()=>setIsOnline(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useOnline.useEffect\": ()=>{\n            window.addEventListener(\"online\", setOnline);\n            window.addEventListener(\"offline\", setOffline);\n            return ({\n                \"useOnline.useEffect\": ()=>{\n                    window.removeEventListener(\"online\", setOnline);\n                    window.removeEventListener(\"offline\", setOffline);\n                }\n            })[\"useOnline.useEffect\"];\n        }\n    }[\"useOnline.useEffect\"], []);\n    return isOnline;\n}\n_s(useOnline, \"9TTTpdMr0LAEvNmxVj+grReKWwQ=\");\n/**\n * Returns the number of seconds elapsed since January 1, 1970 00:00:00 UTC.\n * @internal\n */ function now() {\n    return Math.floor(Date.now() / 1000);\n}\n/**\n * Returns an `URL` like object to make requests/redirects from server-side\n * @internal\n */ function parseUrl(url) {\n    const defaultUrl = new URL(\"http://localhost:3000/api/auth\");\n    if (url && !url.startsWith(\"http\")) {\n        url = \"https://\".concat(url);\n    }\n    const _url = new URL(url || defaultUrl);\n    const path = (_url.pathname === \"/\" ? defaultUrl.pathname : _url.pathname)// Remove trailing slash\n    .replace(/\\/$/, \"\");\n    const base = \"\".concat(_url.origin).concat(path);\n    return {\n        origin: _url.origin,\n        host: _url.host,\n        path,\n        base,\n        toString: ()=>base\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next-auth/lib/client.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next-auth/react.js":
/*!*****************************************!*\
  !*** ./node_modules/next-auth/react.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("var react__WEBPACK_IMPORTED_MODULE_1___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionContext: () => (/* binding */ SessionContext),\n/* harmony export */   SessionProvider: () => (/* binding */ SessionProvider),\n/* harmony export */   __NEXTAUTH: () => (/* binding */ __NEXTAUTH),\n/* harmony export */   getCsrfToken: () => (/* binding */ getCsrfToken),\n/* harmony export */   getProviders: () => (/* binding */ getProviders),\n/* harmony export */   getSession: () => (/* binding */ getSession),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut),\n/* harmony export */   useSession: () => (/* binding */ useSession)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _lib_client_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/client.js */ \"(app-pages-browser)/./node_modules/next-auth/lib/client.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/**\n *\n * NextAuth.js is the official integration of Auth.js for Next.js applications. It supports both\n * [Client Components](https://nextjs.org/docs/app/building-your-application/rendering/client-components) and the\n * [Pages Router](https://nextjs.org/docs/pages). It includes methods for signing in, signing out, hooks, and a React\n * Context provider to wrap your application and make session data available anywhere.\n *\n * For use in [Server Actions](https://nextjs.org/docs/app/api-reference/functions/server-actions), check out [these methods](https://authjs.dev/guides/upgrade-to-v5#methods)\n *\n * @module react\n */ /* __next_internal_client_entry_do_not_use__ __NEXTAUTH,SessionContext,useSession,getSession,getCsrfToken,getProviders,signIn,signOut,SessionProvider auto */ var _React_createContext;\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nvar _process_env_NEXTAUTH_URL, _process_env_NEXTAUTH_URL_INTERNAL, _ref, _process_env_NEXTAUTH_URL_INTERNAL1;\n// This behaviour mirrors the default behaviour for getting the site name that\n// happens server side in server/index.js\n// 1. An empty value is legitimate when the code is being invoked client side as\n//    relative URLs are valid in that context and so defaults to empty.\n// 2. When invoked server side the value is picked up from an environment\n//    variable and defaults to 'http://localhost:3000'.\nconst __NEXTAUTH = {\n    baseUrl: (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.parseUrl)((_process_env_NEXTAUTH_URL = process.env.NEXTAUTH_URL) !== null && _process_env_NEXTAUTH_URL !== void 0 ? _process_env_NEXTAUTH_URL : process.env.VERCEL_URL).origin,\n    basePath: (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.parseUrl)(process.env.NEXTAUTH_URL).path,\n    baseUrlServer: (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.parseUrl)((_ref = (_process_env_NEXTAUTH_URL_INTERNAL = process.env.NEXTAUTH_URL_INTERNAL) !== null && _process_env_NEXTAUTH_URL_INTERNAL !== void 0 ? _process_env_NEXTAUTH_URL_INTERNAL : process.env.NEXTAUTH_URL) !== null && _ref !== void 0 ? _ref : process.env.VERCEL_URL).origin,\n    basePathServer: (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.parseUrl)((_process_env_NEXTAUTH_URL_INTERNAL1 = process.env.NEXTAUTH_URL_INTERNAL) !== null && _process_env_NEXTAUTH_URL_INTERNAL1 !== void 0 ? _process_env_NEXTAUTH_URL_INTERNAL1 : process.env.NEXTAUTH_URL).path,\n    _lastSync: 0,\n    _session: undefined,\n    _getSession: ()=>{}\n};\nlet broadcastChannel = null;\nfunction getNewBroadcastChannel() {\n    return new BroadcastChannel(\"next-auth\");\n}\nfunction broadcast() {\n    if (typeof BroadcastChannel === \"undefined\") {\n        return {\n            postMessage: ()=>{},\n            addEventListener: ()=>{},\n            removeEventListener: ()=>{}\n        };\n    }\n    if (broadcastChannel === null) {\n        broadcastChannel = getNewBroadcastChannel();\n    }\n    return broadcastChannel;\n}\n// TODO:\nconst logger = {\n    debug: console.debug,\n    error: console.error,\n    warn: console.warn\n};\nconst SessionContext = (_React_createContext = react__WEBPACK_IMPORTED_MODULE_1__.createContext) === null || _React_createContext === void 0 ? void 0 : _React_createContext.call(/*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_1___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_1___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_1__, 2))), undefined);\n/**\n * React Hook that gives you access to the logged in user's session data and lets you modify it.\n *\n * :::info\n * `useSession` is for client-side use only and when using [Next.js App Router (`app/`)](https://nextjs.org/blog/next-13-4#nextjs-app-router) you should prefer the `auth()` export.\n * :::\n */ function useSession(options) {\n    _s();\n    if (!SessionContext) {\n        throw new Error(\"React Context is unavailable in Server Components\");\n    }\n    // @ts-expect-error Satisfy TS if branch on line below\n    const value = react__WEBPACK_IMPORTED_MODULE_1__.useContext(SessionContext);\n    if (!value && \"development\" !== \"production\") {\n        throw new Error(\"[next-auth]: `useSession` must be wrapped in a <SessionProvider />\");\n    }\n    const { required, onUnauthenticated } = options !== null && options !== void 0 ? options : {};\n    const requiredAndNotLoading = required && value.status === \"unauthenticated\";\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"useSession.useEffect\": ()=>{\n            if (requiredAndNotLoading) {\n                const url = \"\".concat(__NEXTAUTH.basePath, \"/signin?\").concat(new URLSearchParams({\n                    error: \"SessionRequired\",\n                    callbackUrl: window.location.href\n                }));\n                if (onUnauthenticated) onUnauthenticated();\n                else window.location.href = url;\n            }\n        }\n    }[\"useSession.useEffect\"], [\n        requiredAndNotLoading,\n        onUnauthenticated\n    ]);\n    if (requiredAndNotLoading) {\n        return {\n            data: value.data,\n            update: value.update,\n            status: \"loading\"\n        };\n    }\n    return value;\n}\n_s(useSession, \"fo5J65/TYWua//m8QE+oTrUC6Kk=\");\nasync function getSession(params) {\n    const session = await (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.fetchData)(\"session\", __NEXTAUTH, logger, params);\n    var _params_broadcast;\n    if ((_params_broadcast = params === null || params === void 0 ? void 0 : params.broadcast) !== null && _params_broadcast !== void 0 ? _params_broadcast : true) {\n        const broadcastChannel = getNewBroadcastChannel();\n        broadcastChannel.postMessage({\n            event: \"session\",\n            data: {\n                trigger: \"getSession\"\n            }\n        });\n    }\n    return session;\n}\n/**\n * Returns the current Cross-Site Request Forgery Token (CSRF Token)\n * required to make requests that changes state. (e.g. signing in or out, or updating the session).\n *\n * [CSRF Prevention: Double Submit Cookie](https://cheatsheetseries.owasp.org/cheatsheets/Cross-Site_Request_Forgery_Prevention_Cheat_Sheet.html#double-submit-cookie)\n */ async function getCsrfToken() {\n    const response = await (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.fetchData)(\"csrf\", __NEXTAUTH, logger);\n    var _response_csrfToken;\n    return (_response_csrfToken = response === null || response === void 0 ? void 0 : response.csrfToken) !== null && _response_csrfToken !== void 0 ? _response_csrfToken : \"\";\n}\nasync function getProviders() {\n    return (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.fetchData)(\"providers\", __NEXTAUTH, logger);\n}\nasync function signIn(provider, options, authorizationParams) {\n    const { callbackUrl, ...rest } = options !== null && options !== void 0 ? options : {};\n    const { redirect = true, redirectTo = callbackUrl !== null && callbackUrl !== void 0 ? callbackUrl : window.location.href, ...signInParams } = rest;\n    const baseUrl = (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.apiBaseUrl)(__NEXTAUTH);\n    const providers = await getProviders();\n    if (!providers) {\n        const url = \"\".concat(baseUrl, \"/error\");\n        window.location.href = url;\n        return; // TODO: Return error if `redirect: false`\n    }\n    if (!provider || !providers[provider]) {\n        const url = \"\".concat(baseUrl, \"/signin?\").concat(new URLSearchParams({\n            callbackUrl: redirectTo\n        }));\n        window.location.href = url;\n        return; // TODO: Return error if `redirect: false`\n    }\n    const providerType = providers[provider].type;\n    if (providerType === \"webauthn\") {\n        // TODO: Add docs link with explanation\n        throw new TypeError([\n            'Provider id \"'.concat(provider, '\" refers to a WebAuthn provider.'),\n            'Please use `import { signIn } from \"next-auth/webauthn\"` instead.'\n        ].join(\"\\n\"));\n    }\n    const signInUrl = \"\".concat(baseUrl, \"/\").concat(providerType === \"credentials\" ? \"callback\" : \"signin\", \"/\").concat(provider);\n    const csrfToken = await getCsrfToken();\n    const res = await fetch(\"\".concat(signInUrl, \"?\").concat(new URLSearchParams(authorizationParams)), {\n        method: \"post\",\n        headers: {\n            \"Content-Type\": \"application/x-www-form-urlencoded\",\n            \"X-Auth-Return-Redirect\": \"1\"\n        },\n        body: new URLSearchParams({\n            ...signInParams,\n            csrfToken,\n            callbackUrl: redirectTo\n        })\n    });\n    const data = await res.json();\n    if (redirect) {\n        var _data_url;\n        const url = (_data_url = data.url) !== null && _data_url !== void 0 ? _data_url : redirectTo;\n        window.location.href = url;\n        // If url contains a hash, the browser does not reload the page. We reload manually\n        if (url.includes(\"#\")) window.location.reload();\n        return;\n    }\n    var _searchParams_get;\n    const error = (_searchParams_get = new URL(data.url).searchParams.get(\"error\")) !== null && _searchParams_get !== void 0 ? _searchParams_get : undefined;\n    var _searchParams_get1;\n    const code = (_searchParams_get1 = new URL(data.url).searchParams.get(\"code\")) !== null && _searchParams_get1 !== void 0 ? _searchParams_get1 : undefined;\n    if (res.ok) {\n        await __NEXTAUTH._getSession({\n            event: \"storage\"\n        });\n    }\n    return {\n        error,\n        code,\n        status: res.status,\n        ok: res.ok,\n        url: error ? null : data.url\n    };\n}\nasync function signOut(options) {\n    var _options_callbackUrl;\n    const { redirect = true, redirectTo = (_options_callbackUrl = options === null || options === void 0 ? void 0 : options.callbackUrl) !== null && _options_callbackUrl !== void 0 ? _options_callbackUrl : window.location.href } = options !== null && options !== void 0 ? options : {};\n    const baseUrl = (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.apiBaseUrl)(__NEXTAUTH);\n    const csrfToken = await getCsrfToken();\n    const res = await fetch(\"\".concat(baseUrl, \"/signout\"), {\n        method: \"post\",\n        headers: {\n            \"Content-Type\": \"application/x-www-form-urlencoded\",\n            \"X-Auth-Return-Redirect\": \"1\"\n        },\n        body: new URLSearchParams({\n            csrfToken,\n            callbackUrl: redirectTo\n        })\n    });\n    const data = await res.json();\n    broadcast().postMessage({\n        event: \"session\",\n        data: {\n            trigger: \"signout\"\n        }\n    });\n    if (redirect) {\n        var _data_url;\n        const url = (_data_url = data.url) !== null && _data_url !== void 0 ? _data_url : redirectTo;\n        window.location.href = url;\n        // If url contains a hash, the browser does not reload the page. We reload manually\n        if (url.includes(\"#\")) window.location.reload();\n        return;\n    }\n    await __NEXTAUTH._getSession({\n        event: \"storage\"\n    });\n    return data;\n}\n/**\n * [React Context](https://react.dev/learn/passing-data-deeply-with-context) provider to wrap the app (`pages/`) to make session data available anywhere.\n *\n * When used, the session state is automatically synchronized across all open tabs/windows and they are all updated whenever they gain or lose focus\n * or the state changes (e.g. a user signs in or out) when {@link SessionProviderProps.refetchOnWindowFocus} is `true`.\n *\n * :::info\n * `SessionProvider` is for client-side use only and when using [Next.js App Router (`app/`)](https://nextjs.org/blog/next-13-4#nextjs-app-router) you should prefer the `auth()` export.\n * :::\n */ function SessionProvider(props) {\n    _s1();\n    if (!SessionContext) {\n        throw new Error(\"React Context is unavailable in Server Components\");\n    }\n    const { children, basePath, refetchInterval, refetchWhenOffline } = props;\n    if (basePath) __NEXTAUTH.basePath = basePath;\n    /**\n     * If session was `null`, there was an attempt to fetch it,\n     * but it failed, but we still treat it as a valid initial value.\n     */ const hasInitialSession = props.session !== undefined;\n    /** If session was passed, initialize as already synced */ __NEXTAUTH._lastSync = hasInitialSession ? (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.now)() : 0;\n    const [session, setSession] = react__WEBPACK_IMPORTED_MODULE_1__.useState({\n        \"SessionProvider.useState\": ()=>{\n            if (hasInitialSession) __NEXTAUTH._session = props.session;\n            return props.session;\n        }\n    }[\"SessionProvider.useState\"]);\n    /** If session was passed, initialize as not loading */ const [loading, setLoading] = react__WEBPACK_IMPORTED_MODULE_1__.useState(!hasInitialSession);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"SessionProvider.useEffect\": ()=>{\n            __NEXTAUTH._getSession = ({\n                \"SessionProvider.useEffect\": async function() {\n                    let { event } = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n                    try {\n                        const storageEvent = event === \"storage\";\n                        // We should always update if we don't have a client session yet\n                        // or if there are events from other tabs/windows\n                        if (storageEvent || __NEXTAUTH._session === undefined) {\n                            __NEXTAUTH._lastSync = (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.now)();\n                            __NEXTAUTH._session = await getSession({\n                                broadcast: !storageEvent\n                            });\n                            setSession(__NEXTAUTH._session);\n                            return;\n                        }\n                        if (// If there is no time defined for when a session should be considered\n                        // stale, then it's okay to use the value we have until an event is\n                        // triggered which updates it\n                        !event || // If the client doesn't have a session then we don't need to call\n                        // the server to check if it does (if they have signed in via another\n                        // tab or window that will come through as a \"stroage\" event\n                        // event anyway)\n                        __NEXTAUTH._session === null || // Bail out early if the client session is not stale yet\n                        (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.now)() < __NEXTAUTH._lastSync) {\n                            return;\n                        }\n                        // An event or session staleness occurred, update the client session.\n                        __NEXTAUTH._lastSync = (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.now)();\n                        __NEXTAUTH._session = await getSession();\n                        setSession(__NEXTAUTH._session);\n                    } catch (error) {\n                        logger.error(new _lib_client_js__WEBPACK_IMPORTED_MODULE_2__.ClientSessionError(error.message, error));\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            })[\"SessionProvider.useEffect\"];\n            __NEXTAUTH._getSession();\n            return ({\n                \"SessionProvider.useEffect\": ()=>{\n                    __NEXTAUTH._lastSync = 0;\n                    __NEXTAUTH._session = undefined;\n                    __NEXTAUTH._getSession = ({\n                        \"SessionProvider.useEffect\": ()=>{}\n                    })[\"SessionProvider.useEffect\"];\n                }\n            })[\"SessionProvider.useEffect\"];\n        }\n    }[\"SessionProvider.useEffect\"], []);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"SessionProvider.useEffect\": ()=>{\n            const handle = {\n                \"SessionProvider.useEffect.handle\": ()=>__NEXTAUTH._getSession({\n                        event: \"storage\"\n                    })\n            }[\"SessionProvider.useEffect.handle\"];\n            // Listen for storage events and update session if event fired from\n            // another window (but suppress firing another event to avoid a loop)\n            // Fetch new session data but tell it to not to fire another event to\n            // avoid an infinite loop.\n            // Note: We could pass session data through and do something like\n            // `setData(message.data)` but that can cause problems depending\n            // on how the session object is being used in the client; it is\n            // more robust to have each window/tab fetch it's own copy of the\n            // session object rather than share it across instances.\n            broadcast().addEventListener(\"message\", handle);\n            return ({\n                \"SessionProvider.useEffect\": ()=>broadcast().removeEventListener(\"message\", handle)\n            })[\"SessionProvider.useEffect\"];\n        }\n    }[\"SessionProvider.useEffect\"], []);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"SessionProvider.useEffect\": ()=>{\n            const { refetchOnWindowFocus = true } = props;\n            // Listen for when the page is visible, if the user switches tabs\n            // and makes our tab visible again, re-fetch the session, but only if\n            // this feature is not disabled.\n            const visibilityHandler = {\n                \"SessionProvider.useEffect.visibilityHandler\": ()=>{\n                    if (refetchOnWindowFocus && document.visibilityState === \"visible\") __NEXTAUTH._getSession({\n                        event: \"visibilitychange\"\n                    });\n                }\n            }[\"SessionProvider.useEffect.visibilityHandler\"];\n            document.addEventListener(\"visibilitychange\", visibilityHandler, false);\n            return ({\n                \"SessionProvider.useEffect\": ()=>document.removeEventListener(\"visibilitychange\", visibilityHandler, false)\n            })[\"SessionProvider.useEffect\"];\n        }\n    }[\"SessionProvider.useEffect\"], [\n        props.refetchOnWindowFocus\n    ]);\n    const isOnline = (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.useOnline)();\n    // TODO: Flip this behavior in next major version\n    const shouldRefetch = refetchWhenOffline !== false || isOnline;\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"SessionProvider.useEffect\": ()=>{\n            if (refetchInterval && shouldRefetch) {\n                const refetchIntervalTimer = setInterval({\n                    \"SessionProvider.useEffect.refetchIntervalTimer\": ()=>{\n                        if (__NEXTAUTH._session) {\n                            __NEXTAUTH._getSession({\n                                event: \"poll\"\n                            });\n                        }\n                    }\n                }[\"SessionProvider.useEffect.refetchIntervalTimer\"], refetchInterval * 1000);\n                return ({\n                    \"SessionProvider.useEffect\": ()=>clearInterval(refetchIntervalTimer)\n                })[\"SessionProvider.useEffect\"];\n            }\n        }\n    }[\"SessionProvider.useEffect\"], [\n        refetchInterval,\n        shouldRefetch\n    ]);\n    const value = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"SessionProvider.useMemo[value]\": ()=>({\n                data: session,\n                status: loading ? \"loading\" : session ? \"authenticated\" : \"unauthenticated\",\n                async update (data) {\n                    if (loading) return;\n                    setLoading(true);\n                    const newSession = await (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.fetchData)(\"session\", __NEXTAUTH, logger, typeof data === \"undefined\" ? undefined : {\n                        body: {\n                            csrfToken: await getCsrfToken(),\n                            data\n                        }\n                    });\n                    setLoading(false);\n                    if (newSession) {\n                        setSession(newSession);\n                        broadcast().postMessage({\n                            event: \"session\",\n                            data: {\n                                trigger: \"getSession\"\n                            }\n                        });\n                    }\n                    return newSession;\n                }\n            })\n    }[\"SessionProvider.useMemo[value]\"], [\n        session,\n        loading\n    ]);\n    return(// @ts-expect-error\n    (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SessionContext.Provider, {\n        value: value,\n        children: children\n    }));\n}\n_s1(SessionProvider, \"2gNWkA1ll3+1JambdW4uXB3blzw=\", false, function() {\n    return [\n        _lib_client_js__WEBPACK_IMPORTED_MODULE_2__.useOnline\n    ];\n});\n_c = SessionProvider;\nvar _c;\n$RefreshReg$(_c, \"SessionProvider\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next-auth/react.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-staff-service%5C%5Cnode_modules%5C%5Cnext-auth%5C%5Creact.js%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-staff-service%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-staff-service%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-staff-service%5C%5Cnode_modules%5C%5Cnext-auth%5C%5Creact.js%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-staff-service%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-staff-service%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-auth/react.js */ \"(app-pages-browser)/./node_modules/next-auth/react.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/styles/globals.css */ \"(app-pages-browser)/./src/styles/globals.css\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-staff-service%5C%5Cnode_modules%5C%5Cnext-auth%5C%5Creact.js%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-staff-service%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-staff-service%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":
/*!*******************************************************!*\
  !*** ./node_modules/next/dist/client/app-dir/link.js ***!
  \*******************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    /**\n * A React component that extends the HTML `<a>` element to provide\n * [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation. This is the primary way to navigate between routes in Next.js.\n *\n * @remarks\n * - Prefetching is only enabled in production.\n *\n * @see https://nextjs.org/docs/app/api-reference/components/link\n */ default: function() {\n        return LinkComponent;\n    },\n    useLinkStatus: function() {\n        return useLinkStatus;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _formaturl = __webpack_require__(/*! ../../shared/lib/router/utils/format-url */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js\");\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _routerreducertypes = __webpack_require__(/*! ../components/router-reducer/router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _usemergedref = __webpack_require__(/*! ../use-merged-ref */ \"(app-pages-browser)/./node_modules/next/dist/client/use-merged-ref.js\");\nconst _utils = __webpack_require__(/*! ../../shared/lib/utils */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js\");\nconst _addbasepath = __webpack_require__(/*! ../add-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/add-base-path.js\");\nconst _warnonce = __webpack_require__(/*! ../../shared/lib/utils/warn-once */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/warn-once.js\");\nconst _links = __webpack_require__(/*! ../components/links */ \"(app-pages-browser)/./node_modules/next/dist/client/components/links.js\");\nconst _islocalurl = __webpack_require__(/*! ../../shared/lib/router/utils/is-local-url */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-local-url.js\");\nconst _approuterinstance = __webpack_require__(/*! ../components/app-router-instance */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-instance.js\");\nconst _erroronce = __webpack_require__(/*! ../../shared/lib/utils/error-once */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/error-once.js\");\nfunction isModifiedEvent(event) {\n    const eventTarget = event.currentTarget;\n    const target = eventTarget.getAttribute('target');\n    return target && target !== '_self' || event.metaKey || event.ctrlKey || event.shiftKey || event.altKey || // triggers resource download\n    event.nativeEvent && event.nativeEvent.which === 2;\n}\nfunction linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate) {\n    const { nodeName } = e.currentTarget;\n    // anchors inside an svg have a lowercase nodeName\n    const isAnchorNodeName = nodeName.toUpperCase() === 'A';\n    if (isAnchorNodeName && isModifiedEvent(e) || e.currentTarget.hasAttribute('download')) {\n        // ignore click for browser’s default behavior\n        return;\n    }\n    if (!(0, _islocalurl.isLocalURL)(href)) {\n        if (replace) {\n            // browser default behavior does not replace the history state\n            // so we need to do it manually\n            e.preventDefault();\n            location.replace(href);\n        }\n        // ignore click for browser’s default behavior\n        return;\n    }\n    e.preventDefault();\n    const navigate = ()=>{\n        if (onNavigate) {\n            let isDefaultPrevented = false;\n            onNavigate({\n                preventDefault: ()=>{\n                    isDefaultPrevented = true;\n                }\n            });\n            if (isDefaultPrevented) {\n                return;\n            }\n        }\n        (0, _approuterinstance.dispatchNavigateAction)(as || href, replace ? 'replace' : 'push', scroll != null ? scroll : true, linkInstanceRef.current);\n    };\n    _react.default.startTransition(navigate);\n}\nfunction formatStringOrUrl(urlObjOrString) {\n    if (typeof urlObjOrString === 'string') {\n        return urlObjOrString;\n    }\n    return (0, _formaturl.formatUrl)(urlObjOrString);\n}\nfunction LinkComponent(props) {\n    _s();\n    const [linkStatus, setOptimisticLinkStatus] = (0, _react.useOptimistic)(_links.IDLE_LINK_STATUS);\n    let children;\n    const linkInstanceRef = (0, _react.useRef)(null);\n    const { href: hrefProp, as: asProp, children: childrenProp, prefetch: prefetchProp = null, passHref, replace, shallow, scroll, onClick, onMouseEnter: onMouseEnterProp, onTouchStart: onTouchStartProp, legacyBehavior = false, onNavigate, ref: forwardedRef, unstable_dynamicOnHover, ...restProps } = props;\n    children = childrenProp;\n    if (legacyBehavior && (typeof children === 'string' || typeof children === 'number')) {\n        children = /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n            children: children\n        });\n    }\n    const router = _react.default.useContext(_approutercontextsharedruntime.AppRouterContext);\n    const prefetchEnabled = prefetchProp !== false;\n    /**\n   * The possible states for prefetch are:\n   * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n   * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n   * - false: we will not prefetch if in the viewport at all\n   * - 'unstable_dynamicOnHover': this starts in \"auto\" mode, but switches to \"full\" when the link is hovered\n   */ const appPrefetchKind = prefetchProp === null ? _routerreducertypes.PrefetchKind.AUTO : _routerreducertypes.PrefetchKind.FULL;\n    if (true) {\n        function createPropError(args) {\n            return Object.defineProperty(new Error(\"Failed prop type: The prop `\" + args.key + \"` expects a \" + args.expected + \" in `<Link>`, but got `\" + args.actual + \"` instead.\" + ( true ? \"\\nOpen your browser's console to view the Component stack trace.\" : 0)), \"__NEXT_ERROR_CODE\", {\n                value: \"E319\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        // TypeScript trick for type-guarding:\n        const requiredPropsGuard = {\n            href: true\n        };\n        const requiredProps = Object.keys(requiredPropsGuard);\n        requiredProps.forEach((key)=>{\n            if (key === 'href') {\n                if (props[key] == null || typeof props[key] !== 'string' && typeof props[key] !== 'object') {\n                    throw createPropError({\n                        key,\n                        expected: '`string` or `object`',\n                        actual: props[key] === null ? 'null' : typeof props[key]\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n        // TypeScript trick for type-guarding:\n        const optionalPropsGuard = {\n            as: true,\n            replace: true,\n            scroll: true,\n            shallow: true,\n            passHref: true,\n            prefetch: true,\n            unstable_dynamicOnHover: true,\n            onClick: true,\n            onMouseEnter: true,\n            onTouchStart: true,\n            legacyBehavior: true,\n            onNavigate: true\n        };\n        const optionalProps = Object.keys(optionalPropsGuard);\n        optionalProps.forEach((key)=>{\n            const valType = typeof props[key];\n            if (key === 'as') {\n                if (props[key] && valType !== 'string' && valType !== 'object') {\n                    throw createPropError({\n                        key,\n                        expected: '`string` or `object`',\n                        actual: valType\n                    });\n                }\n            } else if (key === 'onClick' || key === 'onMouseEnter' || key === 'onTouchStart' || key === 'onNavigate') {\n                if (props[key] && valType !== 'function') {\n                    throw createPropError({\n                        key,\n                        expected: '`function`',\n                        actual: valType\n                    });\n                }\n            } else if (key === 'replace' || key === 'scroll' || key === 'shallow' || key === 'passHref' || key === 'prefetch' || key === 'legacyBehavior' || key === 'unstable_dynamicOnHover') {\n                if (props[key] != null && valType !== 'boolean') {\n                    throw createPropError({\n                        key,\n                        expected: '`boolean`',\n                        actual: valType\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n    }\n    if (true) {\n        if (props.locale) {\n            (0, _warnonce.warnOnce)('The `locale` prop is not supported in `next/link` while using the `app` router. Read more about app router internalization: https://nextjs.org/docs/app/building-your-application/routing/internationalization');\n        }\n        if (!asProp) {\n            let href;\n            if (typeof hrefProp === 'string') {\n                href = hrefProp;\n            } else if (typeof hrefProp === 'object' && typeof hrefProp.pathname === 'string') {\n                href = hrefProp.pathname;\n            }\n            if (href) {\n                const hasDynamicSegment = href.split('/').some((segment)=>segment.startsWith('[') && segment.endsWith(']'));\n                if (hasDynamicSegment) {\n                    throw Object.defineProperty(new Error(\"Dynamic href `\" + href + \"` found in <Link> while using the `/app` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href\"), \"__NEXT_ERROR_CODE\", {\n                        value: \"E267\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n            }\n        }\n    }\n    const { href, as } = _react.default.useMemo({\n        \"LinkComponent.useMemo\": ()=>{\n            const resolvedHref = formatStringOrUrl(hrefProp);\n            return {\n                href: resolvedHref,\n                as: asProp ? formatStringOrUrl(asProp) : resolvedHref\n            };\n        }\n    }[\"LinkComponent.useMemo\"], [\n        hrefProp,\n        asProp\n    ]);\n    // This will return the first child, if multiple are provided it will throw an error\n    let child;\n    if (legacyBehavior) {\n        if (true) {\n            if (onClick) {\n                console.warn('\"onClick\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link');\n            }\n            if (onMouseEnterProp) {\n                console.warn('\"onMouseEnter\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link');\n            }\n            try {\n                child = _react.default.Children.only(children);\n            } catch (err) {\n                if (!children) {\n                    throw Object.defineProperty(new Error(\"No children were passed to <Link> with `href` of `\" + hrefProp + \"` but one child is required https://nextjs.org/docs/messages/link-no-children\"), \"__NEXT_ERROR_CODE\", {\n                        value: \"E320\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                throw Object.defineProperty(new Error(\"Multiple children were passed to <Link> with `href` of `\" + hrefProp + \"` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children\" + ( true ? \" \\nOpen your browser's console to view the Component stack trace.\" : 0)), \"__NEXT_ERROR_CODE\", {\n                    value: \"E266\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        } else {}\n    } else {\n        if (true) {\n            if ((children == null ? void 0 : children.type) === 'a') {\n                throw Object.defineProperty(new Error('Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E209\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        }\n    }\n    const childRef = legacyBehavior ? child && typeof child === 'object' && child.ref : forwardedRef;\n    // Use a callback ref to attach an IntersectionObserver to the anchor tag on\n    // mount. In the future we will also use this to keep track of all the\n    // currently mounted <Link> instances, e.g. so we can re-prefetch them after\n    // a revalidation or refresh.\n    const observeLinkVisibilityOnMount = _react.default.useCallback({\n        \"LinkComponent.useCallback[observeLinkVisibilityOnMount]\": (element)=>{\n            if (router !== null) {\n                linkInstanceRef.current = (0, _links.mountLinkInstance)(element, href, router, appPrefetchKind, prefetchEnabled, setOptimisticLinkStatus);\n            }\n            return ({\n                \"LinkComponent.useCallback[observeLinkVisibilityOnMount]\": ()=>{\n                    if (linkInstanceRef.current) {\n                        (0, _links.unmountLinkForCurrentNavigation)(linkInstanceRef.current);\n                        linkInstanceRef.current = null;\n                    }\n                    (0, _links.unmountPrefetchableInstance)(element);\n                }\n            })[\"LinkComponent.useCallback[observeLinkVisibilityOnMount]\"];\n        }\n    }[\"LinkComponent.useCallback[observeLinkVisibilityOnMount]\"], [\n        prefetchEnabled,\n        href,\n        router,\n        appPrefetchKind,\n        setOptimisticLinkStatus\n    ]);\n    const mergedRef = (0, _usemergedref.useMergedRef)(observeLinkVisibilityOnMount, childRef);\n    const childProps = {\n        ref: mergedRef,\n        onClick (e) {\n            if (true) {\n                if (!e) {\n                    throw Object.defineProperty(new Error('Component rendered inside next/link has to pass click event to \"onClick\" prop.'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E312\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n            }\n            if (!legacyBehavior && typeof onClick === 'function') {\n                onClick(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onClick === 'function') {\n                child.props.onClick(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (e.defaultPrevented) {\n                return;\n            }\n            linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate);\n        },\n        onMouseEnter (e) {\n            if (!legacyBehavior && typeof onMouseEnterProp === 'function') {\n                onMouseEnterProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onMouseEnter === 'function') {\n                child.props.onMouseEnter(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (!prefetchEnabled || \"development\" === 'development') {\n                return;\n            }\n            const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true;\n            (0, _links.onNavigationIntent)(e.currentTarget, upgradeToDynamicPrefetch);\n        },\n        onTouchStart:  false ? 0 : function onTouchStart(e) {\n            if (!legacyBehavior && typeof onTouchStartProp === 'function') {\n                onTouchStartProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onTouchStart === 'function') {\n                child.props.onTouchStart(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (!prefetchEnabled) {\n                return;\n            }\n            const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true;\n            (0, _links.onNavigationIntent)(e.currentTarget, upgradeToDynamicPrefetch);\n        }\n    };\n    // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n    // defined, we specify the current 'href', so that repetition is not needed by the user.\n    // If the url is absolute, we can bypass the logic to prepend the basePath.\n    if ((0, _utils.isAbsoluteUrl)(as)) {\n        childProps.href = as;\n    } else if (!legacyBehavior || passHref || child.type === 'a' && !('href' in child.props)) {\n        childProps.href = (0, _addbasepath.addBasePath)(as);\n    }\n    let link;\n    if (legacyBehavior) {\n        if (true) {\n            (0, _erroronce.errorOnce)('`legacyBehavior` is deprecated and will be removed in a future ' + 'release. A codemod is available to upgrade your components:\\n\\n' + 'npx @next/codemod@latest new-link .\\n\\n' + 'Learn more: https://nextjs.org/docs/app/building-your-application/upgrading/codemods#remove-a-tags-from-link-components');\n        }\n        link = /*#__PURE__*/ _react.default.cloneElement(child, childProps);\n    } else {\n        link = /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n            ...restProps,\n            ...childProps,\n            children: children\n        });\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(LinkStatusContext.Provider, {\n        value: linkStatus,\n        children: link\n    });\n}\n_s(LinkComponent, \"MNV6IdWv8Lu3MKc7Fm4v59uGRY0=\");\n_c = LinkComponent;\nconst LinkStatusContext = /*#__PURE__*/ (0, _react.createContext)(_links.IDLE_LINK_STATUS);\nconst useLinkStatus = ()=>{\n    return (0, _react.useContext)(LinkStatusContext);\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=link.js.map\nvar _c;\n$RefreshReg$(_c, \"LinkComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/use-merged-ref.js":
/*!*********************************************************!*\
  !*** ./node_modules/next/dist/client/use-merged-ref.js ***!
  \*********************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"useMergedRef\", ({\n    enumerable: true,\n    get: function() {\n        return useMergedRef;\n    }\n}));\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nfunction useMergedRef(refA, refB) {\n    const cleanupA = (0, _react.useRef)(null);\n    const cleanupB = (0, _react.useRef)(null);\n    // NOTE: In theory, we could skip the wrapping if only one of the refs is non-null.\n    // (this happens often if the user doesn't pass a ref to Link/Form/Image)\n    // But this can cause us to leak a cleanup-ref into user code (e.g. via `<Link legacyBehavior>`),\n    // and the user might pass that ref into ref-merging library that doesn't support cleanup refs\n    // (because it hasn't been updated for React 19)\n    // which can then cause things to blow up, because a cleanup-returning ref gets called with `null`.\n    // So in practice, it's safer to be defensive and always wrap the ref, even on React 19.\n    return (0, _react.useCallback)((current)=>{\n        if (current === null) {\n            const cleanupFnA = cleanupA.current;\n            if (cleanupFnA) {\n                cleanupA.current = null;\n                cleanupFnA();\n            }\n            const cleanupFnB = cleanupB.current;\n            if (cleanupFnB) {\n                cleanupB.current = null;\n                cleanupFnB();\n            }\n        } else {\n            if (refA) {\n                cleanupA.current = applyRef(refA, current);\n            }\n            if (refB) {\n                cleanupB.current = applyRef(refB, current);\n            }\n        }\n    }, [\n        refA,\n        refB\n    ]);\n}\nfunction applyRef(refA, current) {\n    if (typeof refA === 'function') {\n        const cleanup = refA(current);\n        if (typeof cleanup === 'function') {\n            return cleanup;\n        } else {\n            return ()=>refA(null);\n        }\n    } else {\n        refA.current = current;\n        return ()=>{\n            refA.current = null;\n        };\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-merged-ref.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L3VzZS1tZXJnZWQtcmVmLmpzIiwibWFwcGluZ3MiOiI7Ozs7Z0RBU2dCQTs7O2VBQUFBOzs7bUNBVDhCO0FBU3ZDLFNBQVNBLGFBQ2RDLElBQW1CLEVBQ25CQyxJQUFtQjtJQUVuQixNQUFNQyxXQUFXQyxDQUFBQSxHQUFBQSxPQUFBQSxNQUFBQSxFQUE0QjtJQUM3QyxNQUFNQyxXQUFXRCxDQUFBQSxHQUFBQSxPQUFBQSxNQUFBQSxFQUE0QjtJQUU3QyxtRkFBbUY7SUFDbkYseUVBQXlFO0lBQ3pFLGlHQUFpRztJQUNqRyw4RkFBOEY7SUFDOUYsZ0RBQWdEO0lBQ2hELG1HQUFtRztJQUNuRyx3RkFBd0Y7SUFDeEYsT0FBT0UsQ0FBQUEsR0FBQUEsT0FBQUEsV0FBQUEsRUFDTCxDQUFDQztRQUNDLElBQUlBLFlBQVksTUFBTTtZQUNwQixNQUFNQyxhQUFhTCxTQUFTSSxPQUFPO1lBQ25DLElBQUlDLFlBQVk7Z0JBQ2RMLFNBQVNJLE9BQU8sR0FBRztnQkFDbkJDO1lBQ0Y7WUFDQSxNQUFNQyxhQUFhSixTQUFTRSxPQUFPO1lBQ25DLElBQUlFLFlBQVk7Z0JBQ2RKLFNBQVNFLE9BQU8sR0FBRztnQkFDbkJFO1lBQ0Y7UUFDRixPQUFPO1lBQ0wsSUFBSVIsTUFBTTtnQkFDUkUsU0FBU0ksT0FBTyxHQUFHRyxTQUFTVCxNQUFNTTtZQUNwQztZQUNBLElBQUlMLE1BQU07Z0JBQ1JHLFNBQVNFLE9BQU8sR0FBR0csU0FBU1IsTUFBTUs7WUFDcEM7UUFDRjtJQUNGLEdBQ0E7UUFBQ047UUFBTUM7S0FBSztBQUVoQjtBQUVBLFNBQVNRLFNBQ1BULElBQWdDLEVBQ2hDTSxPQUFpQjtJQUVqQixJQUFJLE9BQU9OLFNBQVMsWUFBWTtRQUM5QixNQUFNVSxVQUFVVixLQUFLTTtRQUNyQixJQUFJLE9BQU9JLFlBQVksWUFBWTtZQUNqQyxPQUFPQTtRQUNULE9BQU87WUFDTCxPQUFPLElBQU1WLEtBQUs7UUFDcEI7SUFDRixPQUFPO1FBQ0xBLEtBQUtNLE9BQU8sR0FBR0E7UUFDZixPQUFPO1lBQ0xOLEtBQUtNLE9BQU8sR0FBRztRQUNqQjtJQUNGO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcV2luZG93cyAxMVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxzcmNcXGNsaWVudFxcdXNlLW1lcmdlZC1yZWYudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlQ2FsbGJhY2ssIHVzZVJlZiwgdHlwZSBSZWYgfSBmcm9tICdyZWFjdCdcblxuLy8gVGhpcyBpcyBhIGNvbXBhdGliaWxpdHkgaG9vayB0byBzdXBwb3J0IFJlYWN0IDE4IGFuZCAxOSByZWZzLlxuLy8gSW4gMTksIGEgY2xlYW51cCBmdW5jdGlvbiBmcm9tIHJlZnMgbWF5IGJlIHJldHVybmVkLlxuLy8gSW4gMTgsIHJldHVybmluZyBhIGNsZWFudXAgZnVuY3Rpb24gY3JlYXRlcyBhIHdhcm5pbmcuXG4vLyBTaW5jZSB3ZSB0YWtlIHVzZXJzcGFjZSByZWZzLCB3ZSBkb24ndCBrbm93IGFoZWFkIG9mIHRpbWUgaWYgYSBjbGVhbnVwIGZ1bmN0aW9uIHdpbGwgYmUgcmV0dXJuZWQuXG4vLyBUaGlzIGltcGxlbWVudHMgY2xlYW51cCBmdW5jdGlvbnMgd2l0aCB0aGUgb2xkIGJlaGF2aW9yIGluIDE4LlxuLy8gV2Uga25vdyByZWZzIGFyZSBhbHdheXMgY2FsbGVkIGFsdGVybmF0aW5nIHdpdGggYG51bGxgIGFuZCB0aGVuIGBUYC5cbi8vIFNvIGEgY2FsbCB3aXRoIGBudWxsYCBtZWFucyB3ZSBuZWVkIHRvIGNhbGwgdGhlIHByZXZpb3VzIGNsZWFudXAgZnVuY3Rpb25zLlxuZXhwb3J0IGZ1bmN0aW9uIHVzZU1lcmdlZFJlZjxURWxlbWVudD4oXG4gIHJlZkE6IFJlZjxURWxlbWVudD4sXG4gIHJlZkI6IFJlZjxURWxlbWVudD5cbik6IFJlZjxURWxlbWVudD4ge1xuICBjb25zdCBjbGVhbnVwQSA9IHVzZVJlZjwoKCkgPT4gdm9pZCkgfCBudWxsPihudWxsKVxuICBjb25zdCBjbGVhbnVwQiA9IHVzZVJlZjwoKCkgPT4gdm9pZCkgfCBudWxsPihudWxsKVxuXG4gIC8vIE5PVEU6IEluIHRoZW9yeSwgd2UgY291bGQgc2tpcCB0aGUgd3JhcHBpbmcgaWYgb25seSBvbmUgb2YgdGhlIHJlZnMgaXMgbm9uLW51bGwuXG4gIC8vICh0aGlzIGhhcHBlbnMgb2Z0ZW4gaWYgdGhlIHVzZXIgZG9lc24ndCBwYXNzIGEgcmVmIHRvIExpbmsvRm9ybS9JbWFnZSlcbiAgLy8gQnV0IHRoaXMgY2FuIGNhdXNlIHVzIHRvIGxlYWsgYSBjbGVhbnVwLXJlZiBpbnRvIHVzZXIgY29kZSAoZS5nLiB2aWEgYDxMaW5rIGxlZ2FjeUJlaGF2aW9yPmApLFxuICAvLyBhbmQgdGhlIHVzZXIgbWlnaHQgcGFzcyB0aGF0IHJlZiBpbnRvIHJlZi1tZXJnaW5nIGxpYnJhcnkgdGhhdCBkb2Vzbid0IHN1cHBvcnQgY2xlYW51cCByZWZzXG4gIC8vIChiZWNhdXNlIGl0IGhhc24ndCBiZWVuIHVwZGF0ZWQgZm9yIFJlYWN0IDE5KVxuICAvLyB3aGljaCBjYW4gdGhlbiBjYXVzZSB0aGluZ3MgdG8gYmxvdyB1cCwgYmVjYXVzZSBhIGNsZWFudXAtcmV0dXJuaW5nIHJlZiBnZXRzIGNhbGxlZCB3aXRoIGBudWxsYC5cbiAgLy8gU28gaW4gcHJhY3RpY2UsIGl0J3Mgc2FmZXIgdG8gYmUgZGVmZW5zaXZlIGFuZCBhbHdheXMgd3JhcCB0aGUgcmVmLCBldmVuIG9uIFJlYWN0IDE5LlxuICByZXR1cm4gdXNlQ2FsbGJhY2soXG4gICAgKGN1cnJlbnQ6IFRFbGVtZW50IHwgbnVsbCk6IHZvaWQgPT4ge1xuICAgICAgaWYgKGN1cnJlbnQgPT09IG51bGwpIHtcbiAgICAgICAgY29uc3QgY2xlYW51cEZuQSA9IGNsZWFudXBBLmN1cnJlbnRcbiAgICAgICAgaWYgKGNsZWFudXBGbkEpIHtcbiAgICAgICAgICBjbGVhbnVwQS5jdXJyZW50ID0gbnVsbFxuICAgICAgICAgIGNsZWFudXBGbkEoKVxuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGNsZWFudXBGbkIgPSBjbGVhbnVwQi5jdXJyZW50XG4gICAgICAgIGlmIChjbGVhbnVwRm5CKSB7XG4gICAgICAgICAgY2xlYW51cEIuY3VycmVudCA9IG51bGxcbiAgICAgICAgICBjbGVhbnVwRm5CKClcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgaWYgKHJlZkEpIHtcbiAgICAgICAgICBjbGVhbnVwQS5jdXJyZW50ID0gYXBwbHlSZWYocmVmQSwgY3VycmVudClcbiAgICAgICAgfVxuICAgICAgICBpZiAocmVmQikge1xuICAgICAgICAgIGNsZWFudXBCLmN1cnJlbnQgPSBhcHBseVJlZihyZWZCLCBjdXJyZW50KVxuICAgICAgICB9XG4gICAgICB9XG4gICAgfSxcbiAgICBbcmVmQSwgcmVmQl1cbiAgKVxufVxuXG5mdW5jdGlvbiBhcHBseVJlZjxURWxlbWVudD4oXG4gIHJlZkE6IE5vbk51bGxhYmxlPFJlZjxURWxlbWVudD4+LFxuICBjdXJyZW50OiBURWxlbWVudFxuKSB7XG4gIGlmICh0eXBlb2YgcmVmQSA9PT0gJ2Z1bmN0aW9uJykge1xuICAgIGNvbnN0IGNsZWFudXAgPSByZWZBKGN1cnJlbnQpXG4gICAgaWYgKHR5cGVvZiBjbGVhbnVwID09PSAnZnVuY3Rpb24nKSB7XG4gICAgICByZXR1cm4gY2xlYW51cFxuICAgIH0gZWxzZSB7XG4gICAgICByZXR1cm4gKCkgPT4gcmVmQShudWxsKVxuICAgIH1cbiAgfSBlbHNlIHtcbiAgICByZWZBLmN1cnJlbnQgPSBjdXJyZW50XG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIHJlZkEuY3VycmVudCA9IG51bGxcbiAgICB9XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJ1c2VNZXJnZWRSZWYiLCJyZWZBIiwicmVmQiIsImNsZWFudXBBIiwidXNlUmVmIiwiY2xlYW51cEIiLCJ1c2VDYWxsYmFjayIsImN1cnJlbnQiLCJjbGVhbnVwRm5BIiwiY2xlYW51cEZuQiIsImFwcGx5UmVmIiwiY2xlYW51cCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/use-merged-ref.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js":
/*!**********************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/utils/format-url.js ***!
  \**********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("// Format function modified from nodejs\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    formatUrl: function() {\n        return formatUrl;\n    },\n    formatWithValidation: function() {\n        return formatWithValidation;\n    },\n    urlObjectKeys: function() {\n        return urlObjectKeys;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _querystring = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! ./querystring */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/querystring.js\"));\nconst slashedProtocols = /https?|ftp|gopher|file/;\nfunction formatUrl(urlObj) {\n    let { auth, hostname } = urlObj;\n    let protocol = urlObj.protocol || '';\n    let pathname = urlObj.pathname || '';\n    let hash = urlObj.hash || '';\n    let query = urlObj.query || '';\n    let host = false;\n    auth = auth ? encodeURIComponent(auth).replace(/%3A/i, ':') + '@' : '';\n    if (urlObj.host) {\n        host = auth + urlObj.host;\n    } else if (hostname) {\n        host = auth + (~hostname.indexOf(':') ? \"[\" + hostname + \"]\" : hostname);\n        if (urlObj.port) {\n            host += ':' + urlObj.port;\n        }\n    }\n    if (query && typeof query === 'object') {\n        query = String(_querystring.urlQueryToSearchParams(query));\n    }\n    let search = urlObj.search || query && \"?\" + query || '';\n    if (protocol && !protocol.endsWith(':')) protocol += ':';\n    if (urlObj.slashes || (!protocol || slashedProtocols.test(protocol)) && host !== false) {\n        host = '//' + (host || '');\n        if (pathname && pathname[0] !== '/') pathname = '/' + pathname;\n    } else if (!host) {\n        host = '';\n    }\n    if (hash && hash[0] !== '#') hash = '#' + hash;\n    if (search && search[0] !== '?') search = '?' + search;\n    pathname = pathname.replace(/[?#]/g, encodeURIComponent);\n    search = search.replace('#', '%23');\n    return \"\" + protocol + host + pathname + search + hash;\n}\nconst urlObjectKeys = [\n    'auth',\n    'hash',\n    'host',\n    'hostname',\n    'href',\n    'path',\n    'pathname',\n    'port',\n    'protocol',\n    'query',\n    'search',\n    'slashes'\n];\nfunction formatWithValidation(url) {\n    if (true) {\n        if (url !== null && typeof url === 'object') {\n            Object.keys(url).forEach((key)=>{\n                if (!urlObjectKeys.includes(key)) {\n                    console.warn(\"Unknown key passed via urlObject into url.format: \" + key);\n                }\n            });\n        }\n    }\n    return formatUrl(url);\n} //# sourceMappingURL=format-url.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-local-url.js":
/*!************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/utils/is-local-url.js ***!
  \************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"isLocalURL\", ({\n    enumerable: true,\n    get: function() {\n        return isLocalURL;\n    }\n}));\nconst _utils = __webpack_require__(/*! ../../utils */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js\");\nconst _hasbasepath = __webpack_require__(/*! ../../../../client/has-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/has-base-path.js\");\nfunction isLocalURL(url) {\n    // prevent a hydration mismatch on href for url with anchor refs\n    if (!(0, _utils.isAbsoluteUrl)(url)) return true;\n    try {\n        // absolute urls can be local if they are on the same origin\n        const locationOrigin = (0, _utils.getLocationOrigin)();\n        const resolved = new URL(url, locationOrigin);\n        return resolved.origin === locationOrigin && (0, _hasbasepath.hasBasePath)(resolved.pathname);\n    } catch (_) {\n        return false;\n    }\n} //# sourceMappingURL=is-local-url.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9yb3V0ZXIvdXRpbHMvaXMtbG9jYWwtdXJsLmpzIiwibWFwcGluZ3MiOiI7Ozs7OENBTWdCQTs7O2VBQUFBOzs7bUNBTmlDO3lDQUNyQjtBQUtyQixTQUFTQSxXQUFXQyxHQUFXO0lBQ3BDLGdFQUFnRTtJQUNoRSxJQUFJLENBQUNDLENBQUFBLEdBQUFBLE9BQUFBLGFBQUFBLEVBQWNELE1BQU0sT0FBTztJQUNoQyxJQUFJO1FBQ0YsNERBQTREO1FBQzVELE1BQU1FLGlCQUFpQkMsQ0FBQUEsR0FBQUEsT0FBQUEsaUJBQUFBO1FBQ3ZCLE1BQU1DLFdBQVcsSUFBSUMsSUFBSUwsS0FBS0U7UUFDOUIsT0FBT0UsU0FBU0UsTUFBTSxLQUFLSixrQkFBa0JLLENBQUFBLEdBQUFBLGFBQUFBLFdBQUFBLEVBQVlILFNBQVNJLFFBQVE7SUFDNUUsRUFBRSxPQUFPQyxHQUFHO1FBQ1YsT0FBTztJQUNUO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc3JjXFxzaGFyZWRcXGxpYlxccm91dGVyXFx1dGlsc1xcaXMtbG9jYWwtdXJsLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGlzQWJzb2x1dGVVcmwsIGdldExvY2F0aW9uT3JpZ2luIH0gZnJvbSAnLi4vLi4vdXRpbHMnXG5pbXBvcnQgeyBoYXNCYXNlUGF0aCB9IGZyb20gJy4uLy4uLy4uLy4uL2NsaWVudC9oYXMtYmFzZS1wYXRoJ1xuXG4vKipcbiAqIERldGVjdHMgd2hldGhlciBhIGdpdmVuIHVybCBpcyByb3V0YWJsZSBieSB0aGUgTmV4dC5qcyByb3V0ZXIgKGJyb3dzZXIgb25seSkuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBpc0xvY2FsVVJMKHVybDogc3RyaW5nKTogYm9vbGVhbiB7XG4gIC8vIHByZXZlbnQgYSBoeWRyYXRpb24gbWlzbWF0Y2ggb24gaHJlZiBmb3IgdXJsIHdpdGggYW5jaG9yIHJlZnNcbiAgaWYgKCFpc0Fic29sdXRlVXJsKHVybCkpIHJldHVybiB0cnVlXG4gIHRyeSB7XG4gICAgLy8gYWJzb2x1dGUgdXJscyBjYW4gYmUgbG9jYWwgaWYgdGhleSBhcmUgb24gdGhlIHNhbWUgb3JpZ2luXG4gICAgY29uc3QgbG9jYXRpb25PcmlnaW4gPSBnZXRMb2NhdGlvbk9yaWdpbigpXG4gICAgY29uc3QgcmVzb2x2ZWQgPSBuZXcgVVJMKHVybCwgbG9jYXRpb25PcmlnaW4pXG4gICAgcmV0dXJuIHJlc29sdmVkLm9yaWdpbiA9PT0gbG9jYXRpb25PcmlnaW4gJiYgaGFzQmFzZVBhdGgocmVzb2x2ZWQucGF0aG5hbWUpXG4gIH0gY2F0Y2ggKF8pIHtcbiAgICByZXR1cm4gZmFsc2VcbiAgfVxufVxuIl0sIm5hbWVzIjpbImlzTG9jYWxVUkwiLCJ1cmwiLCJpc0Fic29sdXRlVXJsIiwibG9jYXRpb25PcmlnaW4iLCJnZXRMb2NhdGlvbk9yaWdpbiIsInJlc29sdmVkIiwiVVJMIiwib3JpZ2luIiwiaGFzQmFzZVBhdGgiLCJwYXRobmFtZSIsIl8iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-local-url.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/querystring.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/utils/querystring.js ***!
  \***********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    assign: function() {\n        return assign;\n    },\n    searchParamsToUrlQuery: function() {\n        return searchParamsToUrlQuery;\n    },\n    urlQueryToSearchParams: function() {\n        return urlQueryToSearchParams;\n    }\n});\nfunction searchParamsToUrlQuery(searchParams) {\n    const query = {};\n    for (const [key, value] of searchParams.entries()){\n        const existing = query[key];\n        if (typeof existing === 'undefined') {\n            query[key] = value;\n        } else if (Array.isArray(existing)) {\n            existing.push(value);\n        } else {\n            query[key] = [\n                existing,\n                value\n            ];\n        }\n    }\n    return query;\n}\nfunction stringifyUrlQueryParam(param) {\n    if (typeof param === 'string') {\n        return param;\n    }\n    if (typeof param === 'number' && !isNaN(param) || typeof param === 'boolean') {\n        return String(param);\n    } else {\n        return '';\n    }\n}\nfunction urlQueryToSearchParams(query) {\n    const searchParams = new URLSearchParams();\n    for (const [key, value] of Object.entries(query)){\n        if (Array.isArray(value)) {\n            for (const item of value){\n                searchParams.append(key, stringifyUrlQueryParam(item));\n            }\n        } else {\n            searchParams.set(key, stringifyUrlQueryParam(value));\n        }\n    }\n    return searchParams;\n}\nfunction assign(target) {\n    for(var _len = arguments.length, searchParamsList = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n        searchParamsList[_key - 1] = arguments[_key];\n    }\n    for (const searchParams of searchParamsList){\n        for (const key of searchParams.keys()){\n            target.delete(key);\n        }\n        for (const [key, value] of searchParams.entries()){\n            target.append(key, value);\n        }\n    }\n    return target;\n} //# sourceMappingURL=querystring.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/querystring.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js":
/*!****************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/utils.js ***!
  \****************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DecodeError: function() {\n        return DecodeError;\n    },\n    MiddlewareNotFoundError: function() {\n        return MiddlewareNotFoundError;\n    },\n    MissingStaticPage: function() {\n        return MissingStaticPage;\n    },\n    NormalizeError: function() {\n        return NormalizeError;\n    },\n    PageNotFoundError: function() {\n        return PageNotFoundError;\n    },\n    SP: function() {\n        return SP;\n    },\n    ST: function() {\n        return ST;\n    },\n    WEB_VITALS: function() {\n        return WEB_VITALS;\n    },\n    execOnce: function() {\n        return execOnce;\n    },\n    getDisplayName: function() {\n        return getDisplayName;\n    },\n    getLocationOrigin: function() {\n        return getLocationOrigin;\n    },\n    getURL: function() {\n        return getURL;\n    },\n    isAbsoluteUrl: function() {\n        return isAbsoluteUrl;\n    },\n    isResSent: function() {\n        return isResSent;\n    },\n    loadGetInitialProps: function() {\n        return loadGetInitialProps;\n    },\n    normalizeRepeatedSlashes: function() {\n        return normalizeRepeatedSlashes;\n    },\n    stringifyError: function() {\n        return stringifyError;\n    }\n});\nconst WEB_VITALS = [\n    'CLS',\n    'FCP',\n    'FID',\n    'INP',\n    'LCP',\n    'TTFB'\n];\nfunction execOnce(fn) {\n    let used = false;\n    let result;\n    return function() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        if (!used) {\n            used = true;\n            result = fn(...args);\n        }\n        return result;\n    };\n}\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/;\nconst isAbsoluteUrl = (url)=>ABSOLUTE_URL_REGEX.test(url);\nfunction getLocationOrigin() {\n    const { protocol, hostname, port } = window.location;\n    return protocol + \"//\" + hostname + (port ? ':' + port : '');\n}\nfunction getURL() {\n    const { href } = window.location;\n    const origin = getLocationOrigin();\n    return href.substring(origin.length);\n}\nfunction getDisplayName(Component) {\n    return typeof Component === 'string' ? Component : Component.displayName || Component.name || 'Unknown';\n}\nfunction isResSent(res) {\n    return res.finished || res.headersSent;\n}\nfunction normalizeRepeatedSlashes(url) {\n    const urlParts = url.split('?');\n    const urlNoQuery = urlParts[0];\n    return urlNoQuery // first we replace any non-encoded backslashes with forward\n    // then normalize repeated forward slashes\n    .replace(/\\\\/g, '/').replace(/\\/\\/+/g, '/') + (urlParts[1] ? \"?\" + urlParts.slice(1).join('?') : '');\n}\nasync function loadGetInitialProps(App, ctx) {\n    if (true) {\n        var _App_prototype;\n        if ((_App_prototype = App.prototype) == null ? void 0 : _App_prototype.getInitialProps) {\n            const message = '\"' + getDisplayName(App) + '.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.';\n            throw Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n                value: \"E394\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n    }\n    // when called from _app `ctx` is nested in `ctx`\n    const res = ctx.res || ctx.ctx && ctx.ctx.res;\n    if (!App.getInitialProps) {\n        if (ctx.ctx && ctx.Component) {\n            // @ts-ignore pageProps default\n            return {\n                pageProps: await loadGetInitialProps(ctx.Component, ctx.ctx)\n            };\n        }\n        return {};\n    }\n    const props = await App.getInitialProps(ctx);\n    if (res && isResSent(res)) {\n        return props;\n    }\n    if (!props) {\n        const message = '\"' + getDisplayName(App) + '.getInitialProps()\" should resolve to an object. But found \"' + props + '\" instead.';\n        throw Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n            value: \"E394\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    if (true) {\n        if (Object.keys(props).length === 0 && !ctx.ctx) {\n            console.warn(\"\" + getDisplayName(App) + \" returned an empty object from `getInitialProps`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps\");\n        }\n    }\n    return props;\n}\nconst SP = typeof performance !== 'undefined';\nconst ST = SP && [\n    'mark',\n    'measure',\n    'getEntriesByName'\n].every((method)=>typeof performance[method] === 'function');\nclass DecodeError extends Error {\n}\nclass NormalizeError extends Error {\n}\nclass PageNotFoundError extends Error {\n    constructor(page){\n        super();\n        this.code = 'ENOENT';\n        this.name = 'PageNotFoundError';\n        this.message = \"Cannot find module for page: \" + page;\n    }\n}\nclass MissingStaticPage extends Error {\n    constructor(page, message){\n        super();\n        this.message = \"Failed to load static file for page: \" + page + \" \" + message;\n    }\n}\nclass MiddlewareNotFoundError extends Error {\n    constructor(){\n        super();\n        this.code = 'ENOENT';\n        this.message = \"Cannot find the middleware module\";\n    }\n}\nfunction stringifyError(error) {\n    return JSON.stringify({\n        message: error.message,\n        stack: error.stack\n    });\n} //# sourceMappingURL=utils.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/error-once.js":
/*!***************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/utils/error-once.js ***!
  \***************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"errorOnce\", ({\n    enumerable: true,\n    get: function() {\n        return errorOnce;\n    }\n}));\nlet errorOnce = (_)=>{};\nif (true) {\n    const errors = new Set();\n    errorOnce = (msg)=>{\n        if (!errors.has(msg)) {\n            console.error(msg);\n        }\n        errors.add(msg);\n    };\n} //# sourceMappingURL=error-once.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi91dGlscy9lcnJvci1vbmNlLmpzIiwibWFwcGluZ3MiOiI7Ozs7NkNBV1NBOzs7ZUFBQUE7OztBQVhULElBQUlBLFlBQVksQ0FBQ0MsS0FBZTtBQUNoQyxJQUFJQyxJQUFvQixFQUFtQjtJQUN6QyxNQUFNRyxTQUFTLElBQUlDO0lBQ25CTixZQUFZLENBQUNPO1FBQ1gsSUFBSSxDQUFDRixPQUFPRyxHQUFHLENBQUNELE1BQU07WUFDcEJFLFFBQVFDLEtBQUssQ0FBQ0g7UUFDaEI7UUFDQUYsT0FBT00sR0FBRyxDQUFDSjtJQUNiO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcV2luZG93cyAxMVxcc3JjXFxzaGFyZWRcXGxpYlxcdXRpbHNcXGVycm9yLW9uY2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsibGV0IGVycm9yT25jZSA9IChfOiBzdHJpbmcpID0+IHt9XG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICBjb25zdCBlcnJvcnMgPSBuZXcgU2V0PHN0cmluZz4oKVxuICBlcnJvck9uY2UgPSAobXNnOiBzdHJpbmcpID0+IHtcbiAgICBpZiAoIWVycm9ycy5oYXMobXNnKSkge1xuICAgICAgY29uc29sZS5lcnJvcihtc2cpXG4gICAgfVxuICAgIGVycm9ycy5hZGQobXNnKVxuICB9XG59XG5cbmV4cG9ydCB7IGVycm9yT25jZSB9XG4iXSwibmFtZXMiOlsiZXJyb3JPbmNlIiwiXyIsInByb2Nlc3MiLCJlbnYiLCJOT0RFX0VOViIsImVycm9ycyIsIlNldCIsIm1zZyIsImhhcyIsImNvbnNvbGUiLCJlcnJvciIsImFkZCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/error-once.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"6eff0f08b5b2\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFdpbmRvd3MgMTFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcSW5ub3ZhdGl2ZSBDZW50cmVcXGNybS1zdGFmZi1zZXJ2aWNlXFxzcmNcXHN0eWxlc1xcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI2ZWZmMGYwOGI1YjJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/styles/globals.css\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-staff-service%5C%5Cnode_modules%5C%5Cnext-auth%5C%5Creact.js%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-staff-service%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-staff-service%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);