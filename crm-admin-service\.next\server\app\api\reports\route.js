(()=>{var e={};e.id=647,e.ids=[647],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1390:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>R,routeModule:()=>f,serverHooks:()=>N,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>v});var a={};r.r(a),r.d(a,{GET:()=>m,POST:()=>y});var s=r(6559),n=r(8088),i=r(7719),o=r(2190),d=r(2909),u=r(5069),c=r(6330),p=r(5697);let l=p.z.object({reportType:p.z.enum(["revenue","payments","transactions"]),periodStart:p.z.string().transform(e=>new Date(e)),periodEnd:p.z.string().transform(e=>new Date(e))});async function m(e){try{let t=await (0,d.j2)();if(!t?.user)return o.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:r}=new URL(e.url),a=parseInt(r.get("page")||"1"),s=parseInt(r.get("limit")||"10"),n=r.get("reportType"),i=(a-1)*s,c={};n&&(c.reportType=n);let[p,l]=await Promise.all([u.z.financialReport.findMany({where:c,skip:i,take:s,include:{generatedByUser:{select:{firstName:!0,lastName:!0,email:!0}}},orderBy:{createdAt:"desc"}}),u.z.financialReport.count({where:c})]);return o.NextResponse.json({success:!0,data:{reports:p,pagination:{page:a,limit:s,total:l,pages:Math.ceil(l/s)}}})}catch(e){return console.error("Error fetching reports:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}async function y(e){try{let t=await (0,d.j2)();if(!t?.user)return o.NextResponse.json({error:"Unauthorized"},{status:401});if(!["admin","accounting"].includes(t.user.role))return o.NextResponse.json({error:"Forbidden"},{status:403});let r=await e.json(),{reportType:a,periodStart:s,periodEnd:n}=l.parse(r),i={};switch(a){case"revenue":i=await w(s,n);break;case"payments":i=await g(s,n);break;case"transactions":i=await h(s,n)}let c=await u.z.financialReport.create({data:{reportType:a,periodStart:s,periodEnd:n,data:i,generatedBy:t.user.id},include:{generatedByUser:{select:{firstName:!0,lastName:!0,email:!0}}}});return await u.z.auditLog.create({data:{userId:t.user.id,action:"GENERATE_REPORT",resourceType:"FINANCIAL_REPORT",resourceId:c.id,newValues:{reportType:a,periodStart:s,periodEnd:n},ipAddress:"127.0.0.1",userAgent:e.headers.get("user-agent")||"Unknown"}}),o.NextResponse.json({success:!0,data:c})}catch(e){if(e instanceof p.z.ZodError)return o.NextResponse.json({error:"Validation error",details:e.errors},{status:400});return console.error("Error generating report:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}async function w(e,t){let r=await u.z.paymentRecord.findMany({where:{paymentDate:{gte:e,lte:t},status:c.PaymentStatus.verified}}),a=r.reduce((e,t)=>e+Number(t.amount),0),s=r.length;return{summary:{totalRevenue:a,paymentCount:s,averagePayment:s>0?a/s:0},revenueByMethod:r.reduce((e,t)=>(e[t.paymentMethod]=(e[t.paymentMethod]||0)+Number(t.amount),e),{}),periodStart:e,periodEnd:t}}async function g(e,t){let r=await u.z.paymentRecord.findMany({where:{paymentDate:{gte:e,lte:t}},include:{recordedByUser:{select:{firstName:!0,lastName:!0}}}}),a=r.reduce((e,t)=>(e[t.status]=(e[t.status]||0)+1,e),{}),s=r.reduce((e,t)=>(e[t.paymentMethod]=(e[t.paymentMethod]||0)+1,e),{});return{summary:{totalPayments:r.length,paymentsByStatus:a,paymentsByMethod:s},payments:r.map(e=>({id:e.id,amount:e.amount,paymentDate:e.paymentDate,paymentMethod:e.paymentMethod,status:e.status,recordedBy:`${e.recordedByUser.firstName} ${e.recordedByUser.lastName}`})),periodStart:e,periodEnd:t}}async function h(e,t){let r=await u.z.financialTransaction.findMany({where:{timestamp:{gte:e,lte:t}},include:{performedByUser:{select:{firstName:!0,lastName:!0}}}}),a=r.reduce((e,t)=>(e[t.transactionType]=(e[t.transactionType]||0)+1,e),{});return{summary:{totalTransactions:r.length,transactionsByType:a},transactions:r.map(e=>({id:e.id,transactionType:e.transactionType,amount:e.amount,description:e.description,timestamp:e.timestamp,performedBy:`${e.performedByUser.firstName} ${e.performedByUser.lastName}`})),periodStart:e,periodEnd:t}}let f=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/reports/route",pathname:"/api/reports",filename:"route",bundlePath:"app/api/reports/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-admin-service\\src\\app\\api\\reports\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:x,workUnitAsyncStorage:v,serverHooks:N}=f;function R(){return(0,i.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:v})}},2909:(e,t,r)=>{"use strict";r.d(t,{Y9:()=>d,j2:()=>u});var a=r(8643),s=r(189),n=r(6467),i=r(5069),o=r(5663);let{handlers:d,auth:u,signIn:c,signOut:p}=(0,a.Ay)({adapter:(0,n.y)(i.z),session:{strategy:"jwt",maxAge:1800},pages:{signIn:"/auth/signin",error:"/auth/error"},providers:[(0,s.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"},mfaCode:{label:"MFA Code",type:"text"}},async authorize(e){if(!e?.email||!e?.password)return null;try{let t=await i.z.adminUser.findUnique({where:{email:e.email}});if(!t||!t.isActive||!await o.Ay.compare(e.password,t.passwordHash))return null;return t.mfaEnabled&&!e.mfaCode&&console.warn("MFA required but not implemented yet"),await i.z.adminUser.update({where:{id:t.id},data:{lastLogin:new Date}}),await i.z.auditLog.create({data:{userId:t.id,action:"LOGIN",resourceType:"AUTH",resourceId:t.id,newValues:{timestamp:new Date,success:!0},ipAddress:"127.0.0.1",userAgent:"Unknown"}}),{id:t.id,email:t.email,name:`${t.firstName} ${t.lastName}`,role:t.role}}catch(e){return console.error("Authentication error:",e),null}}})],callbacks:{jwt:async({token:e,user:t})=>(t&&(e.role=t.role,e.id=t.id),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.id,e.user.role=t.role),e),async authorized({auth:e,request:{nextUrl:t}}){let r=!!e?.user,a=t.pathname.startsWith("/dashboard"),s=t.pathname.startsWith("/auth");return a?!!r:!s||!r||Response.redirect(new URL("/dashboard",t))}},events:{async signOut(e){let t="token"in e&&e.token?.id?e.token.id:"session"in e&&e.session?.user?.id?e.session.user.id:null;t&&await i.z.auditLog.create({data:{userId:t,action:"LOGOUT",resourceType:"AUTH",resourceId:t,newValues:{timestamp:new Date},ipAddress:"127.0.0.1",userAgent:"Unknown"}})}}}),{GET:l,POST:m}=d},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5069:(e,t,r)=>{"use strict";r.d(t,{db:()=>n,z:()=>s});var a=r(6330);let s=globalThis.prisma??new a.PrismaClient({log:["query","error","warn"]}),n=s},5511:e=>{"use strict";e.exports=require("crypto")},6330:e=>{"use strict";e.exports=require("@prisma/client")},6487:()=>{},8335:()=>{},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[447,199,828,697],()=>r(1390));module.exports=a})();