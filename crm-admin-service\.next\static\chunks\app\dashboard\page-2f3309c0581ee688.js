(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[105],{285:(e,s,a)=>{"use strict";a.d(s,{$:()=>d});var t=a(5155);a(2115);var r=a(4624),i=a(2085),l=a(9434);let n=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:s,variant:a,size:i,asChild:d=!1,...c}=e,x=d?r.DX:"button";return(0,t.jsx)(x,{"data-slot":"button",className:(0,l.cn)(n({variant:a,size:i,className:s})),...c})}},5525:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},5695:(e,s,a)=>{"use strict";var t=a(8999);a.o(t,"useRouter")&&a.d(s,{useRouter:function(){return t.useRouter}})},5868:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},6695:(e,s,a)=>{"use strict";a.d(s,{BT:()=>d,Wu:()=>c,ZB:()=>n,Zp:()=>i,aR:()=>l});var t=a(5155);a(2115);var r=a(9434);function i(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",s),...a})}function l(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...a})}function n(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",s),...a})}function d(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",s),...a})}function c(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",s),...a})}},7607:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>v});var t=a(5155),r=a(5493),i=a(5695),l=a(9946);let n=(0,l.A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]]);var d=a(5525),c=a(5868);let x=(0,l.A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]),m=(0,l.A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]),o=(0,l.A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);var u=a(285),h=a(6695);function v(){var e,s;let{data:a,status:l}=(0,r.wV)(),v=(0,i.useRouter)();return"loading"===l?(0,t.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,t.jsx)("p",{className:"mt-2 text-gray-600",children:"Loading..."})]})}):a?(0,t.jsxs)("div",{className:"p-8",children:[(0,t.jsxs)("div",{className:"mb-8 flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Admin Dashboard"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Enhanced security portal for financial data management"}),(0,t.jsxs)("p",{className:"text-sm text-gray-500 mt-1",children:["Welcome back, ",null==(e=a.user)?void 0:e.name," (",null==(s=a.user)?void 0:s.role,")"]})]}),(0,t.jsxs)(u.$,{onClick:()=>{(0,r.CI)({callbackUrl:"/auth/signin"})},variant:"outline",className:"flex items-center gap-2",children:[(0,t.jsx)(n,{className:"h-4 w-4"}),"Sign Out"]})]}),(0,t.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-8",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(d.A,{className:"h-5 w-5 text-blue-600 mr-2"}),(0,t.jsx)("span",{className:"text-blue-800 font-medium",children:"Enhanced Security Mode Active"})]}),(0,t.jsx)("p",{className:"text-blue-700 text-sm mt-1",children:"All actions are logged and monitored. MFA is required for sensitive operations."})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,t.jsx)(h.Zp,{children:(0,t.jsx)(h.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(c.A,{className:"h-8 w-8 text-green-600"}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Revenue"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"$0"})]})]})})}),(0,t.jsx)(h.Zp,{children:(0,t.jsx)(h.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(x,{className:"h-8 w-8 text-blue-600"}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Active Users"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"0"})]})]})})}),(0,t.jsx)(h.Zp,{children:(0,t.jsx)(h.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(m,{className:"h-8 w-8 text-purple-600"}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Payment Records"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"0"})]})]})})}),(0,t.jsx)(h.Zp,{children:(0,t.jsx)(h.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(o,{className:"h-8 w-8 text-orange-600"}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Reports Generated"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"0"})]})]})})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,t.jsxs)(h.Zp,{children:[(0,t.jsxs)(h.aR,{children:[(0,t.jsx)(h.ZB,{children:"Payment Management"}),(0,t.jsx)(h.BT,{children:"Record and track payment information"})]}),(0,t.jsx)(h.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)(u.$,{variant:"outline",className:"w-full justify-start",onClick:()=>v.push("/dashboard/payments"),children:(0,t.jsxs)("div",{className:"text-left",children:[(0,t.jsx)("div",{className:"font-medium",children:"Record New Payment"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Add payment records for students"})]})}),(0,t.jsx)(u.$,{variant:"outline",className:"w-full justify-start",onClick:()=>v.push("/dashboard/payments"),children:(0,t.jsxs)("div",{className:"text-left",children:[(0,t.jsx)("div",{className:"font-medium",children:"Verify Payments"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Review and verify payment records"})]})}),(0,t.jsx)(u.$,{variant:"outline",className:"w-full justify-start",children:(0,t.jsxs)("div",{className:"text-left",children:[(0,t.jsx)("div",{className:"font-medium",children:"Fee Structures"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Manage course fees and pricing"})]})})]})})]}),(0,t.jsxs)(h.Zp,{children:[(0,t.jsxs)(h.aR,{children:[(0,t.jsx)(h.ZB,{children:"User Management"}),(0,t.jsx)(h.BT,{children:"Manage users across all services"})]}),(0,t.jsx)(h.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)(u.$,{variant:"outline",className:"w-full justify-start",children:(0,t.jsxs)("div",{className:"text-left",children:[(0,t.jsx)("div",{className:"font-medium",children:"Staff Users"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Manage staff service users"})]})}),(0,t.jsx)(u.$,{variant:"outline",className:"w-full justify-start",children:(0,t.jsxs)("div",{className:"text-left",children:[(0,t.jsx)("div",{className:"font-medium",children:"Student Users"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Manage student service users"})]})}),(0,t.jsx)(u.$,{variant:"outline",className:"w-full justify-start",children:(0,t.jsxs)("div",{className:"text-left",children:[(0,t.jsx)("div",{className:"font-medium",children:"Admin Users"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Manage admin service users"})]})})]})})]}),(0,t.jsxs)(h.Zp,{children:[(0,t.jsxs)(h.aR,{children:[(0,t.jsx)(h.ZB,{children:"Financial Reports"}),(0,t.jsx)(h.BT,{children:"Generate and view financial reports"})]}),(0,t.jsx)(h.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)(u.$,{variant:"outline",className:"w-full justify-start",children:(0,t.jsxs)("div",{className:"text-left",children:[(0,t.jsx)("div",{className:"font-medium",children:"Revenue Report"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Monthly and yearly revenue analysis"})]})}),(0,t.jsx)(u.$,{variant:"outline",className:"w-full justify-start",children:(0,t.jsxs)("div",{className:"text-left",children:[(0,t.jsx)("div",{className:"font-medium",children:"Payment Summary"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Payment status and trends"})]})}),(0,t.jsx)(u.$,{variant:"outline",className:"w-full justify-start",children:(0,t.jsxs)("div",{className:"text-left",children:[(0,t.jsx)("div",{className:"font-medium",children:"Audit Trail"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"View system audit logs"})]})})]})})]}),(0,t.jsxs)(h.Zp,{children:[(0,t.jsxs)(h.aR,{children:[(0,t.jsx)(h.ZB,{children:"System Configuration"}),(0,t.jsx)(h.BT,{children:"Configure system settings and security"})]}),(0,t.jsx)(h.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)(u.$,{variant:"outline",className:"w-full justify-start",children:(0,t.jsxs)("div",{className:"text-left",children:[(0,t.jsx)("div",{className:"font-medium",children:"Security Settings"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"MFA, session timeout, access control"})]})}),(0,t.jsx)(u.$,{variant:"outline",className:"w-full justify-start",children:(0,t.jsxs)("div",{className:"text-left",children:[(0,t.jsx)("div",{className:"font-medium",children:"System Configuration"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Business rules and system settings"})]})}),(0,t.jsx)(u.$,{variant:"outline",className:"w-full justify-start",children:(0,t.jsxs)("div",{className:"text-left",children:[(0,t.jsx)("div",{className:"font-medium",children:"Backup & Recovery"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Data backup and recovery options"})]})})]})})]})]})]}):(0,t.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,t.jsx)("div",{className:"text-center",children:(0,t.jsx)("p",{className:"text-gray-600",children:"Please sign in to access the admin dashboard."})})})}},9434:(e,s,a)=>{"use strict";a.d(s,{cn:()=>i});var t=a(2596),r=a(9688);function i(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,r.QP)((0,t.$)(s))}},9964:(e,s,a)=>{Promise.resolve().then(a.bind(a,7607))}},e=>{var s=s=>e(e.s=s);e.O(0,[493,497,441,684,358],()=>s(9964)),_N_E=e.O()}]);