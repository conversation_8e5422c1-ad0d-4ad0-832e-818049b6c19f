import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { 
  NotificationManager, 
  createNotificationManager,
  SimpleEmailProvider,
  type NotificationTemplate,
  type EmailProvider
} from '../lib/notifications';

describe('NotificationManager', () => {
  let notificationManager: NotificationManager;
  let mockEmailProvider: jest.Mocked<EmailProvider>;

  beforeEach(() => {
    mockEmailProvider = {
      sendEmail: jest.fn().mockImplementation(async () => {}),
    } as jest.Mocked<EmailProvider>;
    notificationManager = new NotificationManager(mockEmailProvider);
  });

  describe('template management', () => {
    it('should add and retrieve templates', () => {
      const template: NotificationTemplate = {
        id: 'test-template',
        name: 'Test Template',
        subject: 'Hello {{name}}',
        body: 'Welcome {{name}}, your account is ready!',
        type: 'email',
        variables: ['name'],
        isActive: true,
      };

      notificationManager.addTemplate(template);
      const retrieved = notificationManager.getTemplate('test-template');

      expect(retrieved).toEqual(template);
    });

    it('should return undefined for non-existent template', () => {
      const retrieved = notificationManager.getTemplate('non-existent');
      expect(retrieved).toBeUndefined();
    });
  });

  describe('template interpolation', () => {
    beforeEach(() => {
      const template: NotificationTemplate = {
        id: 'welcome-template',
        name: 'Welcome Template',
        subject: 'Welcome {{name}}!',
        body: 'Hello {{name}}, welcome to {{company}}. Your role is {{role}}.',
        type: 'email',
        variables: ['name', 'company', 'role'],
        isActive: true,
      };
      notificationManager.addTemplate(template);
    });

    it('should interpolate template variables correctly', async () => {
      const notificationId = await notificationManager.sendNotification(
        'welcome-template',
        '<EMAIL>',
        {
          name: 'John Doe',
          company: 'Innovative Centre',
          role: 'Student',
        }
      );

      expect(mockEmailProvider.sendEmail).toHaveBeenCalledWith({
        to: '<EMAIL>',
        subject: 'Welcome John Doe!',
        body: 'Hello John Doe, welcome to Innovative Centre. Your role is Student.',
        data: {
          name: 'John Doe',
          company: 'Innovative Centre',
          role: 'Student',
        },
      });
      expect(notificationId).toBeDefined();
    });

    it('should leave unmatched variables as-is', async () => {
      await notificationManager.sendNotification(
        'welcome-template',
        '<EMAIL>',
        {
          name: 'John Doe',
          // Missing company and role
        }
      );

      expect(mockEmailProvider.sendEmail).toHaveBeenCalledWith({
        to: '<EMAIL>',
        subject: 'Welcome John Doe!',
        body: 'Hello John Doe, welcome to {{company}}. Your role is {{role}}.',
        data: {
          name: 'John Doe',
        },
      });
    });
  });

  describe('direct notifications', () => {
    it('should send direct email notification', async () => {
      const notificationId = await notificationManager.sendDirectNotification(
        'email',
        '<EMAIL>',
        'Test Subject',
        'Test message content',
        {
          priority: 'high',
          data: { customField: 'value' },
        }
      );

      expect(mockEmailProvider.sendEmail).toHaveBeenCalledWith({
        to: '<EMAIL>',
        subject: 'Test Subject',
        body: 'Test message content',
        data: { customField: 'value' },
      });
      expect(notificationId).toBeDefined();
    });

    it('should handle different notification types', async () => {
      const types = ['email', 'sms', 'push', 'system'] as const;
      
      for (const type of types) {
        await notificationManager.sendDirectNotification(
          type,
          'recipient',
          'Subject',
          'Message'
        );
      }

      // Only email should call the email provider
      expect(mockEmailProvider.sendEmail).toHaveBeenCalledTimes(1);
    });
  });

  describe('error handling and retries', () => {
    it('should retry failed email sends', async () => {
      mockEmailProvider.sendEmail
        .mockRejectedValueOnce(new Error('SMTP error'))
        .mockRejectedValueOnce(new Error('SMTP error'))
        .mockResolvedValueOnce(undefined);

      const notificationId = await notificationManager.sendDirectNotification(
        'email',
        '<EMAIL>',
        'Test Subject',
        'Test message'
      );

      // Wait for retries to complete
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(mockEmailProvider.sendEmail).toHaveBeenCalledTimes(3);
      
      const notification = notificationManager.getNotificationStatus(notificationId);
      expect(notification?.status).toBe('sent');
    });

    it('should mark notification as failed after max retries', async () => {
      mockEmailProvider.sendEmail.mockRejectedValue(new Error('Persistent error'));

      const notificationId = await notificationManager.sendDirectNotification(
        'email',
        '<EMAIL>',
        'Test Subject',
        'Test message'
      );

      // Wait for all retries to complete
      await new Promise(resolve => setTimeout(resolve, 500));

      const notification = notificationManager.getNotificationStatus(notificationId);
      expect(notification?.status).toBe('failed');
      expect(notification?.failureReason).toBe('Persistent error');
      expect(notification?.retryCount).toBe(3);
    });
  });

  describe('scheduled notifications', () => {
    it('should not send scheduled notifications immediately', async () => {
      const futureDate = new Date(Date.now() + 60000); // 1 minute from now

      await notificationManager.sendDirectNotification(
        'email',
        '<EMAIL>',
        'Scheduled Subject',
        'Scheduled message',
        { scheduledAt: futureDate }
      );

      expect(mockEmailProvider.sendEmail).not.toHaveBeenCalled();
    });
  });

  describe('announcements', () => {
    it('should create and retrieve announcements', () => {
      const announcementId = notificationManager.createAnnouncement({
        title: 'System Maintenance',
        message: 'The system will be down for maintenance.',
        type: 'warning',
        targetAudience: 'all',
        isActive: true,
        startDate: new Date(),
        createdBy: '<EMAIL>',
      });

      const announcements = notificationManager.getActiveAnnouncements();
      expect(announcements).toHaveLength(1);
      expect(announcements[0].id).toBe(announcementId);
      expect(announcements[0].title).toBe('System Maintenance');
    });

    it('should filter announcements by audience', () => {
      notificationManager.createAnnouncement({
        title: 'Staff Meeting',
        message: 'Monthly staff meeting scheduled.',
        type: 'info',
        targetAudience: 'staff',
        isActive: true,
        startDate: new Date(),
        createdBy: '<EMAIL>',
      });

      notificationManager.createAnnouncement({
        title: 'Student Event',
        message: 'Student graduation ceremony.',
        type: 'success',
        targetAudience: 'students',
        isActive: true,
        startDate: new Date(),
        createdBy: '<EMAIL>',
      });

      const staffAnnouncements = notificationManager.getActiveAnnouncements('staff');
      const studentAnnouncements = notificationManager.getActiveAnnouncements('students');
      const allAnnouncements = notificationManager.getActiveAnnouncements();

      expect(staffAnnouncements).toHaveLength(1);
      expect(staffAnnouncements[0].title).toBe('Staff Meeting');
      
      expect(studentAnnouncements).toHaveLength(1);
      expect(studentAnnouncements[0].title).toBe('Student Event');
      
      expect(allAnnouncements).toHaveLength(2);
    });

    it('should not return expired announcements', () => {
      const pastDate = new Date(Date.now() - 86400000); // 1 day ago

      notificationManager.createAnnouncement({
        title: 'Expired Announcement',
        message: 'This announcement has expired.',
        type: 'info',
        targetAudience: 'all',
        isActive: true,
        startDate: pastDate,
        endDate: pastDate,
        createdBy: '<EMAIL>',
      });

      const announcements = notificationManager.getActiveAnnouncements();
      expect(announcements).toHaveLength(0);
    });
  });

  describe('statistics', () => {
    it('should return notification statistics', async () => {
      await notificationManager.sendDirectNotification('email', '<EMAIL>', 'Subject 1', 'Message 1');
      await notificationManager.sendDirectNotification('sms', '<EMAIL>', 'Subject 2', 'Message 2');
      
      // Mock one failure
      mockEmailProvider.sendEmail.mockRejectedValueOnce(new Error('Failed'));
      await notificationManager.sendDirectNotification('email', '<EMAIL>', 'Subject 3', 'Message 3');

      const stats = notificationManager.getNotificationStats();

      expect(stats.total).toBe(3);
      expect(stats.byType.email).toBe(2);
      expect(stats.byType.sms).toBe(1);
    });
  });

  describe('SimpleEmailProvider', () => {
    it('should create email provider with config', () => {
      const config = {
        smtpHost: 'smtp.example.com',
        smtpPort: 587,
        username: '<EMAIL>',
        password: 'password',
        fromEmail: '<EMAIL>',
      };

      const provider = new SimpleEmailProvider(config);
      expect(provider).toBeInstanceOf(SimpleEmailProvider);
    });
  });

  describe('factory function', () => {
    it('should create NotificationManager instance', () => {
      const manager = createNotificationManager(mockEmailProvider);
      expect(manager).toBeInstanceOf(NotificationManager);
    });

    it('should create NotificationManager without email provider', () => {
      const manager = createNotificationManager();
      expect(manager).toBeInstanceOf(NotificationManager);
    });
  });
});
