"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/jose";
exports.ids = ["vendor-chunks/jose"];
exports.modules = {

/***/ "(rsc)/./node_modules/jose/dist/webapi/jwe/compact/decrypt.js":
/*!**************************************************************!*\
  !*** ./node_modules/jose/dist/webapi/jwe/compact/decrypt.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   compactDecrypt: () => (/* binding */ compactDecrypt)\n/* harmony export */ });\n/* harmony import */ var _flattened_decrypt_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../flattened/decrypt.js */ \"(rsc)/./node_modules/jose/dist/webapi/jwe/flattened/decrypt.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../util/errors.js */ \"(rsc)/./node_modules/jose/dist/webapi/util/errors.js\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../lib/buffer_utils.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/buffer_utils.js\");\n\n\n\nasync function compactDecrypt(jwe, key, options) {\n    if (jwe instanceof Uint8Array) {\n        jwe = _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_0__.decoder.decode(jwe);\n    }\n    if (typeof jwe !== 'string') {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('Compact JWE must be a string or Uint8Array');\n    }\n    const { 0: protectedHeader, 1: encryptedKey, 2: iv, 3: ciphertext, 4: tag, length, } = jwe.split('.');\n    if (length !== 5) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('Invalid Compact JWE');\n    }\n    const decrypted = await (0,_flattened_decrypt_js__WEBPACK_IMPORTED_MODULE_2__.flattenedDecrypt)({\n        ciphertext,\n        iv: iv || undefined,\n        protected: protectedHeader,\n        tag: tag || undefined,\n        encrypted_key: encryptedKey || undefined,\n    }, key, options);\n    const result = { plaintext: decrypted.plaintext, protectedHeader: decrypted.protectedHeader };\n    if (typeof key === 'function') {\n        return { ...result, key: decrypted.key };\n    }\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/jwe/compact/decrypt.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/webapi/jwe/compact/encrypt.js":
/*!**************************************************************!*\
  !*** ./node_modules/jose/dist/webapi/jwe/compact/encrypt.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CompactEncrypt: () => (/* binding */ CompactEncrypt)\n/* harmony export */ });\n/* harmony import */ var _flattened_encrypt_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../flattened/encrypt.js */ \"(rsc)/./node_modules/jose/dist/webapi/jwe/flattened/encrypt.js\");\n\nclass CompactEncrypt {\n    #flattened;\n    constructor(plaintext) {\n        this.#flattened = new _flattened_encrypt_js__WEBPACK_IMPORTED_MODULE_0__.FlattenedEncrypt(plaintext);\n    }\n    setContentEncryptionKey(cek) {\n        this.#flattened.setContentEncryptionKey(cek);\n        return this;\n    }\n    setInitializationVector(iv) {\n        this.#flattened.setInitializationVector(iv);\n        return this;\n    }\n    setProtectedHeader(protectedHeader) {\n        this.#flattened.setProtectedHeader(protectedHeader);\n        return this;\n    }\n    setKeyManagementParameters(parameters) {\n        this.#flattened.setKeyManagementParameters(parameters);\n        return this;\n    }\n    async encrypt(key, options) {\n        const jwe = await this.#flattened.encrypt(key, options);\n        return [jwe.protected, jwe.encrypted_key, jwe.iv, jwe.ciphertext, jwe.tag].join('.');\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L3dlYmFwaS9qd2UvY29tcGFjdC9lbmNyeXB0LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTJEO0FBQ3BEO0FBQ1A7QUFDQTtBQUNBLDhCQUE4QixtRUFBZ0I7QUFDOUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcV2luZG93cyAxMVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxJbm5vdmF0aXZlIENlbnRyZVxcY3JtLXN0YWZmLXNlcnZpY2VcXG5vZGVfbW9kdWxlc1xcam9zZVxcZGlzdFxcd2ViYXBpXFxqd2VcXGNvbXBhY3RcXGVuY3J5cHQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgRmxhdHRlbmVkRW5jcnlwdCB9IGZyb20gJy4uL2ZsYXR0ZW5lZC9lbmNyeXB0LmpzJztcbmV4cG9ydCBjbGFzcyBDb21wYWN0RW5jcnlwdCB7XG4gICAgI2ZsYXR0ZW5lZDtcbiAgICBjb25zdHJ1Y3RvcihwbGFpbnRleHQpIHtcbiAgICAgICAgdGhpcy4jZmxhdHRlbmVkID0gbmV3IEZsYXR0ZW5lZEVuY3J5cHQocGxhaW50ZXh0KTtcbiAgICB9XG4gICAgc2V0Q29udGVudEVuY3J5cHRpb25LZXkoY2VrKSB7XG4gICAgICAgIHRoaXMuI2ZsYXR0ZW5lZC5zZXRDb250ZW50RW5jcnlwdGlvbktleShjZWspO1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9XG4gICAgc2V0SW5pdGlhbGl6YXRpb25WZWN0b3IoaXYpIHtcbiAgICAgICAgdGhpcy4jZmxhdHRlbmVkLnNldEluaXRpYWxpemF0aW9uVmVjdG9yKGl2KTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuICAgIHNldFByb3RlY3RlZEhlYWRlcihwcm90ZWN0ZWRIZWFkZXIpIHtcbiAgICAgICAgdGhpcy4jZmxhdHRlbmVkLnNldFByb3RlY3RlZEhlYWRlcihwcm90ZWN0ZWRIZWFkZXIpO1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9XG4gICAgc2V0S2V5TWFuYWdlbWVudFBhcmFtZXRlcnMocGFyYW1ldGVycykge1xuICAgICAgICB0aGlzLiNmbGF0dGVuZWQuc2V0S2V5TWFuYWdlbWVudFBhcmFtZXRlcnMocGFyYW1ldGVycyk7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbiAgICBhc3luYyBlbmNyeXB0KGtleSwgb3B0aW9ucykge1xuICAgICAgICBjb25zdCBqd2UgPSBhd2FpdCB0aGlzLiNmbGF0dGVuZWQuZW5jcnlwdChrZXksIG9wdGlvbnMpO1xuICAgICAgICByZXR1cm4gW2p3ZS5wcm90ZWN0ZWQsIGp3ZS5lbmNyeXB0ZWRfa2V5LCBqd2UuaXYsIGp3ZS5jaXBoZXJ0ZXh0LCBqd2UudGFnXS5qb2luKCcuJyk7XG4gICAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/jwe/compact/encrypt.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/webapi/jwe/flattened/decrypt.js":
/*!****************************************************************!*\
  !*** ./node_modules/jose/dist/webapi/jwe/flattened/decrypt.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   flattenedDecrypt: () => (/* binding */ flattenedDecrypt)\n/* harmony export */ });\n/* harmony import */ var _util_base64url_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../util/base64url.js */ \"(rsc)/./node_modules/jose/dist/webapi/util/base64url.js\");\n/* harmony import */ var _lib_decrypt_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../lib/decrypt.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/decrypt.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../util/errors.js */ \"(rsc)/./node_modules/jose/dist/webapi/util/errors.js\");\n/* harmony import */ var _lib_is_disjoint_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../lib/is_disjoint.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/is_disjoint.js\");\n/* harmony import */ var _lib_is_object_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../lib/is_object.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/is_object.js\");\n/* harmony import */ var _lib_decrypt_key_management_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../lib/decrypt_key_management.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/decrypt_key_management.js\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../lib/buffer_utils.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/buffer_utils.js\");\n/* harmony import */ var _lib_cek_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../lib/cek.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/cek.js\");\n/* harmony import */ var _lib_validate_crit_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../lib/validate_crit.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/validate_crit.js\");\n/* harmony import */ var _lib_validate_algorithms_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../lib/validate_algorithms.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/validate_algorithms.js\");\n/* harmony import */ var _lib_normalize_key_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../lib/normalize_key.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/normalize_key.js\");\n/* harmony import */ var _lib_check_key_type_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../lib/check_key_type.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/check_key_type.js\");\n\n\n\n\n\n\n\n\n\n\n\n\nasync function flattenedDecrypt(jwe, key, options) {\n    if (!(0,_lib_is_object_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(jwe)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('Flattened JWE must be an object');\n    }\n    if (jwe.protected === undefined && jwe.header === undefined && jwe.unprotected === undefined) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('JOSE Header missing');\n    }\n    if (jwe.iv !== undefined && typeof jwe.iv !== 'string') {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('JWE Initialization Vector incorrect type');\n    }\n    if (typeof jwe.ciphertext !== 'string') {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('JWE Ciphertext missing or incorrect type');\n    }\n    if (jwe.tag !== undefined && typeof jwe.tag !== 'string') {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('JWE Authentication Tag incorrect type');\n    }\n    if (jwe.protected !== undefined && typeof jwe.protected !== 'string') {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('JWE Protected Header incorrect type');\n    }\n    if (jwe.encrypted_key !== undefined && typeof jwe.encrypted_key !== 'string') {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('JWE Encrypted Key incorrect type');\n    }\n    if (jwe.aad !== undefined && typeof jwe.aad !== 'string') {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('JWE AAD incorrect type');\n    }\n    if (jwe.header !== undefined && !(0,_lib_is_object_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(jwe.header)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('JWE Shared Unprotected Header incorrect type');\n    }\n    if (jwe.unprotected !== undefined && !(0,_lib_is_object_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(jwe.unprotected)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('JWE Per-Recipient Unprotected Header incorrect type');\n    }\n    let parsedProt;\n    if (jwe.protected) {\n        try {\n            const protectedHeader = (0,_util_base64url_js__WEBPACK_IMPORTED_MODULE_2__.decode)(jwe.protected);\n            parsedProt = JSON.parse(_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_3__.decoder.decode(protectedHeader));\n        }\n        catch {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('JWE Protected Header is invalid');\n        }\n    }\n    if (!(0,_lib_is_disjoint_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(parsedProt, jwe.header, jwe.unprotected)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('JWE Protected, JWE Unprotected Header, and JWE Per-Recipient Unprotected Header Parameter names must be disjoint');\n    }\n    const joseHeader = {\n        ...parsedProt,\n        ...jwe.header,\n        ...jwe.unprotected,\n    };\n    (0,_lib_validate_crit_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid, new Map(), options?.crit, parsedProt, joseHeader);\n    if (joseHeader.zip !== undefined) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JOSENotSupported('JWE \"zip\" (Compression Algorithm) Header Parameter is not supported.');\n    }\n    const { alg, enc } = joseHeader;\n    if (typeof alg !== 'string' || !alg) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('missing JWE Algorithm (alg) in JWE Header');\n    }\n    if (typeof enc !== 'string' || !enc) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('missing JWE Encryption Algorithm (enc) in JWE Header');\n    }\n    const keyManagementAlgorithms = options && (0,_lib_validate_algorithms_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])('keyManagementAlgorithms', options.keyManagementAlgorithms);\n    const contentEncryptionAlgorithms = options &&\n        (0,_lib_validate_algorithms_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])('contentEncryptionAlgorithms', options.contentEncryptionAlgorithms);\n    if ((keyManagementAlgorithms && !keyManagementAlgorithms.has(alg)) ||\n        (!keyManagementAlgorithms && alg.startsWith('PBES2'))) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JOSEAlgNotAllowed('\"alg\" (Algorithm) Header Parameter value not allowed');\n    }\n    if (contentEncryptionAlgorithms && !contentEncryptionAlgorithms.has(enc)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JOSEAlgNotAllowed('\"enc\" (Encryption Algorithm) Header Parameter value not allowed');\n    }\n    let encryptedKey;\n    if (jwe.encrypted_key !== undefined) {\n        try {\n            encryptedKey = (0,_util_base64url_js__WEBPACK_IMPORTED_MODULE_2__.decode)(jwe.encrypted_key);\n        }\n        catch {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('Failed to base64url decode the encrypted_key');\n        }\n    }\n    let resolvedKey = false;\n    if (typeof key === 'function') {\n        key = await key(parsedProt, jwe);\n        resolvedKey = true;\n    }\n    (0,_lib_check_key_type_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(alg === 'dir' ? enc : alg, key, 'decrypt');\n    const k = await (0,_lib_normalize_key_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(key, alg);\n    let cek;\n    try {\n        cek = await (0,_lib_decrypt_key_management_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(alg, k, encryptedKey, joseHeader, options);\n    }\n    catch (err) {\n        if (err instanceof TypeError || err instanceof _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid || err instanceof _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JOSENotSupported) {\n            throw err;\n        }\n        cek = (0,_lib_cek_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(enc);\n    }\n    let iv;\n    let tag;\n    if (jwe.iv !== undefined) {\n        try {\n            iv = (0,_util_base64url_js__WEBPACK_IMPORTED_MODULE_2__.decode)(jwe.iv);\n        }\n        catch {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('Failed to base64url decode the iv');\n        }\n    }\n    if (jwe.tag !== undefined) {\n        try {\n            tag = (0,_util_base64url_js__WEBPACK_IMPORTED_MODULE_2__.decode)(jwe.tag);\n        }\n        catch {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('Failed to base64url decode the tag');\n        }\n    }\n    const protectedHeader = _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_3__.encoder.encode(jwe.protected ?? '');\n    let additionalData;\n    if (jwe.aad !== undefined) {\n        additionalData = (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_3__.concat)(protectedHeader, _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_3__.encoder.encode('.'), _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_3__.encoder.encode(jwe.aad));\n    }\n    else {\n        additionalData = protectedHeader;\n    }\n    let ciphertext;\n    try {\n        ciphertext = (0,_util_base64url_js__WEBPACK_IMPORTED_MODULE_2__.decode)(jwe.ciphertext);\n    }\n    catch {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('Failed to base64url decode the ciphertext');\n    }\n    const plaintext = await (0,_lib_decrypt_js__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(enc, cek, ciphertext, iv, tag, additionalData);\n    const result = { plaintext };\n    if (jwe.protected !== undefined) {\n        result.protectedHeader = parsedProt;\n    }\n    if (jwe.aad !== undefined) {\n        try {\n            result.additionalAuthenticatedData = (0,_util_base64url_js__WEBPACK_IMPORTED_MODULE_2__.decode)(jwe.aad);\n        }\n        catch {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('Failed to base64url decode the aad');\n        }\n    }\n    if (jwe.unprotected !== undefined) {\n        result.sharedUnprotectedHeader = jwe.unprotected;\n    }\n    if (jwe.header !== undefined) {\n        result.unprotectedHeader = jwe.header;\n    }\n    if (resolvedKey) {\n        return { ...result, key: k };\n    }\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/jwe/flattened/decrypt.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/webapi/jwe/flattened/encrypt.js":
/*!****************************************************************!*\
  !*** ./node_modules/jose/dist/webapi/jwe/flattened/encrypt.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FlattenedEncrypt: () => (/* binding */ FlattenedEncrypt)\n/* harmony export */ });\n/* harmony import */ var _util_base64url_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../util/base64url.js */ \"(rsc)/./node_modules/jose/dist/webapi/util/base64url.js\");\n/* harmony import */ var _lib_private_symbols_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../lib/private_symbols.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/private_symbols.js\");\n/* harmony import */ var _lib_encrypt_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../lib/encrypt.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/encrypt.js\");\n/* harmony import */ var _lib_encrypt_key_management_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../lib/encrypt_key_management.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/encrypt_key_management.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../util/errors.js */ \"(rsc)/./node_modules/jose/dist/webapi/util/errors.js\");\n/* harmony import */ var _lib_is_disjoint_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../lib/is_disjoint.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/is_disjoint.js\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../lib/buffer_utils.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/buffer_utils.js\");\n/* harmony import */ var _lib_validate_crit_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../lib/validate_crit.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/validate_crit.js\");\n/* harmony import */ var _lib_normalize_key_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../lib/normalize_key.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/normalize_key.js\");\n/* harmony import */ var _lib_check_key_type_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../lib/check_key_type.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/check_key_type.js\");\n\n\n\n\n\n\n\n\n\n\nclass FlattenedEncrypt {\n    #plaintext;\n    #protectedHeader;\n    #sharedUnprotectedHeader;\n    #unprotectedHeader;\n    #aad;\n    #cek;\n    #iv;\n    #keyManagementParameters;\n    constructor(plaintext) {\n        if (!(plaintext instanceof Uint8Array)) {\n            throw new TypeError('plaintext must be an instance of Uint8Array');\n        }\n        this.#plaintext = plaintext;\n    }\n    setKeyManagementParameters(parameters) {\n        if (this.#keyManagementParameters) {\n            throw new TypeError('setKeyManagementParameters can only be called once');\n        }\n        this.#keyManagementParameters = parameters;\n        return this;\n    }\n    setProtectedHeader(protectedHeader) {\n        if (this.#protectedHeader) {\n            throw new TypeError('setProtectedHeader can only be called once');\n        }\n        this.#protectedHeader = protectedHeader;\n        return this;\n    }\n    setSharedUnprotectedHeader(sharedUnprotectedHeader) {\n        if (this.#sharedUnprotectedHeader) {\n            throw new TypeError('setSharedUnprotectedHeader can only be called once');\n        }\n        this.#sharedUnprotectedHeader = sharedUnprotectedHeader;\n        return this;\n    }\n    setUnprotectedHeader(unprotectedHeader) {\n        if (this.#unprotectedHeader) {\n            throw new TypeError('setUnprotectedHeader can only be called once');\n        }\n        this.#unprotectedHeader = unprotectedHeader;\n        return this;\n    }\n    setAdditionalAuthenticatedData(aad) {\n        this.#aad = aad;\n        return this;\n    }\n    setContentEncryptionKey(cek) {\n        if (this.#cek) {\n            throw new TypeError('setContentEncryptionKey can only be called once');\n        }\n        this.#cek = cek;\n        return this;\n    }\n    setInitializationVector(iv) {\n        if (this.#iv) {\n            throw new TypeError('setInitializationVector can only be called once');\n        }\n        this.#iv = iv;\n        return this;\n    }\n    async encrypt(key, options) {\n        if (!this.#protectedHeader && !this.#unprotectedHeader && !this.#sharedUnprotectedHeader) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid('either setProtectedHeader, setUnprotectedHeader, or sharedUnprotectedHeader must be called before #encrypt()');\n        }\n        if (!(0,_lib_is_disjoint_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(this.#protectedHeader, this.#unprotectedHeader, this.#sharedUnprotectedHeader)) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid('JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint');\n        }\n        const joseHeader = {\n            ...this.#protectedHeader,\n            ...this.#unprotectedHeader,\n            ...this.#sharedUnprotectedHeader,\n        };\n        (0,_lib_validate_crit_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid, new Map(), options?.crit, this.#protectedHeader, joseHeader);\n        if (joseHeader.zip !== undefined) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JOSENotSupported('JWE \"zip\" (Compression Algorithm) Header Parameter is not supported.');\n        }\n        const { alg, enc } = joseHeader;\n        if (typeof alg !== 'string' || !alg) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid('JWE \"alg\" (Algorithm) Header Parameter missing or invalid');\n        }\n        if (typeof enc !== 'string' || !enc) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid('JWE \"enc\" (Encryption Algorithm) Header Parameter missing or invalid');\n        }\n        let encryptedKey;\n        if (this.#cek && (alg === 'dir' || alg === 'ECDH-ES')) {\n            throw new TypeError(`setContentEncryptionKey cannot be called with JWE \"alg\" (Algorithm) Header ${alg}`);\n        }\n        (0,_lib_check_key_type_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(alg === 'dir' ? enc : alg, key, 'encrypt');\n        let cek;\n        {\n            let parameters;\n            const k = await (0,_lib_normalize_key_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(key, alg);\n            ({ cek, encryptedKey, parameters } = await (0,_lib_encrypt_key_management_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(alg, enc, k, this.#cek, this.#keyManagementParameters));\n            if (parameters) {\n                if (options && _lib_private_symbols_js__WEBPACK_IMPORTED_MODULE_6__.unprotected in options) {\n                    if (!this.#unprotectedHeader) {\n                        this.setUnprotectedHeader(parameters);\n                    }\n                    else {\n                        this.#unprotectedHeader = { ...this.#unprotectedHeader, ...parameters };\n                    }\n                }\n                else if (!this.#protectedHeader) {\n                    this.setProtectedHeader(parameters);\n                }\n                else {\n                    this.#protectedHeader = { ...this.#protectedHeader, ...parameters };\n                }\n            }\n        }\n        let additionalData;\n        let protectedHeader;\n        let aadMember;\n        if (this.#protectedHeader) {\n            protectedHeader = _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_7__.encoder.encode((0,_util_base64url_js__WEBPACK_IMPORTED_MODULE_8__.encode)(JSON.stringify(this.#protectedHeader)));\n        }\n        else {\n            protectedHeader = _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_7__.encoder.encode('');\n        }\n        if (this.#aad) {\n            aadMember = (0,_util_base64url_js__WEBPACK_IMPORTED_MODULE_8__.encode)(this.#aad);\n            additionalData = (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_7__.concat)(protectedHeader, _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_7__.encoder.encode('.'), _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_7__.encoder.encode(aadMember));\n        }\n        else {\n            additionalData = protectedHeader;\n        }\n        const { ciphertext, tag, iv } = await (0,_lib_encrypt_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(enc, this.#plaintext, cek, this.#iv, additionalData);\n        const jwe = {\n            ciphertext: (0,_util_base64url_js__WEBPACK_IMPORTED_MODULE_8__.encode)(ciphertext),\n        };\n        if (iv) {\n            jwe.iv = (0,_util_base64url_js__WEBPACK_IMPORTED_MODULE_8__.encode)(iv);\n        }\n        if (tag) {\n            jwe.tag = (0,_util_base64url_js__WEBPACK_IMPORTED_MODULE_8__.encode)(tag);\n        }\n        if (encryptedKey) {\n            jwe.encrypted_key = (0,_util_base64url_js__WEBPACK_IMPORTED_MODULE_8__.encode)(encryptedKey);\n        }\n        if (aadMember) {\n            jwe.aad = aadMember;\n        }\n        if (this.#protectedHeader) {\n            jwe.protected = _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_7__.decoder.decode(protectedHeader);\n        }\n        if (this.#sharedUnprotectedHeader) {\n            jwe.unprotected = this.#sharedUnprotectedHeader;\n        }\n        if (this.#unprotectedHeader) {\n            jwe.header = this.#unprotectedHeader;\n        }\n        return jwe;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/jwe/flattened/encrypt.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/webapi/jwk/thumbprint.js":
/*!*********************************************************!*\
  !*** ./node_modules/jose/dist/webapi/jwk/thumbprint.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateJwkThumbprint: () => (/* binding */ calculateJwkThumbprint),\n/* harmony export */   calculateJwkThumbprintUri: () => (/* binding */ calculateJwkThumbprintUri)\n/* harmony export */ });\n/* harmony import */ var _lib_digest_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../lib/digest.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/digest.js\");\n/* harmony import */ var _util_base64url_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../util/base64url.js */ \"(rsc)/./node_modules/jose/dist/webapi/util/base64url.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/webapi/util/errors.js\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../lib/buffer_utils.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/buffer_utils.js\");\n/* harmony import */ var _lib_is_key_like_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/is_key_like.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/is_key_like.js\");\n/* harmony import */ var _lib_is_jwk_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/is_jwk.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/is_jwk.js\");\n/* harmony import */ var _key_export_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../key/export.js */ \"(rsc)/./node_modules/jose/dist/webapi/key/export.js\");\n/* harmony import */ var _lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/invalid_key_input.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/invalid_key_input.js\");\n\n\n\n\n\n\n\n\nconst check = (value, description) => {\n    if (typeof value !== 'string' || !value) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWKInvalid(`${description} missing or invalid`);\n    }\n};\nasync function calculateJwkThumbprint(key, digestAlgorithm) {\n    let jwk;\n    if ((0,_lib_is_jwk_js__WEBPACK_IMPORTED_MODULE_1__.isJWK)(key)) {\n        jwk = key;\n    }\n    else if ((0,_lib_is_key_like_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(key)) {\n        jwk = await (0,_key_export_js__WEBPACK_IMPORTED_MODULE_3__.exportJWK)(key);\n    }\n    else {\n        throw new TypeError((0,_lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(key, 'CryptoKey', 'KeyObject', 'JSON Web Key'));\n    }\n    digestAlgorithm ??= 'sha256';\n    if (digestAlgorithm !== 'sha256' &&\n        digestAlgorithm !== 'sha384' &&\n        digestAlgorithm !== 'sha512') {\n        throw new TypeError('digestAlgorithm must one of \"sha256\", \"sha384\", or \"sha512\"');\n    }\n    let components;\n    switch (jwk.kty) {\n        case 'EC':\n            check(jwk.crv, '\"crv\" (Curve) Parameter');\n            check(jwk.x, '\"x\" (X Coordinate) Parameter');\n            check(jwk.y, '\"y\" (Y Coordinate) Parameter');\n            components = { crv: jwk.crv, kty: jwk.kty, x: jwk.x, y: jwk.y };\n            break;\n        case 'OKP':\n            check(jwk.crv, '\"crv\" (Subtype of Key Pair) Parameter');\n            check(jwk.x, '\"x\" (Public Key) Parameter');\n            components = { crv: jwk.crv, kty: jwk.kty, x: jwk.x };\n            break;\n        case 'RSA':\n            check(jwk.e, '\"e\" (Exponent) Parameter');\n            check(jwk.n, '\"n\" (Modulus) Parameter');\n            components = { e: jwk.e, kty: jwk.kty, n: jwk.n };\n            break;\n        case 'oct':\n            check(jwk.k, '\"k\" (Key Value) Parameter');\n            components = { k: jwk.k, kty: jwk.kty };\n            break;\n        default:\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JOSENotSupported('\"kty\" (Key Type) Parameter missing or unsupported');\n    }\n    const data = _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_5__.encoder.encode(JSON.stringify(components));\n    return (0,_util_base64url_js__WEBPACK_IMPORTED_MODULE_6__.encode)(await (0,_lib_digest_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(digestAlgorithm, data));\n}\nasync function calculateJwkThumbprintUri(key, digestAlgorithm) {\n    digestAlgorithm ??= 'sha256';\n    const thumbprint = await calculateJwkThumbprint(key, digestAlgorithm);\n    return `urn:ietf:params:oauth:jwk-thumbprint:sha-${digestAlgorithm.slice(-3)}:${thumbprint}`;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L3dlYmFwaS9qd2svdGh1bWJwcmludC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQXNDO0FBQ2dCO0FBQ1c7QUFDaEI7QUFDSDtBQUNMO0FBQ0k7QUFDYTtBQUMxRDtBQUNBO0FBQ0Esa0JBQWtCLHVEQUFVLElBQUksYUFBYTtBQUM3QztBQUNBO0FBQ087QUFDUDtBQUNBLFFBQVEscURBQUs7QUFDYjtBQUNBO0FBQ0EsYUFBYSwrREFBUztBQUN0QixvQkFBb0IseURBQVM7QUFDN0I7QUFDQTtBQUNBLDRCQUE0QixxRUFBZTtBQUMzQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQjtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQjtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQjtBQUMzQjtBQUNBO0FBQ0E7QUFDQSwyQkFBMkI7QUFDM0I7QUFDQTtBQUNBLHNCQUFzQiw2REFBZ0I7QUFDdEM7QUFDQSxpQkFBaUIseURBQU87QUFDeEIsV0FBVywwREFBSSxPQUFPLDBEQUFNO0FBQzVCO0FBQ087QUFDUDtBQUNBO0FBQ0EsdURBQXVELDBCQUEwQixHQUFHLFdBQVc7QUFDL0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcV2luZG93cyAxMVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxJbm5vdmF0aXZlIENlbnRyZVxcY3JtLXN0YWZmLXNlcnZpY2VcXG5vZGVfbW9kdWxlc1xcam9zZVxcZGlzdFxcd2ViYXBpXFxqd2tcXHRodW1icHJpbnQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGRpZ2VzdCBmcm9tICcuLi9saWIvZGlnZXN0LmpzJztcbmltcG9ydCB7IGVuY29kZSBhcyBiNjR1IH0gZnJvbSAnLi4vdXRpbC9iYXNlNjR1cmwuanMnO1xuaW1wb3J0IHsgSk9TRU5vdFN1cHBvcnRlZCwgSldLSW52YWxpZCB9IGZyb20gJy4uL3V0aWwvZXJyb3JzLmpzJztcbmltcG9ydCB7IGVuY29kZXIgfSBmcm9tICcuLi9saWIvYnVmZmVyX3V0aWxzLmpzJztcbmltcG9ydCBpc0tleUxpa2UgZnJvbSAnLi4vbGliL2lzX2tleV9saWtlLmpzJztcbmltcG9ydCB7IGlzSldLIH0gZnJvbSAnLi4vbGliL2lzX2p3ay5qcyc7XG5pbXBvcnQgeyBleHBvcnRKV0sgfSBmcm9tICcuLi9rZXkvZXhwb3J0LmpzJztcbmltcG9ydCBpbnZhbGlkS2V5SW5wdXQgZnJvbSAnLi4vbGliL2ludmFsaWRfa2V5X2lucHV0LmpzJztcbmNvbnN0IGNoZWNrID0gKHZhbHVlLCBkZXNjcmlwdGlvbikgPT4ge1xuICAgIGlmICh0eXBlb2YgdmFsdWUgIT09ICdzdHJpbmcnIHx8ICF2YWx1ZSkge1xuICAgICAgICB0aHJvdyBuZXcgSldLSW52YWxpZChgJHtkZXNjcmlwdGlvbn0gbWlzc2luZyBvciBpbnZhbGlkYCk7XG4gICAgfVxufTtcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBjYWxjdWxhdGVKd2tUaHVtYnByaW50KGtleSwgZGlnZXN0QWxnb3JpdGhtKSB7XG4gICAgbGV0IGp3aztcbiAgICBpZiAoaXNKV0soa2V5KSkge1xuICAgICAgICBqd2sgPSBrZXk7XG4gICAgfVxuICAgIGVsc2UgaWYgKGlzS2V5TGlrZShrZXkpKSB7XG4gICAgICAgIGp3ayA9IGF3YWl0IGV4cG9ydEpXSyhrZXkpO1xuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcihpbnZhbGlkS2V5SW5wdXQoa2V5LCAnQ3J5cHRvS2V5JywgJ0tleU9iamVjdCcsICdKU09OIFdlYiBLZXknKSk7XG4gICAgfVxuICAgIGRpZ2VzdEFsZ29yaXRobSA/Pz0gJ3NoYTI1Nic7XG4gICAgaWYgKGRpZ2VzdEFsZ29yaXRobSAhPT0gJ3NoYTI1NicgJiZcbiAgICAgICAgZGlnZXN0QWxnb3JpdGhtICE9PSAnc2hhMzg0JyAmJlxuICAgICAgICBkaWdlc3RBbGdvcml0aG0gIT09ICdzaGE1MTInKSB7XG4gICAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoJ2RpZ2VzdEFsZ29yaXRobSBtdXN0IG9uZSBvZiBcInNoYTI1NlwiLCBcInNoYTM4NFwiLCBvciBcInNoYTUxMlwiJyk7XG4gICAgfVxuICAgIGxldCBjb21wb25lbnRzO1xuICAgIHN3aXRjaCAoandrLmt0eSkge1xuICAgICAgICBjYXNlICdFQyc6XG4gICAgICAgICAgICBjaGVjayhqd2suY3J2LCAnXCJjcnZcIiAoQ3VydmUpIFBhcmFtZXRlcicpO1xuICAgICAgICAgICAgY2hlY2soandrLngsICdcInhcIiAoWCBDb29yZGluYXRlKSBQYXJhbWV0ZXInKTtcbiAgICAgICAgICAgIGNoZWNrKGp3ay55LCAnXCJ5XCIgKFkgQ29vcmRpbmF0ZSkgUGFyYW1ldGVyJyk7XG4gICAgICAgICAgICBjb21wb25lbnRzID0geyBjcnY6IGp3ay5jcnYsIGt0eTogandrLmt0eSwgeDogandrLngsIHk6IGp3ay55IH07XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSAnT0tQJzpcbiAgICAgICAgICAgIGNoZWNrKGp3ay5jcnYsICdcImNydlwiIChTdWJ0eXBlIG9mIEtleSBQYWlyKSBQYXJhbWV0ZXInKTtcbiAgICAgICAgICAgIGNoZWNrKGp3ay54LCAnXCJ4XCIgKFB1YmxpYyBLZXkpIFBhcmFtZXRlcicpO1xuICAgICAgICAgICAgY29tcG9uZW50cyA9IHsgY3J2OiBqd2suY3J2LCBrdHk6IGp3ay5rdHksIHg6IGp3ay54IH07XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSAnUlNBJzpcbiAgICAgICAgICAgIGNoZWNrKGp3ay5lLCAnXCJlXCIgKEV4cG9uZW50KSBQYXJhbWV0ZXInKTtcbiAgICAgICAgICAgIGNoZWNrKGp3ay5uLCAnXCJuXCIgKE1vZHVsdXMpIFBhcmFtZXRlcicpO1xuICAgICAgICAgICAgY29tcG9uZW50cyA9IHsgZTogandrLmUsIGt0eTogandrLmt0eSwgbjogandrLm4gfTtcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlICdvY3QnOlxuICAgICAgICAgICAgY2hlY2soandrLmssICdcImtcIiAoS2V5IFZhbHVlKSBQYXJhbWV0ZXInKTtcbiAgICAgICAgICAgIGNvbXBvbmVudHMgPSB7IGs6IGp3ay5rLCBrdHk6IGp3ay5rdHkgfTtcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgdGhyb3cgbmV3IEpPU0VOb3RTdXBwb3J0ZWQoJ1wia3R5XCIgKEtleSBUeXBlKSBQYXJhbWV0ZXIgbWlzc2luZyBvciB1bnN1cHBvcnRlZCcpO1xuICAgIH1cbiAgICBjb25zdCBkYXRhID0gZW5jb2Rlci5lbmNvZGUoSlNPTi5zdHJpbmdpZnkoY29tcG9uZW50cykpO1xuICAgIHJldHVybiBiNjR1KGF3YWl0IGRpZ2VzdChkaWdlc3RBbGdvcml0aG0sIGRhdGEpKTtcbn1cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBjYWxjdWxhdGVKd2tUaHVtYnByaW50VXJpKGtleSwgZGlnZXN0QWxnb3JpdGhtKSB7XG4gICAgZGlnZXN0QWxnb3JpdGhtID8/PSAnc2hhMjU2JztcbiAgICBjb25zdCB0aHVtYnByaW50ID0gYXdhaXQgY2FsY3VsYXRlSndrVGh1bWJwcmludChrZXksIGRpZ2VzdEFsZ29yaXRobSk7XG4gICAgcmV0dXJuIGB1cm46aWV0ZjpwYXJhbXM6b2F1dGg6andrLXRodW1icHJpbnQ6c2hhLSR7ZGlnZXN0QWxnb3JpdGhtLnNsaWNlKC0zKX06JHt0aHVtYnByaW50fWA7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/jwk/thumbprint.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/webapi/jwt/decrypt.js":
/*!******************************************************!*\
  !*** ./node_modules/jose/dist/webapi/jwt/decrypt.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   jwtDecrypt: () => (/* binding */ jwtDecrypt)\n/* harmony export */ });\n/* harmony import */ var _jwe_compact_decrypt_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../jwe/compact/decrypt.js */ \"(rsc)/./node_modules/jose/dist/webapi/jwe/compact/decrypt.js\");\n/* harmony import */ var _lib_jwt_claims_set_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/jwt_claims_set.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/jwt_claims_set.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/webapi/util/errors.js\");\n\n\n\nasync function jwtDecrypt(jwt, key, options) {\n    const decrypted = await (0,_jwe_compact_decrypt_js__WEBPACK_IMPORTED_MODULE_0__.compactDecrypt)(jwt, key, options);\n    const payload = (0,_lib_jwt_claims_set_js__WEBPACK_IMPORTED_MODULE_1__.validateClaimsSet)(decrypted.protectedHeader, decrypted.plaintext, options);\n    const { protectedHeader } = decrypted;\n    if (protectedHeader.iss !== undefined && protectedHeader.iss !== payload.iss) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('replicated \"iss\" claim header parameter mismatch', payload, 'iss', 'mismatch');\n    }\n    if (protectedHeader.sub !== undefined && protectedHeader.sub !== payload.sub) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('replicated \"sub\" claim header parameter mismatch', payload, 'sub', 'mismatch');\n    }\n    if (protectedHeader.aud !== undefined &&\n        JSON.stringify(protectedHeader.aud) !== JSON.stringify(payload.aud)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('replicated \"aud\" claim header parameter mismatch', payload, 'aud', 'mismatch');\n    }\n    const result = { payload, protectedHeader };\n    if (typeof key === 'function') {\n        return { ...result, key: decrypted.key };\n    }\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/jwt/decrypt.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/webapi/jwt/encrypt.js":
/*!******************************************************!*\
  !*** ./node_modules/jose/dist/webapi/jwt/encrypt.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EncryptJWT: () => (/* binding */ EncryptJWT)\n/* harmony export */ });\n/* harmony import */ var _jwe_compact_encrypt_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../jwe/compact/encrypt.js */ \"(rsc)/./node_modules/jose/dist/webapi/jwe/compact/encrypt.js\");\n/* harmony import */ var _lib_jwt_claims_set_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib/jwt_claims_set.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/jwt_claims_set.js\");\n\n\nclass EncryptJWT {\n    #cek;\n    #iv;\n    #keyManagementParameters;\n    #protectedHeader;\n    #replicateIssuerAsHeader;\n    #replicateSubjectAsHeader;\n    #replicateAudienceAsHeader;\n    #jwt;\n    constructor(payload = {}) {\n        this.#jwt = new _lib_jwt_claims_set_js__WEBPACK_IMPORTED_MODULE_0__.JWTClaimsBuilder(payload);\n    }\n    setIssuer(issuer) {\n        this.#jwt.iss = issuer;\n        return this;\n    }\n    setSubject(subject) {\n        this.#jwt.sub = subject;\n        return this;\n    }\n    setAudience(audience) {\n        this.#jwt.aud = audience;\n        return this;\n    }\n    setJti(jwtId) {\n        this.#jwt.jti = jwtId;\n        return this;\n    }\n    setNotBefore(input) {\n        this.#jwt.nbf = input;\n        return this;\n    }\n    setExpirationTime(input) {\n        this.#jwt.exp = input;\n        return this;\n    }\n    setIssuedAt(input) {\n        this.#jwt.iat = input;\n        return this;\n    }\n    setProtectedHeader(protectedHeader) {\n        if (this.#protectedHeader) {\n            throw new TypeError('setProtectedHeader can only be called once');\n        }\n        this.#protectedHeader = protectedHeader;\n        return this;\n    }\n    setKeyManagementParameters(parameters) {\n        if (this.#keyManagementParameters) {\n            throw new TypeError('setKeyManagementParameters can only be called once');\n        }\n        this.#keyManagementParameters = parameters;\n        return this;\n    }\n    setContentEncryptionKey(cek) {\n        if (this.#cek) {\n            throw new TypeError('setContentEncryptionKey can only be called once');\n        }\n        this.#cek = cek;\n        return this;\n    }\n    setInitializationVector(iv) {\n        if (this.#iv) {\n            throw new TypeError('setInitializationVector can only be called once');\n        }\n        this.#iv = iv;\n        return this;\n    }\n    replicateIssuerAsHeader() {\n        this.#replicateIssuerAsHeader = true;\n        return this;\n    }\n    replicateSubjectAsHeader() {\n        this.#replicateSubjectAsHeader = true;\n        return this;\n    }\n    replicateAudienceAsHeader() {\n        this.#replicateAudienceAsHeader = true;\n        return this;\n    }\n    async encrypt(key, options) {\n        const enc = new _jwe_compact_encrypt_js__WEBPACK_IMPORTED_MODULE_1__.CompactEncrypt(this.#jwt.data());\n        if (this.#protectedHeader &&\n            (this.#replicateIssuerAsHeader ||\n                this.#replicateSubjectAsHeader ||\n                this.#replicateAudienceAsHeader)) {\n            this.#protectedHeader = {\n                ...this.#protectedHeader,\n                iss: this.#replicateIssuerAsHeader ? this.#jwt.iss : undefined,\n                sub: this.#replicateSubjectAsHeader ? this.#jwt.sub : undefined,\n                aud: this.#replicateAudienceAsHeader ? this.#jwt.aud : undefined,\n            };\n        }\n        enc.setProtectedHeader(this.#protectedHeader);\n        if (this.#iv) {\n            enc.setInitializationVector(this.#iv);\n        }\n        if (this.#cek) {\n            enc.setContentEncryptionKey(this.#cek);\n        }\n        if (this.#keyManagementParameters) {\n            enc.setKeyManagementParameters(this.#keyManagementParameters);\n        }\n        return enc.encrypt(key, options);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/jwt/encrypt.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/webapi/key/export.js":
/*!*****************************************************!*\
  !*** ./node_modules/jose/dist/webapi/key/export.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   exportJWK: () => (/* binding */ exportJWK),\n/* harmony export */   exportPKCS8: () => (/* binding */ exportPKCS8),\n/* harmony export */   exportSPKI: () => (/* binding */ exportSPKI)\n/* harmony export */ });\n/* harmony import */ var _lib_asn1_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib/asn1.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/asn1.js\");\n/* harmony import */ var _lib_key_to_jwk_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/key_to_jwk.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/key_to_jwk.js\");\n\n\nasync function exportSPKI(key) {\n    return (0,_lib_asn1_js__WEBPACK_IMPORTED_MODULE_0__.toSPKI)(key);\n}\nasync function exportPKCS8(key) {\n    return (0,_lib_asn1_js__WEBPACK_IMPORTED_MODULE_0__.toPKCS8)(key);\n}\nasync function exportJWK(key) {\n    return (0,_lib_key_to_jwk_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(key);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L3dlYmFwaS9rZXkvZXhwb3J0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQWtGO0FBQ3RDO0FBQ3JDO0FBQ1AsV0FBVyxvREFBWTtBQUN2QjtBQUNPO0FBQ1AsV0FBVyxxREFBYTtBQUN4QjtBQUNPO0FBQ1AsV0FBVyw4REFBUTtBQUNuQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxXaW5kb3dzIDExXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXElubm92YXRpdmUgQ2VudHJlXFxjcm0tc3RhZmYtc2VydmljZVxcbm9kZV9tb2R1bGVzXFxqb3NlXFxkaXN0XFx3ZWJhcGlcXGtleVxcZXhwb3J0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHRvU1BLSSBhcyBleHBvcnRQdWJsaWMsIHRvUEtDUzggYXMgZXhwb3J0UHJpdmF0ZSB9IGZyb20gJy4uL2xpYi9hc24xLmpzJztcbmltcG9ydCBrZXlUb0pXSyBmcm9tICcuLi9saWIva2V5X3RvX2p3ay5qcyc7XG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZXhwb3J0U1BLSShrZXkpIHtcbiAgICByZXR1cm4gZXhwb3J0UHVibGljKGtleSk7XG59XG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZXhwb3J0UEtDUzgoa2V5KSB7XG4gICAgcmV0dXJuIGV4cG9ydFByaXZhdGUoa2V5KTtcbn1cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBleHBvcnRKV0soa2V5KSB7XG4gICAgcmV0dXJuIGtleVRvSldLKGtleSk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/key/export.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/webapi/key/import.js":
/*!*****************************************************!*\
  !*** ./node_modules/jose/dist/webapi/key/import.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   importJWK: () => (/* binding */ importJWK),\n/* harmony export */   importPKCS8: () => (/* binding */ importPKCS8),\n/* harmony export */   importSPKI: () => (/* binding */ importSPKI),\n/* harmony export */   importX509: () => (/* binding */ importX509)\n/* harmony export */ });\n/* harmony import */ var _util_base64url_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/base64url.js */ \"(rsc)/./node_modules/jose/dist/webapi/util/base64url.js\");\n/* harmony import */ var _lib_asn1_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib/asn1.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/asn1.js\");\n/* harmony import */ var _lib_jwk_to_key_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/jwk_to_key.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/jwk_to_key.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/webapi/util/errors.js\");\n/* harmony import */ var _lib_is_object_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/is_object.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/is_object.js\");\n\n\n\n\n\nasync function importSPKI(spki, alg, options) {\n    if (typeof spki !== 'string' || spki.indexOf('-----BEGIN PUBLIC KEY-----') !== 0) {\n        throw new TypeError('\"spki\" must be SPKI formatted string');\n    }\n    return (0,_lib_asn1_js__WEBPACK_IMPORTED_MODULE_0__.fromSPKI)(spki, alg, options);\n}\nasync function importX509(x509, alg, options) {\n    if (typeof x509 !== 'string' || x509.indexOf('-----BEGIN CERTIFICATE-----') !== 0) {\n        throw new TypeError('\"x509\" must be X.509 formatted string');\n    }\n    return (0,_lib_asn1_js__WEBPACK_IMPORTED_MODULE_0__.fromX509)(x509, alg, options);\n}\nasync function importPKCS8(pkcs8, alg, options) {\n    if (typeof pkcs8 !== 'string' || pkcs8.indexOf('-----BEGIN PRIVATE KEY-----') !== 0) {\n        throw new TypeError('\"pkcs8\" must be PKCS#8 formatted string');\n    }\n    return (0,_lib_asn1_js__WEBPACK_IMPORTED_MODULE_0__.fromPKCS8)(pkcs8, alg, options);\n}\nasync function importJWK(jwk, alg, options) {\n    if (!(0,_lib_is_object_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(jwk)) {\n        throw new TypeError('JWK must be an object');\n    }\n    let ext;\n    alg ??= jwk.alg;\n    ext ??= options?.extractable ?? jwk.ext;\n    switch (jwk.kty) {\n        case 'oct':\n            if (typeof jwk.k !== 'string' || !jwk.k) {\n                throw new TypeError('missing \"k\" (Key Value) Parameter value');\n            }\n            return (0,_util_base64url_js__WEBPACK_IMPORTED_MODULE_2__.decode)(jwk.k);\n        case 'RSA':\n            if ('oth' in jwk && jwk.oth !== undefined) {\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JOSENotSupported('RSA JWK \"oth\" (Other Primes Info) Parameter value is not supported');\n            }\n        case 'EC':\n        case 'OKP':\n            return (0,_lib_jwk_to_key_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])({ ...jwk, alg, ext });\n        default:\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JOSENotSupported('Unsupported \"kty\" (Key Type) Parameter value');\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/key/import.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/webapi/lib/aesgcmkw.js":
/*!*******************************************************!*\
  !*** ./node_modules/jose/dist/webapi/lib/aesgcmkw.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   unwrap: () => (/* binding */ unwrap),\n/* harmony export */   wrap: () => (/* binding */ wrap)\n/* harmony export */ });\n/* harmony import */ var _encrypt_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./encrypt.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/encrypt.js\");\n/* harmony import */ var _decrypt_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./decrypt.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/decrypt.js\");\n/* harmony import */ var _util_base64url_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/base64url.js */ \"(rsc)/./node_modules/jose/dist/webapi/util/base64url.js\");\n\n\n\nasync function wrap(alg, key, cek, iv) {\n    const jweAlgorithm = alg.slice(0, 7);\n    const wrapped = await (0,_encrypt_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(jweAlgorithm, cek, key, iv, new Uint8Array(0));\n    return {\n        encryptedKey: wrapped.ciphertext,\n        iv: (0,_util_base64url_js__WEBPACK_IMPORTED_MODULE_1__.encode)(wrapped.iv),\n        tag: (0,_util_base64url_js__WEBPACK_IMPORTED_MODULE_1__.encode)(wrapped.tag),\n    };\n}\nasync function unwrap(alg, key, encryptedKey, iv, tag) {\n    const jweAlgorithm = alg.slice(0, 7);\n    return (0,_decrypt_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(jweAlgorithm, key, encryptedKey, iv, tag, new Uint8Array(0));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L3dlYmFwaS9saWIvYWVzZ2Nta3cuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBbUM7QUFDQTtBQUNtQjtBQUMvQztBQUNQO0FBQ0EsMEJBQTBCLHVEQUFPO0FBQ2pDO0FBQ0E7QUFDQSxZQUFZLDBEQUFJO0FBQ2hCLGFBQWEsMERBQUk7QUFDakI7QUFDQTtBQUNPO0FBQ1A7QUFDQSxXQUFXLHVEQUFPO0FBQ2xCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFdpbmRvd3MgMTFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcSW5ub3ZhdGl2ZSBDZW50cmVcXGNybS1zdGFmZi1zZXJ2aWNlXFxub2RlX21vZHVsZXNcXGpvc2VcXGRpc3RcXHdlYmFwaVxcbGliXFxhZXNnY21rdy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgZW5jcnlwdCBmcm9tICcuL2VuY3J5cHQuanMnO1xuaW1wb3J0IGRlY3J5cHQgZnJvbSAnLi9kZWNyeXB0LmpzJztcbmltcG9ydCB7IGVuY29kZSBhcyBiNjR1IH0gZnJvbSAnLi4vdXRpbC9iYXNlNjR1cmwuanMnO1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHdyYXAoYWxnLCBrZXksIGNlaywgaXYpIHtcbiAgICBjb25zdCBqd2VBbGdvcml0aG0gPSBhbGcuc2xpY2UoMCwgNyk7XG4gICAgY29uc3Qgd3JhcHBlZCA9IGF3YWl0IGVuY3J5cHQoandlQWxnb3JpdGhtLCBjZWssIGtleSwgaXYsIG5ldyBVaW50OEFycmF5KDApKTtcbiAgICByZXR1cm4ge1xuICAgICAgICBlbmNyeXB0ZWRLZXk6IHdyYXBwZWQuY2lwaGVydGV4dCxcbiAgICAgICAgaXY6IGI2NHUod3JhcHBlZC5pdiksXG4gICAgICAgIHRhZzogYjY0dSh3cmFwcGVkLnRhZyksXG4gICAgfTtcbn1cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiB1bndyYXAoYWxnLCBrZXksIGVuY3J5cHRlZEtleSwgaXYsIHRhZykge1xuICAgIGNvbnN0IGp3ZUFsZ29yaXRobSA9IGFsZy5zbGljZSgwLCA3KTtcbiAgICByZXR1cm4gZGVjcnlwdChqd2VBbGdvcml0aG0sIGtleSwgZW5jcnlwdGVkS2V5LCBpdiwgdGFnLCBuZXcgVWludDhBcnJheSgwKSk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/lib/aesgcmkw.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/webapi/lib/aeskw.js":
/*!****************************************************!*\
  !*** ./node_modules/jose/dist/webapi/lib/aeskw.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   unwrap: () => (/* binding */ unwrap),\n/* harmony export */   wrap: () => (/* binding */ wrap)\n/* harmony export */ });\n/* harmony import */ var _crypto_key_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./crypto_key.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/crypto_key.js\");\n\nfunction checkKeySize(key, alg) {\n    if (key.algorithm.length !== parseInt(alg.slice(1, 4), 10)) {\n        throw new TypeError(`Invalid key size for alg: ${alg}`);\n    }\n}\nfunction getCryptoKey(key, alg, usage) {\n    if (key instanceof Uint8Array) {\n        return crypto.subtle.importKey('raw', key, 'AES-KW', true, [usage]);\n    }\n    (0,_crypto_key_js__WEBPACK_IMPORTED_MODULE_0__.checkEncCryptoKey)(key, alg, usage);\n    return key;\n}\nasync function wrap(alg, key, cek) {\n    const cryptoKey = await getCryptoKey(key, alg, 'wrapKey');\n    checkKeySize(cryptoKey, alg);\n    const cryptoKeyCek = await crypto.subtle.importKey('raw', cek, { hash: 'SHA-256', name: 'HMAC' }, true, ['sign']);\n    return new Uint8Array(await crypto.subtle.wrapKey('raw', cryptoKeyCek, cryptoKey, 'AES-KW'));\n}\nasync function unwrap(alg, key, encryptedKey) {\n    const cryptoKey = await getCryptoKey(key, alg, 'unwrapKey');\n    checkKeySize(cryptoKey, alg);\n    const cryptoKeyCek = await crypto.subtle.unwrapKey('raw', encryptedKey, cryptoKey, 'AES-KW', { hash: 'SHA-256', name: 'HMAC' }, true, ['sign']);\n    return new Uint8Array(await crypto.subtle.exportKey('raw', cryptoKeyCek));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/lib/aeskw.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/webapi/lib/asn1.js":
/*!***************************************************!*\
  !*** ./node_modules/jose/dist/webapi/lib/asn1.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromPKCS8: () => (/* binding */ fromPKCS8),\n/* harmony export */   fromSPKI: () => (/* binding */ fromSPKI),\n/* harmony export */   fromX509: () => (/* binding */ fromX509),\n/* harmony export */   toPKCS8: () => (/* binding */ toPKCS8),\n/* harmony export */   toSPKI: () => (/* binding */ toSPKI)\n/* harmony export */ });\n/* harmony import */ var _invalid_key_input_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./invalid_key_input.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/invalid_key_input.js\");\n/* harmony import */ var _lib_base64_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/base64.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/base64.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/webapi/util/errors.js\");\n/* harmony import */ var _is_key_like_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is_key_like.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/is_key_like.js\");\n\n\n\n\nconst formatPEM = (b64, descriptor) => {\n    const newlined = (b64.match(/.{1,64}/g) || []).join('\\n');\n    return `-----BEGIN ${descriptor}-----\\n${newlined}\\n-----END ${descriptor}-----`;\n};\nconst genericExport = async (keyType, keyFormat, key) => {\n    if ((0,_is_key_like_js__WEBPACK_IMPORTED_MODULE_0__.isKeyObject)(key)) {\n        if (key.type !== keyType) {\n            throw new TypeError(`key is not a ${keyType} key`);\n        }\n        return key.export({ format: 'pem', type: keyFormat });\n    }\n    if (!(0,_is_key_like_js__WEBPACK_IMPORTED_MODULE_0__.isCryptoKey)(key)) {\n        throw new TypeError((0,_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(key, 'CryptoKey', 'KeyObject'));\n    }\n    if (!key.extractable) {\n        throw new TypeError('CryptoKey is not extractable');\n    }\n    if (key.type !== keyType) {\n        throw new TypeError(`key is not a ${keyType} key`);\n    }\n    return formatPEM((0,_lib_base64_js__WEBPACK_IMPORTED_MODULE_2__.encodeBase64)(new Uint8Array(await crypto.subtle.exportKey(keyFormat, key))), `${keyType.toUpperCase()} KEY`);\n};\nconst toSPKI = (key) => {\n    return genericExport('public', 'spki', key);\n};\nconst toPKCS8 = (key) => {\n    return genericExport('private', 'pkcs8', key);\n};\nconst findOid = (keyData, oid, from = 0) => {\n    if (from === 0) {\n        oid.unshift(oid.length);\n        oid.unshift(0x06);\n    }\n    const i = keyData.indexOf(oid[0], from);\n    if (i === -1)\n        return false;\n    const sub = keyData.subarray(i, i + oid.length);\n    if (sub.length !== oid.length)\n        return false;\n    return sub.every((value, index) => value === oid[index]) || findOid(keyData, oid, i + 1);\n};\nconst getNamedCurve = (keyData) => {\n    switch (true) {\n        case findOid(keyData, [0x2a, 0x86, 0x48, 0xce, 0x3d, 0x03, 0x01, 0x07]):\n            return 'P-256';\n        case findOid(keyData, [0x2b, 0x81, 0x04, 0x00, 0x22]):\n            return 'P-384';\n        case findOid(keyData, [0x2b, 0x81, 0x04, 0x00, 0x23]):\n            return 'P-521';\n        default:\n            return undefined;\n    }\n};\nconst genericImport = async (replace, keyFormat, pem, alg, options) => {\n    let algorithm;\n    let keyUsages;\n    const keyData = new Uint8Array(atob(pem.replace(replace, ''))\n        .split('')\n        .map((c) => c.charCodeAt(0)));\n    const isPublic = keyFormat === 'spki';\n    switch (alg) {\n        case 'PS256':\n        case 'PS384':\n        case 'PS512':\n            algorithm = { name: 'RSA-PSS', hash: `SHA-${alg.slice(-3)}` };\n            keyUsages = isPublic ? ['verify'] : ['sign'];\n            break;\n        case 'RS256':\n        case 'RS384':\n        case 'RS512':\n            algorithm = { name: 'RSASSA-PKCS1-v1_5', hash: `SHA-${alg.slice(-3)}` };\n            keyUsages = isPublic ? ['verify'] : ['sign'];\n            break;\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512':\n            algorithm = {\n                name: 'RSA-OAEP',\n                hash: `SHA-${parseInt(alg.slice(-3), 10) || 1}`,\n            };\n            keyUsages = isPublic ? ['encrypt', 'wrapKey'] : ['decrypt', 'unwrapKey'];\n            break;\n        case 'ES256':\n            algorithm = { name: 'ECDSA', namedCurve: 'P-256' };\n            keyUsages = isPublic ? ['verify'] : ['sign'];\n            break;\n        case 'ES384':\n            algorithm = { name: 'ECDSA', namedCurve: 'P-384' };\n            keyUsages = isPublic ? ['verify'] : ['sign'];\n            break;\n        case 'ES512':\n            algorithm = { name: 'ECDSA', namedCurve: 'P-521' };\n            keyUsages = isPublic ? ['verify'] : ['sign'];\n            break;\n        case 'ECDH-ES':\n        case 'ECDH-ES+A128KW':\n        case 'ECDH-ES+A192KW':\n        case 'ECDH-ES+A256KW': {\n            const namedCurve = getNamedCurve(keyData);\n            algorithm = namedCurve?.startsWith('P-') ? { name: 'ECDH', namedCurve } : { name: 'X25519' };\n            keyUsages = isPublic ? [] : ['deriveBits'];\n            break;\n        }\n        case 'Ed25519':\n        case 'EdDSA':\n            algorithm = { name: 'Ed25519' };\n            keyUsages = isPublic ? ['verify'] : ['sign'];\n            break;\n        default:\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JOSENotSupported('Invalid or unsupported \"alg\" (Algorithm) value');\n    }\n    return crypto.subtle.importKey(keyFormat, keyData, algorithm, options?.extractable ?? (isPublic ? true : false), keyUsages);\n};\nconst fromPKCS8 = (pem, alg, options) => {\n    return genericImport(/(?:-----(?:BEGIN|END) PRIVATE KEY-----|\\s)/g, 'pkcs8', pem, alg, options);\n};\nconst fromSPKI = (pem, alg, options) => {\n    return genericImport(/(?:-----(?:BEGIN|END) PUBLIC KEY-----|\\s)/g, 'spki', pem, alg, options);\n};\nfunction getElement(seq) {\n    const result = [];\n    let next = 0;\n    while (next < seq.length) {\n        const nextPart = parseElement(seq.subarray(next));\n        result.push(nextPart);\n        next += nextPart.byteLength;\n    }\n    return result;\n}\nfunction parseElement(bytes) {\n    let position = 0;\n    let tag = bytes[0] & 0x1f;\n    position++;\n    if (tag === 0x1f) {\n        tag = 0;\n        while (bytes[position] >= 0x80) {\n            tag = tag * 128 + bytes[position] - 0x80;\n            position++;\n        }\n        tag = tag * 128 + bytes[position] - 0x80;\n        position++;\n    }\n    let length = 0;\n    if (bytes[position] < 0x80) {\n        length = bytes[position];\n        position++;\n    }\n    else if (length === 0x80) {\n        length = 0;\n        while (bytes[position + length] !== 0 || bytes[position + length + 1] !== 0) {\n            if (length > bytes.byteLength) {\n                throw new TypeError('invalid indefinite form length');\n            }\n            length++;\n        }\n        const byteLength = position + length + 2;\n        return {\n            byteLength,\n            contents: bytes.subarray(position, position + length),\n            raw: bytes.subarray(0, byteLength),\n        };\n    }\n    else {\n        const numberOfDigits = bytes[position] & 0x7f;\n        position++;\n        length = 0;\n        for (let i = 0; i < numberOfDigits; i++) {\n            length = length * 256 + bytes[position];\n            position++;\n        }\n    }\n    const byteLength = position + length;\n    return {\n        byteLength,\n        contents: bytes.subarray(position, byteLength),\n        raw: bytes.subarray(0, byteLength),\n    };\n}\nfunction spkiFromX509(buf) {\n    const tbsCertificate = getElement(getElement(parseElement(buf).contents)[0].contents);\n    return (0,_lib_base64_js__WEBPACK_IMPORTED_MODULE_2__.encodeBase64)(tbsCertificate[tbsCertificate[0].raw[0] === 0xa0 ? 6 : 5].raw);\n}\nlet createPublicKey;\nfunction getSPKI(x509) {\n    try {\n        createPublicKey ??= globalThis.process?.getBuiltinModule?.('node:crypto')?.createPublicKey;\n    }\n    catch {\n        createPublicKey = 0;\n    }\n    if (createPublicKey) {\n        try {\n            return new createPublicKey(x509).export({ format: 'pem', type: 'spki' });\n        }\n        catch { }\n    }\n    const pem = x509.replace(/(?:-----(?:BEGIN|END) CERTIFICATE-----|\\s)/g, '');\n    const raw = (0,_lib_base64_js__WEBPACK_IMPORTED_MODULE_2__.decodeBase64)(pem);\n    return formatPEM(spkiFromX509(raw), 'PUBLIC KEY');\n}\nconst fromX509 = (pem, alg, options) => {\n    let spki;\n    try {\n        spki = getSPKI(pem);\n    }\n    catch (cause) {\n        throw new TypeError('Failed to parse the X.509 certificate', { cause });\n    }\n    return fromSPKI(spki, alg, options);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/lib/asn1.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/webapi/lib/base64.js":
/*!*****************************************************!*\
  !*** ./node_modules/jose/dist/webapi/lib/base64.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decodeBase64: () => (/* binding */ decodeBase64),\n/* harmony export */   encodeBase64: () => (/* binding */ encodeBase64)\n/* harmony export */ });\nfunction encodeBase64(input) {\n    if (Uint8Array.prototype.toBase64) {\n        return input.toBase64();\n    }\n    const CHUNK_SIZE = 0x8000;\n    const arr = [];\n    for (let i = 0; i < input.length; i += CHUNK_SIZE) {\n        arr.push(String.fromCharCode.apply(null, input.subarray(i, i + CHUNK_SIZE)));\n    }\n    return btoa(arr.join(''));\n}\nfunction decodeBase64(encoded) {\n    if (Uint8Array.fromBase64) {\n        return Uint8Array.fromBase64(encoded);\n    }\n    const binary = atob(encoded);\n    const bytes = new Uint8Array(binary.length);\n    for (let i = 0; i < binary.length; i++) {\n        bytes[i] = binary.charCodeAt(i);\n    }\n    return bytes;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L3dlYmFwaS9saWIvYmFzZTY0LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLGtCQUFrQjtBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixtQkFBbUI7QUFDdkM7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcV2luZG93cyAxMVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxJbm5vdmF0aXZlIENlbnRyZVxcY3JtLXN0YWZmLXNlcnZpY2VcXG5vZGVfbW9kdWxlc1xcam9zZVxcZGlzdFxcd2ViYXBpXFxsaWJcXGJhc2U2NC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gZW5jb2RlQmFzZTY0KGlucHV0KSB7XG4gICAgaWYgKFVpbnQ4QXJyYXkucHJvdG90eXBlLnRvQmFzZTY0KSB7XG4gICAgICAgIHJldHVybiBpbnB1dC50b0Jhc2U2NCgpO1xuICAgIH1cbiAgICBjb25zdCBDSFVOS19TSVpFID0gMHg4MDAwO1xuICAgIGNvbnN0IGFyciA9IFtdO1xuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgaW5wdXQubGVuZ3RoOyBpICs9IENIVU5LX1NJWkUpIHtcbiAgICAgICAgYXJyLnB1c2goU3RyaW5nLmZyb21DaGFyQ29kZS5hcHBseShudWxsLCBpbnB1dC5zdWJhcnJheShpLCBpICsgQ0hVTktfU0laRSkpKTtcbiAgICB9XG4gICAgcmV0dXJuIGJ0b2EoYXJyLmpvaW4oJycpKTtcbn1cbmV4cG9ydCBmdW5jdGlvbiBkZWNvZGVCYXNlNjQoZW5jb2RlZCkge1xuICAgIGlmIChVaW50OEFycmF5LmZyb21CYXNlNjQpIHtcbiAgICAgICAgcmV0dXJuIFVpbnQ4QXJyYXkuZnJvbUJhc2U2NChlbmNvZGVkKTtcbiAgICB9XG4gICAgY29uc3QgYmluYXJ5ID0gYXRvYihlbmNvZGVkKTtcbiAgICBjb25zdCBieXRlcyA9IG5ldyBVaW50OEFycmF5KGJpbmFyeS5sZW5ndGgpO1xuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgYmluYXJ5Lmxlbmd0aDsgaSsrKSB7XG4gICAgICAgIGJ5dGVzW2ldID0gYmluYXJ5LmNoYXJDb2RlQXQoaSk7XG4gICAgfVxuICAgIHJldHVybiBieXRlcztcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/lib/base64.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/webapi/lib/buffer_utils.js":
/*!***********************************************************!*\
  !*** ./node_modules/jose/dist/webapi/lib/buffer_utils.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   concat: () => (/* binding */ concat),\n/* harmony export */   decoder: () => (/* binding */ decoder),\n/* harmony export */   encoder: () => (/* binding */ encoder),\n/* harmony export */   uint32be: () => (/* binding */ uint32be),\n/* harmony export */   uint64be: () => (/* binding */ uint64be)\n/* harmony export */ });\nconst encoder = new TextEncoder();\nconst decoder = new TextDecoder();\nconst MAX_INT32 = 2 ** 32;\nfunction concat(...buffers) {\n    const size = buffers.reduce((acc, { length }) => acc + length, 0);\n    const buf = new Uint8Array(size);\n    let i = 0;\n    for (const buffer of buffers) {\n        buf.set(buffer, i);\n        i += buffer.length;\n    }\n    return buf;\n}\nfunction writeUInt32BE(buf, value, offset) {\n    if (value < 0 || value >= MAX_INT32) {\n        throw new RangeError(`value must be >= 0 and <= ${MAX_INT32 - 1}. Received ${value}`);\n    }\n    buf.set([value >>> 24, value >>> 16, value >>> 8, value & 0xff], offset);\n}\nfunction uint64be(value) {\n    const high = Math.floor(value / MAX_INT32);\n    const low = value % MAX_INT32;\n    const buf = new Uint8Array(8);\n    writeUInt32BE(buf, high, 0);\n    writeUInt32BE(buf, low, 4);\n    return buf;\n}\nfunction uint32be(value) {\n    const buf = new Uint8Array(4);\n    writeUInt32BE(buf, value);\n    return buf;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L3dlYmFwaS9saWIvYnVmZmVyX3V0aWxzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQU87QUFDQTtBQUNQO0FBQ087QUFDUCx3Q0FBd0MsUUFBUTtBQUNoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBEQUEwRCxjQUFjLGFBQWEsTUFBTTtBQUMzRjtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxXaW5kb3dzIDExXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXElubm92YXRpdmUgQ2VudHJlXFxjcm0tc3RhZmYtc2VydmljZVxcbm9kZV9tb2R1bGVzXFxqb3NlXFxkaXN0XFx3ZWJhcGlcXGxpYlxcYnVmZmVyX3V0aWxzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBlbmNvZGVyID0gbmV3IFRleHRFbmNvZGVyKCk7XG5leHBvcnQgY29uc3QgZGVjb2RlciA9IG5ldyBUZXh0RGVjb2RlcigpO1xuY29uc3QgTUFYX0lOVDMyID0gMiAqKiAzMjtcbmV4cG9ydCBmdW5jdGlvbiBjb25jYXQoLi4uYnVmZmVycykge1xuICAgIGNvbnN0IHNpemUgPSBidWZmZXJzLnJlZHVjZSgoYWNjLCB7IGxlbmd0aCB9KSA9PiBhY2MgKyBsZW5ndGgsIDApO1xuICAgIGNvbnN0IGJ1ZiA9IG5ldyBVaW50OEFycmF5KHNpemUpO1xuICAgIGxldCBpID0gMDtcbiAgICBmb3IgKGNvbnN0IGJ1ZmZlciBvZiBidWZmZXJzKSB7XG4gICAgICAgIGJ1Zi5zZXQoYnVmZmVyLCBpKTtcbiAgICAgICAgaSArPSBidWZmZXIubGVuZ3RoO1xuICAgIH1cbiAgICByZXR1cm4gYnVmO1xufVxuZnVuY3Rpb24gd3JpdGVVSW50MzJCRShidWYsIHZhbHVlLCBvZmZzZXQpIHtcbiAgICBpZiAodmFsdWUgPCAwIHx8IHZhbHVlID49IE1BWF9JTlQzMikge1xuICAgICAgICB0aHJvdyBuZXcgUmFuZ2VFcnJvcihgdmFsdWUgbXVzdCBiZSA+PSAwIGFuZCA8PSAke01BWF9JTlQzMiAtIDF9LiBSZWNlaXZlZCAke3ZhbHVlfWApO1xuICAgIH1cbiAgICBidWYuc2V0KFt2YWx1ZSA+Pj4gMjQsIHZhbHVlID4+PiAxNiwgdmFsdWUgPj4+IDgsIHZhbHVlICYgMHhmZl0sIG9mZnNldCk7XG59XG5leHBvcnQgZnVuY3Rpb24gdWludDY0YmUodmFsdWUpIHtcbiAgICBjb25zdCBoaWdoID0gTWF0aC5mbG9vcih2YWx1ZSAvIE1BWF9JTlQzMik7XG4gICAgY29uc3QgbG93ID0gdmFsdWUgJSBNQVhfSU5UMzI7XG4gICAgY29uc3QgYnVmID0gbmV3IFVpbnQ4QXJyYXkoOCk7XG4gICAgd3JpdGVVSW50MzJCRShidWYsIGhpZ2gsIDApO1xuICAgIHdyaXRlVUludDMyQkUoYnVmLCBsb3csIDQpO1xuICAgIHJldHVybiBidWY7XG59XG5leHBvcnQgZnVuY3Rpb24gdWludDMyYmUodmFsdWUpIHtcbiAgICBjb25zdCBidWYgPSBuZXcgVWludDhBcnJheSg0KTtcbiAgICB3cml0ZVVJbnQzMkJFKGJ1ZiwgdmFsdWUpO1xuICAgIHJldHVybiBidWY7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/lib/buffer_utils.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/webapi/lib/cek.js":
/*!**************************************************!*\
  !*** ./node_modules/jose/dist/webapi/lib/cek.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bitLength: () => (/* binding */ bitLength),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/webapi/util/errors.js\");\n\nfunction bitLength(alg) {\n    switch (alg) {\n        case 'A128GCM':\n            return 128;\n        case 'A192GCM':\n            return 192;\n        case 'A256GCM':\n        case 'A128CBC-HS256':\n            return 256;\n        case 'A192CBC-HS384':\n            return 384;\n        case 'A256CBC-HS512':\n            return 512;\n        default:\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JOSENotSupported(`Unsupported JWE Algorithm: ${alg}`);\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((alg) => crypto.getRandomValues(new Uint8Array(bitLength(alg) >> 3)));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L3dlYmFwaS9saWIvY2VrLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFxRDtBQUM5QztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLDZEQUFnQiwrQkFBK0IsSUFBSTtBQUN6RTtBQUNBO0FBQ0EsaUVBQWUsb0VBQW9FLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcV2luZG93cyAxMVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxJbm5vdmF0aXZlIENlbnRyZVxcY3JtLXN0YWZmLXNlcnZpY2VcXG5vZGVfbW9kdWxlc1xcam9zZVxcZGlzdFxcd2ViYXBpXFxsaWJcXGNlay5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBKT1NFTm90U3VwcG9ydGVkIH0gZnJvbSAnLi4vdXRpbC9lcnJvcnMuanMnO1xuZXhwb3J0IGZ1bmN0aW9uIGJpdExlbmd0aChhbGcpIHtcbiAgICBzd2l0Y2ggKGFsZykge1xuICAgICAgICBjYXNlICdBMTI4R0NNJzpcbiAgICAgICAgICAgIHJldHVybiAxMjg7XG4gICAgICAgIGNhc2UgJ0ExOTJHQ00nOlxuICAgICAgICAgICAgcmV0dXJuIDE5MjtcbiAgICAgICAgY2FzZSAnQTI1NkdDTSc6XG4gICAgICAgIGNhc2UgJ0ExMjhDQkMtSFMyNTYnOlxuICAgICAgICAgICAgcmV0dXJuIDI1NjtcbiAgICAgICAgY2FzZSAnQTE5MkNCQy1IUzM4NCc6XG4gICAgICAgICAgICByZXR1cm4gMzg0O1xuICAgICAgICBjYXNlICdBMjU2Q0JDLUhTNTEyJzpcbiAgICAgICAgICAgIHJldHVybiA1MTI7XG4gICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICB0aHJvdyBuZXcgSk9TRU5vdFN1cHBvcnRlZChgVW5zdXBwb3J0ZWQgSldFIEFsZ29yaXRobTogJHthbGd9YCk7XG4gICAgfVxufVxuZXhwb3J0IGRlZmF1bHQgKGFsZykgPT4gY3J5cHRvLmdldFJhbmRvbVZhbHVlcyhuZXcgVWludDhBcnJheShiaXRMZW5ndGgoYWxnKSA+PiAzKSk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/lib/cek.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/webapi/lib/check_cek_length.js":
/*!***************************************************************!*\
  !*** ./node_modules/jose/dist/webapi/lib/check_cek_length.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/webapi/util/errors.js\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((cek, expected) => {\n    const actual = cek.byteLength << 3;\n    if (actual !== expected) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid(`Invalid Content Encryption Key length. Expected ${expected} bits, got ${actual} bits`);\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L3dlYmFwaS9saWIvY2hlY2tfY2VrX2xlbmd0aC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUErQztBQUMvQyxpRUFBZTtBQUNmO0FBQ0E7QUFDQSxrQkFBa0IsdURBQVUsb0RBQW9ELFVBQVUsWUFBWSxRQUFRO0FBQzlHO0FBQ0EsQ0FBQyxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFdpbmRvd3MgMTFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcSW5ub3ZhdGl2ZSBDZW50cmVcXGNybS1zdGFmZi1zZXJ2aWNlXFxub2RlX21vZHVsZXNcXGpvc2VcXGRpc3RcXHdlYmFwaVxcbGliXFxjaGVja19jZWtfbGVuZ3RoLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEpXRUludmFsaWQgfSBmcm9tICcuLi91dGlsL2Vycm9ycy5qcyc7XG5leHBvcnQgZGVmYXVsdCAoY2VrLCBleHBlY3RlZCkgPT4ge1xuICAgIGNvbnN0IGFjdHVhbCA9IGNlay5ieXRlTGVuZ3RoIDw8IDM7XG4gICAgaWYgKGFjdHVhbCAhPT0gZXhwZWN0ZWQpIHtcbiAgICAgICAgdGhyb3cgbmV3IEpXRUludmFsaWQoYEludmFsaWQgQ29udGVudCBFbmNyeXB0aW9uIEtleSBsZW5ndGguIEV4cGVjdGVkICR7ZXhwZWN0ZWR9IGJpdHMsIGdvdCAke2FjdHVhbH0gYml0c2ApO1xuICAgIH1cbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/lib/check_cek_length.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/webapi/lib/check_iv_length.js":
/*!**************************************************************!*\
  !*** ./node_modules/jose/dist/webapi/lib/check_iv_length.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/webapi/util/errors.js\");\n/* harmony import */ var _iv_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./iv.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/iv.js\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((enc, iv) => {\n    if (iv.length << 3 !== (0,_iv_js__WEBPACK_IMPORTED_MODULE_0__.bitLength)(enc)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('Invalid Initialization Vector length');\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L3dlYmFwaS9saWIvY2hlY2tfaXZfbGVuZ3RoLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUErQztBQUNYO0FBQ3BDLGlFQUFlO0FBQ2YsMkJBQTJCLGlEQUFTO0FBQ3BDLGtCQUFrQix1REFBVTtBQUM1QjtBQUNBLENBQUMsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxXaW5kb3dzIDExXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXElubm92YXRpdmUgQ2VudHJlXFxjcm0tc3RhZmYtc2VydmljZVxcbm9kZV9tb2R1bGVzXFxqb3NlXFxkaXN0XFx3ZWJhcGlcXGxpYlxcY2hlY2tfaXZfbGVuZ3RoLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEpXRUludmFsaWQgfSBmcm9tICcuLi91dGlsL2Vycm9ycy5qcyc7XG5pbXBvcnQgeyBiaXRMZW5ndGggfSBmcm9tICcuL2l2LmpzJztcbmV4cG9ydCBkZWZhdWx0IChlbmMsIGl2KSA9PiB7XG4gICAgaWYgKGl2Lmxlbmd0aCA8PCAzICE9PSBiaXRMZW5ndGgoZW5jKSkge1xuICAgICAgICB0aHJvdyBuZXcgSldFSW52YWxpZCgnSW52YWxpZCBJbml0aWFsaXphdGlvbiBWZWN0b3IgbGVuZ3RoJyk7XG4gICAgfVxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/lib/check_iv_length.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/webapi/lib/check_key_length.js":
/*!***************************************************************!*\
  !*** ./node_modules/jose/dist/webapi/lib/check_key_length.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((alg, key) => {\n    if (alg.startsWith('RS') || alg.startsWith('PS')) {\n        const { modulusLength } = key.algorithm;\n        if (typeof modulusLength !== 'number' || modulusLength < 2048) {\n            throw new TypeError(`${alg} requires key modulusLength to be 2048 bits or larger`);\n        }\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L3dlYmFwaS9saWIvY2hlY2tfa2V5X2xlbmd0aC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWU7QUFDZjtBQUNBLGdCQUFnQixnQkFBZ0I7QUFDaEM7QUFDQSxtQ0FBbUMsS0FBSztBQUN4QztBQUNBO0FBQ0EsQ0FBQyxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFdpbmRvd3MgMTFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcSW5ub3ZhdGl2ZSBDZW50cmVcXGNybS1zdGFmZi1zZXJ2aWNlXFxub2RlX21vZHVsZXNcXGpvc2VcXGRpc3RcXHdlYmFwaVxcbGliXFxjaGVja19rZXlfbGVuZ3RoLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IChhbGcsIGtleSkgPT4ge1xuICAgIGlmIChhbGcuc3RhcnRzV2l0aCgnUlMnKSB8fCBhbGcuc3RhcnRzV2l0aCgnUFMnKSkge1xuICAgICAgICBjb25zdCB7IG1vZHVsdXNMZW5ndGggfSA9IGtleS5hbGdvcml0aG07XG4gICAgICAgIGlmICh0eXBlb2YgbW9kdWx1c0xlbmd0aCAhPT0gJ251bWJlcicgfHwgbW9kdWx1c0xlbmd0aCA8IDIwNDgpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoYCR7YWxnfSByZXF1aXJlcyBrZXkgbW9kdWx1c0xlbmd0aCB0byBiZSAyMDQ4IGJpdHMgb3IgbGFyZ2VyYCk7XG4gICAgICAgIH1cbiAgICB9XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/lib/check_key_length.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/webapi/lib/check_key_type.js":
/*!*************************************************************!*\
  !*** ./node_modules/jose/dist/webapi/lib/check_key_type.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _invalid_key_input_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./invalid_key_input.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/invalid_key_input.js\");\n/* harmony import */ var _is_key_like_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./is_key_like.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/is_key_like.js\");\n/* harmony import */ var _is_jwk_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is_jwk.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/is_jwk.js\");\n\n\n\nconst tag = (key) => key?.[Symbol.toStringTag];\nconst jwkMatchesOp = (alg, key, usage) => {\n    if (key.use !== undefined) {\n        let expected;\n        switch (usage) {\n            case 'sign':\n            case 'verify':\n                expected = 'sig';\n                break;\n            case 'encrypt':\n            case 'decrypt':\n                expected = 'enc';\n                break;\n        }\n        if (key.use !== expected) {\n            throw new TypeError(`Invalid key for this operation, its \"use\" must be \"${expected}\" when present`);\n        }\n    }\n    if (key.alg !== undefined && key.alg !== alg) {\n        throw new TypeError(`Invalid key for this operation, its \"alg\" must be \"${alg}\" when present`);\n    }\n    if (Array.isArray(key.key_ops)) {\n        let expectedKeyOp;\n        switch (true) {\n            case usage === 'sign' || usage === 'verify':\n            case alg === 'dir':\n            case alg.includes('CBC-HS'):\n                expectedKeyOp = usage;\n                break;\n            case alg.startsWith('PBES2'):\n                expectedKeyOp = 'deriveBits';\n                break;\n            case /^A\\d{3}(?:GCM)?(?:KW)?$/.test(alg):\n                if (!alg.includes('GCM') && alg.endsWith('KW')) {\n                    expectedKeyOp = usage === 'encrypt' ? 'wrapKey' : 'unwrapKey';\n                }\n                else {\n                    expectedKeyOp = usage;\n                }\n                break;\n            case usage === 'encrypt' && alg.startsWith('RSA'):\n                expectedKeyOp = 'wrapKey';\n                break;\n            case usage === 'decrypt':\n                expectedKeyOp = alg.startsWith('RSA') ? 'unwrapKey' : 'deriveBits';\n                break;\n        }\n        if (expectedKeyOp && key.key_ops?.includes?.(expectedKeyOp) === false) {\n            throw new TypeError(`Invalid key for this operation, its \"key_ops\" must include \"${expectedKeyOp}\" when present`);\n        }\n    }\n    return true;\n};\nconst symmetricTypeCheck = (alg, key, usage) => {\n    if (key instanceof Uint8Array)\n        return;\n    if (_is_jwk_js__WEBPACK_IMPORTED_MODULE_0__.isJWK(key)) {\n        if (_is_jwk_js__WEBPACK_IMPORTED_MODULE_0__.isSecretJWK(key) && jwkMatchesOp(alg, key, usage))\n            return;\n        throw new TypeError(`JSON Web Key for symmetric algorithms must have JWK \"kty\" (Key Type) equal to \"oct\" and the JWK \"k\" (Key Value) present`);\n    }\n    if (!(0,_is_key_like_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(key)) {\n        throw new TypeError((0,_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_2__.withAlg)(alg, key, 'CryptoKey', 'KeyObject', 'JSON Web Key', 'Uint8Array'));\n    }\n    if (key.type !== 'secret') {\n        throw new TypeError(`${tag(key)} instances for symmetric algorithms must be of type \"secret\"`);\n    }\n};\nconst asymmetricTypeCheck = (alg, key, usage) => {\n    if (_is_jwk_js__WEBPACK_IMPORTED_MODULE_0__.isJWK(key)) {\n        switch (usage) {\n            case 'decrypt':\n            case 'sign':\n                if (_is_jwk_js__WEBPACK_IMPORTED_MODULE_0__.isPrivateJWK(key) && jwkMatchesOp(alg, key, usage))\n                    return;\n                throw new TypeError(`JSON Web Key for this operation be a private JWK`);\n            case 'encrypt':\n            case 'verify':\n                if (_is_jwk_js__WEBPACK_IMPORTED_MODULE_0__.isPublicJWK(key) && jwkMatchesOp(alg, key, usage))\n                    return;\n                throw new TypeError(`JSON Web Key for this operation be a public JWK`);\n        }\n    }\n    if (!(0,_is_key_like_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(key)) {\n        throw new TypeError((0,_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_2__.withAlg)(alg, key, 'CryptoKey', 'KeyObject', 'JSON Web Key'));\n    }\n    if (key.type === 'secret') {\n        throw new TypeError(`${tag(key)} instances for asymmetric algorithms must not be of type \"secret\"`);\n    }\n    if (key.type === 'public') {\n        switch (usage) {\n            case 'sign':\n                throw new TypeError(`${tag(key)} instances for asymmetric algorithm signing must be of type \"private\"`);\n            case 'decrypt':\n                throw new TypeError(`${tag(key)} instances for asymmetric algorithm decryption must be of type \"private\"`);\n            default:\n                break;\n        }\n    }\n    if (key.type === 'private') {\n        switch (usage) {\n            case 'verify':\n                throw new TypeError(`${tag(key)} instances for asymmetric algorithm verifying must be of type \"public\"`);\n            case 'encrypt':\n                throw new TypeError(`${tag(key)} instances for asymmetric algorithm encryption must be of type \"public\"`);\n            default:\n                break;\n        }\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((alg, key, usage) => {\n    const symmetric = alg.startsWith('HS') ||\n        alg === 'dir' ||\n        alg.startsWith('PBES2') ||\n        /^A(?:128|192|256)(?:GCM)?(?:KW)?$/.test(alg) ||\n        /^A(?:128|192|256)CBC-HS(?:256|384|512)$/.test(alg);\n    if (symmetric) {\n        symmetricTypeCheck(alg, key, usage);\n    }\n    else {\n        asymmetricTypeCheck(alg, key, usage);\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/lib/check_key_type.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/webapi/lib/crypto_key.js":
/*!*********************************************************!*\
  !*** ./node_modules/jose/dist/webapi/lib/crypto_key.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkEncCryptoKey: () => (/* binding */ checkEncCryptoKey),\n/* harmony export */   checkSigCryptoKey: () => (/* binding */ checkSigCryptoKey)\n/* harmony export */ });\nfunction unusable(name, prop = 'algorithm.name') {\n    return new TypeError(`CryptoKey does not support this operation, its ${prop} must be ${name}`);\n}\nfunction isAlgorithm(algorithm, name) {\n    return algorithm.name === name;\n}\nfunction getHashLength(hash) {\n    return parseInt(hash.name.slice(4), 10);\n}\nfunction getNamedCurve(alg) {\n    switch (alg) {\n        case 'ES256':\n            return 'P-256';\n        case 'ES384':\n            return 'P-384';\n        case 'ES512':\n            return 'P-521';\n        default:\n            throw new Error('unreachable');\n    }\n}\nfunction checkUsage(key, usage) {\n    if (usage && !key.usages.includes(usage)) {\n        throw new TypeError(`CryptoKey does not support this operation, its usages must include ${usage}.`);\n    }\n}\nfunction checkSigCryptoKey(key, alg, usage) {\n    switch (alg) {\n        case 'HS256':\n        case 'HS384':\n        case 'HS512': {\n            if (!isAlgorithm(key.algorithm, 'HMAC'))\n                throw unusable('HMAC');\n            const expected = parseInt(alg.slice(2), 10);\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        case 'RS256':\n        case 'RS384':\n        case 'RS512': {\n            if (!isAlgorithm(key.algorithm, 'RSASSA-PKCS1-v1_5'))\n                throw unusable('RSASSA-PKCS1-v1_5');\n            const expected = parseInt(alg.slice(2), 10);\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        case 'PS256':\n        case 'PS384':\n        case 'PS512': {\n            if (!isAlgorithm(key.algorithm, 'RSA-PSS'))\n                throw unusable('RSA-PSS');\n            const expected = parseInt(alg.slice(2), 10);\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        case 'Ed25519':\n        case 'EdDSA': {\n            if (!isAlgorithm(key.algorithm, 'Ed25519'))\n                throw unusable('Ed25519');\n            break;\n        }\n        case 'ES256':\n        case 'ES384':\n        case 'ES512': {\n            if (!isAlgorithm(key.algorithm, 'ECDSA'))\n                throw unusable('ECDSA');\n            const expected = getNamedCurve(alg);\n            const actual = key.algorithm.namedCurve;\n            if (actual !== expected)\n                throw unusable(expected, 'algorithm.namedCurve');\n            break;\n        }\n        default:\n            throw new TypeError('CryptoKey does not support this operation');\n    }\n    checkUsage(key, usage);\n}\nfunction checkEncCryptoKey(key, alg, usage) {\n    switch (alg) {\n        case 'A128GCM':\n        case 'A192GCM':\n        case 'A256GCM': {\n            if (!isAlgorithm(key.algorithm, 'AES-GCM'))\n                throw unusable('AES-GCM');\n            const expected = parseInt(alg.slice(1, 4), 10);\n            const actual = key.algorithm.length;\n            if (actual !== expected)\n                throw unusable(expected, 'algorithm.length');\n            break;\n        }\n        case 'A128KW':\n        case 'A192KW':\n        case 'A256KW': {\n            if (!isAlgorithm(key.algorithm, 'AES-KW'))\n                throw unusable('AES-KW');\n            const expected = parseInt(alg.slice(1, 4), 10);\n            const actual = key.algorithm.length;\n            if (actual !== expected)\n                throw unusable(expected, 'algorithm.length');\n            break;\n        }\n        case 'ECDH': {\n            switch (key.algorithm.name) {\n                case 'ECDH':\n                case 'X25519':\n                    break;\n                default:\n                    throw unusable('ECDH or X25519');\n            }\n            break;\n        }\n        case 'PBES2-HS256+A128KW':\n        case 'PBES2-HS384+A192KW':\n        case 'PBES2-HS512+A256KW':\n            if (!isAlgorithm(key.algorithm, 'PBKDF2'))\n                throw unusable('PBKDF2');\n            break;\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512': {\n            if (!isAlgorithm(key.algorithm, 'RSA-OAEP'))\n                throw unusable('RSA-OAEP');\n            const expected = parseInt(alg.slice(9), 10) || 1;\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        default:\n            throw new TypeError('CryptoKey does not support this operation');\n    }\n    checkUsage(key, usage);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/lib/crypto_key.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/webapi/lib/decrypt.js":
/*!******************************************************!*\
  !*** ./node_modules/jose/dist/webapi/lib/decrypt.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _buffer_utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./buffer_utils.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/buffer_utils.js\");\n/* harmony import */ var _check_iv_length_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./check_iv_length.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/check_iv_length.js\");\n/* harmony import */ var _check_cek_length_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./check_cek_length.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/check_cek_length.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/webapi/util/errors.js\");\n/* harmony import */ var _crypto_key_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./crypto_key.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/crypto_key.js\");\n/* harmony import */ var _invalid_key_input_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./invalid_key_input.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/invalid_key_input.js\");\n/* harmony import */ var _is_key_like_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./is_key_like.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/is_key_like.js\");\n\n\n\n\n\n\n\nasync function timingSafeEqual(a, b) {\n    if (!(a instanceof Uint8Array)) {\n        throw new TypeError('First argument must be a buffer');\n    }\n    if (!(b instanceof Uint8Array)) {\n        throw new TypeError('Second argument must be a buffer');\n    }\n    const algorithm = { name: 'HMAC', hash: 'SHA-256' };\n    const key = (await crypto.subtle.generateKey(algorithm, false, ['sign']));\n    const aHmac = new Uint8Array(await crypto.subtle.sign(algorithm, key, a));\n    const bHmac = new Uint8Array(await crypto.subtle.sign(algorithm, key, b));\n    let out = 0;\n    let i = -1;\n    while (++i < 32) {\n        out |= aHmac[i] ^ bHmac[i];\n    }\n    return out === 0;\n}\nasync function cbcDecrypt(enc, cek, ciphertext, iv, tag, aad) {\n    if (!(cek instanceof Uint8Array)) {\n        throw new TypeError((0,_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(cek, 'Uint8Array'));\n    }\n    const keySize = parseInt(enc.slice(1, 4), 10);\n    const encKey = await crypto.subtle.importKey('raw', cek.subarray(keySize >> 3), 'AES-CBC', false, ['decrypt']);\n    const macKey = await crypto.subtle.importKey('raw', cek.subarray(0, keySize >> 3), {\n        hash: `SHA-${keySize << 1}`,\n        name: 'HMAC',\n    }, false, ['sign']);\n    const macData = (0,_buffer_utils_js__WEBPACK_IMPORTED_MODULE_1__.concat)(aad, iv, ciphertext, (0,_buffer_utils_js__WEBPACK_IMPORTED_MODULE_1__.uint64be)(aad.length << 3));\n    const expectedTag = new Uint8Array((await crypto.subtle.sign('HMAC', macKey, macData)).slice(0, keySize >> 3));\n    let macCheckPassed;\n    try {\n        macCheckPassed = await timingSafeEqual(tag, expectedTag);\n    }\n    catch {\n    }\n    if (!macCheckPassed) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEDecryptionFailed();\n    }\n    let plaintext;\n    try {\n        plaintext = new Uint8Array(await crypto.subtle.decrypt({ iv, name: 'AES-CBC' }, encKey, ciphertext));\n    }\n    catch {\n    }\n    if (!plaintext) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEDecryptionFailed();\n    }\n    return plaintext;\n}\nasync function gcmDecrypt(enc, cek, ciphertext, iv, tag, aad) {\n    let encKey;\n    if (cek instanceof Uint8Array) {\n        encKey = await crypto.subtle.importKey('raw', cek, 'AES-GCM', false, ['decrypt']);\n    }\n    else {\n        (0,_crypto_key_js__WEBPACK_IMPORTED_MODULE_3__.checkEncCryptoKey)(cek, enc, 'decrypt');\n        encKey = cek;\n    }\n    try {\n        return new Uint8Array(await crypto.subtle.decrypt({\n            additionalData: aad,\n            iv,\n            name: 'AES-GCM',\n            tagLength: 128,\n        }, encKey, (0,_buffer_utils_js__WEBPACK_IMPORTED_MODULE_1__.concat)(ciphertext, tag)));\n    }\n    catch {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEDecryptionFailed();\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (enc, cek, ciphertext, iv, tag, aad) => {\n    if (!(0,_is_key_like_js__WEBPACK_IMPORTED_MODULE_4__.isCryptoKey)(cek) && !(cek instanceof Uint8Array)) {\n        throw new TypeError((0,_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(cek, 'CryptoKey', 'KeyObject', 'Uint8Array', 'JSON Web Key'));\n    }\n    if (!iv) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid('JWE Initialization Vector missing');\n    }\n    if (!tag) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid('JWE Authentication Tag missing');\n    }\n    (0,_check_iv_length_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(enc, iv);\n    switch (enc) {\n        case 'A128CBC-HS256':\n        case 'A192CBC-HS384':\n        case 'A256CBC-HS512':\n            if (cek instanceof Uint8Array)\n                (0,_check_cek_length_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(cek, parseInt(enc.slice(-3), 10));\n            return cbcDecrypt(enc, cek, ciphertext, iv, tag, aad);\n        case 'A128GCM':\n        case 'A192GCM':\n        case 'A256GCM':\n            if (cek instanceof Uint8Array)\n                (0,_check_cek_length_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(cek, parseInt(enc.slice(1, 4), 10));\n            return gcmDecrypt(enc, cek, ciphertext, iv, tag, aad);\n        default:\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JOSENotSupported('Unsupported JWE Content Encryption Algorithm');\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/lib/decrypt.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/webapi/lib/decrypt_key_management.js":
/*!*********************************************************************!*\
  !*** ./node_modules/jose/dist/webapi/lib/decrypt_key_management.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _aeskw_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./aeskw.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/aeskw.js\");\n/* harmony import */ var _ecdhes_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ecdhes.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/ecdhes.js\");\n/* harmony import */ var _pbes2kw_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./pbes2kw.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/pbes2kw.js\");\n/* harmony import */ var _rsaes_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./rsaes.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/rsaes.js\");\n/* harmony import */ var _util_base64url_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../util/base64url.js */ \"(rsc)/./node_modules/jose/dist/webapi/util/base64url.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/webapi/util/errors.js\");\n/* harmony import */ var _lib_cek_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../lib/cek.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/cek.js\");\n/* harmony import */ var _key_import_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../key/import.js */ \"(rsc)/./node_modules/jose/dist/webapi/key/import.js\");\n/* harmony import */ var _is_object_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./is_object.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/is_object.js\");\n/* harmony import */ var _aesgcmkw_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./aesgcmkw.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/aesgcmkw.js\");\n/* harmony import */ var _is_key_like_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./is_key_like.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/is_key_like.js\");\n\n\n\n\n\n\n\n\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (alg, key, encryptedKey, joseHeader, options) => {\n    switch (alg) {\n        case 'dir': {\n            if (encryptedKey !== undefined)\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid('Encountered unexpected JWE Encrypted Key');\n            return key;\n        }\n        case 'ECDH-ES':\n            if (encryptedKey !== undefined)\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid('Encountered unexpected JWE Encrypted Key');\n        case 'ECDH-ES+A128KW':\n        case 'ECDH-ES+A192KW':\n        case 'ECDH-ES+A256KW': {\n            if (!(0,_is_object_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(joseHeader.epk))\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid(`JOSE Header \"epk\" (Ephemeral Public Key) missing or invalid`);\n            (0,_is_key_like_js__WEBPACK_IMPORTED_MODULE_2__.assertCryptoKey)(key);\n            if (!_ecdhes_js__WEBPACK_IMPORTED_MODULE_3__.allowed(key))\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JOSENotSupported('ECDH with the provided key is not allowed or not supported by your javascript runtime');\n            const epk = await (0,_key_import_js__WEBPACK_IMPORTED_MODULE_4__.importJWK)(joseHeader.epk, alg);\n            (0,_is_key_like_js__WEBPACK_IMPORTED_MODULE_2__.assertCryptoKey)(epk);\n            let partyUInfo;\n            let partyVInfo;\n            if (joseHeader.apu !== undefined) {\n                if (typeof joseHeader.apu !== 'string')\n                    throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid(`JOSE Header \"apu\" (Agreement PartyUInfo) invalid`);\n                try {\n                    partyUInfo = (0,_util_base64url_js__WEBPACK_IMPORTED_MODULE_5__.decode)(joseHeader.apu);\n                }\n                catch {\n                    throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid('Failed to base64url decode the apu');\n                }\n            }\n            if (joseHeader.apv !== undefined) {\n                if (typeof joseHeader.apv !== 'string')\n                    throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid(`JOSE Header \"apv\" (Agreement PartyVInfo) invalid`);\n                try {\n                    partyVInfo = (0,_util_base64url_js__WEBPACK_IMPORTED_MODULE_5__.decode)(joseHeader.apv);\n                }\n                catch {\n                    throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid('Failed to base64url decode the apv');\n                }\n            }\n            const sharedSecret = await _ecdhes_js__WEBPACK_IMPORTED_MODULE_3__.deriveKey(epk, key, alg === 'ECDH-ES' ? joseHeader.enc : alg, alg === 'ECDH-ES' ? (0,_lib_cek_js__WEBPACK_IMPORTED_MODULE_6__.bitLength)(joseHeader.enc) : parseInt(alg.slice(-5, -2), 10), partyUInfo, partyVInfo);\n            if (alg === 'ECDH-ES')\n                return sharedSecret;\n            if (encryptedKey === undefined)\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid('JWE Encrypted Key missing');\n            return _aeskw_js__WEBPACK_IMPORTED_MODULE_7__.unwrap(alg.slice(-6), sharedSecret, encryptedKey);\n        }\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512': {\n            if (encryptedKey === undefined)\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid('JWE Encrypted Key missing');\n            (0,_is_key_like_js__WEBPACK_IMPORTED_MODULE_2__.assertCryptoKey)(key);\n            return _rsaes_js__WEBPACK_IMPORTED_MODULE_8__.decrypt(alg, key, encryptedKey);\n        }\n        case 'PBES2-HS256+A128KW':\n        case 'PBES2-HS384+A192KW':\n        case 'PBES2-HS512+A256KW': {\n            if (encryptedKey === undefined)\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid('JWE Encrypted Key missing');\n            if (typeof joseHeader.p2c !== 'number')\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid(`JOSE Header \"p2c\" (PBES2 Count) missing or invalid`);\n            const p2cLimit = options?.maxPBES2Count || 10_000;\n            if (joseHeader.p2c > p2cLimit)\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid(`JOSE Header \"p2c\" (PBES2 Count) out is of acceptable bounds`);\n            if (typeof joseHeader.p2s !== 'string')\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid(`JOSE Header \"p2s\" (PBES2 Salt) missing or invalid`);\n            let p2s;\n            try {\n                p2s = (0,_util_base64url_js__WEBPACK_IMPORTED_MODULE_5__.decode)(joseHeader.p2s);\n            }\n            catch {\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid('Failed to base64url decode the p2s');\n            }\n            return _pbes2kw_js__WEBPACK_IMPORTED_MODULE_9__.unwrap(alg, key, encryptedKey, joseHeader.p2c, p2s);\n        }\n        case 'A128KW':\n        case 'A192KW':\n        case 'A256KW': {\n            if (encryptedKey === undefined)\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid('JWE Encrypted Key missing');\n            return _aeskw_js__WEBPACK_IMPORTED_MODULE_7__.unwrap(alg, key, encryptedKey);\n        }\n        case 'A128GCMKW':\n        case 'A192GCMKW':\n        case 'A256GCMKW': {\n            if (encryptedKey === undefined)\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid('JWE Encrypted Key missing');\n            if (typeof joseHeader.iv !== 'string')\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid(`JOSE Header \"iv\" (Initialization Vector) missing or invalid`);\n            if (typeof joseHeader.tag !== 'string')\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid(`JOSE Header \"tag\" (Authentication Tag) missing or invalid`);\n            let iv;\n            try {\n                iv = (0,_util_base64url_js__WEBPACK_IMPORTED_MODULE_5__.decode)(joseHeader.iv);\n            }\n            catch {\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid('Failed to base64url decode the iv');\n            }\n            let tag;\n            try {\n                tag = (0,_util_base64url_js__WEBPACK_IMPORTED_MODULE_5__.decode)(joseHeader.tag);\n            }\n            catch {\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid('Failed to base64url decode the tag');\n            }\n            return (0,_aesgcmkw_js__WEBPACK_IMPORTED_MODULE_10__.unwrap)(alg, key, encryptedKey, iv, tag);\n        }\n        default: {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JOSENotSupported('Invalid or unsupported \"alg\" (JWE Algorithm) header value');\n        }\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L3dlYmFwaS9saWIvZGVjcnlwdF9rZXlfbWFuYWdlbWVudC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFBb0M7QUFDRTtBQUNFO0FBQ0o7QUFDa0I7QUFDVztBQUNWO0FBQ1Y7QUFDUDtBQUNhO0FBQ0E7QUFDbkQsaUVBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsdURBQVU7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsdURBQVU7QUFDcEM7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCLHlEQUFRO0FBQ3pCLDBCQUEwQix1REFBVTtBQUNwQyxZQUFZLGdFQUFlO0FBQzNCLGlCQUFpQiwrQ0FBYztBQUMvQiwwQkFBMEIsNkRBQWdCO0FBQzFDLDhCQUE4Qix5REFBUztBQUN2QyxZQUFZLGdFQUFlO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOEJBQThCLHVEQUFVO0FBQ3hDO0FBQ0EsaUNBQWlDLDBEQUFJO0FBQ3JDO0FBQ0E7QUFDQSw4QkFBOEIsdURBQVU7QUFDeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4QkFBOEIsdURBQVU7QUFDeEM7QUFDQSxpQ0FBaUMsMERBQUk7QUFDckM7QUFDQTtBQUNBLDhCQUE4Qix1REFBVTtBQUN4QztBQUNBO0FBQ0EsdUNBQXVDLGlEQUFnQix5RUFBeUUsc0RBQVM7QUFDekk7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLHVEQUFVO0FBQ3BDLG1CQUFtQiw2Q0FBWTtBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsdURBQVU7QUFDcEMsWUFBWSxnRUFBZTtBQUMzQixtQkFBbUIsOENBQWE7QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQix1REFBVTtBQUNwQztBQUNBLDBCQUEwQix1REFBVTtBQUNwQztBQUNBO0FBQ0EsMEJBQTBCLHVEQUFVO0FBQ3BDO0FBQ0EsMEJBQTBCLHVEQUFVO0FBQ3BDO0FBQ0E7QUFDQSxzQkFBc0IsMERBQUk7QUFDMUI7QUFDQTtBQUNBLDBCQUEwQix1REFBVTtBQUNwQztBQUNBLG1CQUFtQiwrQ0FBYztBQUNqQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLHVEQUFVO0FBQ3BDLG1CQUFtQiw2Q0FBWTtBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLHVEQUFVO0FBQ3BDO0FBQ0EsMEJBQTBCLHVEQUFVO0FBQ3BDO0FBQ0EsMEJBQTBCLHVEQUFVO0FBQ3BDO0FBQ0E7QUFDQSxxQkFBcUIsMERBQUk7QUFDekI7QUFDQTtBQUNBLDBCQUEwQix1REFBVTtBQUNwQztBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsMERBQUk7QUFDMUI7QUFDQTtBQUNBLDBCQUEwQix1REFBVTtBQUNwQztBQUNBLG1CQUFtQixxREFBUTtBQUMzQjtBQUNBO0FBQ0Esc0JBQXNCLDZEQUFnQjtBQUN0QztBQUNBO0FBQ0EsQ0FBQyxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFdpbmRvd3MgMTFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcSW5ub3ZhdGl2ZSBDZW50cmVcXGNybS1zdGFmZi1zZXJ2aWNlXFxub2RlX21vZHVsZXNcXGpvc2VcXGRpc3RcXHdlYmFwaVxcbGliXFxkZWNyeXB0X2tleV9tYW5hZ2VtZW50LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIGFlc2t3IGZyb20gJy4vYWVza3cuanMnO1xuaW1wb3J0ICogYXMgZWNkaGVzIGZyb20gJy4vZWNkaGVzLmpzJztcbmltcG9ydCAqIGFzIHBiZXMya3cgZnJvbSAnLi9wYmVzMmt3LmpzJztcbmltcG9ydCAqIGFzIHJzYWVzIGZyb20gJy4vcnNhZXMuanMnO1xuaW1wb3J0IHsgZGVjb2RlIGFzIGI2NHUgfSBmcm9tICcuLi91dGlsL2Jhc2U2NHVybC5qcyc7XG5pbXBvcnQgeyBKT1NFTm90U3VwcG9ydGVkLCBKV0VJbnZhbGlkIH0gZnJvbSAnLi4vdXRpbC9lcnJvcnMuanMnO1xuaW1wb3J0IHsgYml0TGVuZ3RoIGFzIGNla0xlbmd0aCB9IGZyb20gJy4uL2xpYi9jZWsuanMnO1xuaW1wb3J0IHsgaW1wb3J0SldLIH0gZnJvbSAnLi4va2V5L2ltcG9ydC5qcyc7XG5pbXBvcnQgaXNPYmplY3QgZnJvbSAnLi9pc19vYmplY3QuanMnO1xuaW1wb3J0IHsgdW53cmFwIGFzIGFlc0djbUt3IH0gZnJvbSAnLi9hZXNnY21rdy5qcyc7XG5pbXBvcnQgeyBhc3NlcnRDcnlwdG9LZXkgfSBmcm9tICcuL2lzX2tleV9saWtlLmpzJztcbmV4cG9ydCBkZWZhdWx0IGFzeW5jIChhbGcsIGtleSwgZW5jcnlwdGVkS2V5LCBqb3NlSGVhZGVyLCBvcHRpb25zKSA9PiB7XG4gICAgc3dpdGNoIChhbGcpIHtcbiAgICAgICAgY2FzZSAnZGlyJzoge1xuICAgICAgICAgICAgaWYgKGVuY3J5cHRlZEtleSAhPT0gdW5kZWZpbmVkKVxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBKV0VJbnZhbGlkKCdFbmNvdW50ZXJlZCB1bmV4cGVjdGVkIEpXRSBFbmNyeXB0ZWQgS2V5Jyk7XG4gICAgICAgICAgICByZXR1cm4ga2V5O1xuICAgICAgICB9XG4gICAgICAgIGNhc2UgJ0VDREgtRVMnOlxuICAgICAgICAgICAgaWYgKGVuY3J5cHRlZEtleSAhPT0gdW5kZWZpbmVkKVxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBKV0VJbnZhbGlkKCdFbmNvdW50ZXJlZCB1bmV4cGVjdGVkIEpXRSBFbmNyeXB0ZWQgS2V5Jyk7XG4gICAgICAgIGNhc2UgJ0VDREgtRVMrQTEyOEtXJzpcbiAgICAgICAgY2FzZSAnRUNESC1FUytBMTkyS1cnOlxuICAgICAgICBjYXNlICdFQ0RILUVTK0EyNTZLVyc6IHtcbiAgICAgICAgICAgIGlmICghaXNPYmplY3Qoam9zZUhlYWRlci5lcGspKVxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBKV0VJbnZhbGlkKGBKT1NFIEhlYWRlciBcImVwa1wiIChFcGhlbWVyYWwgUHVibGljIEtleSkgbWlzc2luZyBvciBpbnZhbGlkYCk7XG4gICAgICAgICAgICBhc3NlcnRDcnlwdG9LZXkoa2V5KTtcbiAgICAgICAgICAgIGlmICghZWNkaGVzLmFsbG93ZWQoa2V5KSlcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgSk9TRU5vdFN1cHBvcnRlZCgnRUNESCB3aXRoIHRoZSBwcm92aWRlZCBrZXkgaXMgbm90IGFsbG93ZWQgb3Igbm90IHN1cHBvcnRlZCBieSB5b3VyIGphdmFzY3JpcHQgcnVudGltZScpO1xuICAgICAgICAgICAgY29uc3QgZXBrID0gYXdhaXQgaW1wb3J0SldLKGpvc2VIZWFkZXIuZXBrLCBhbGcpO1xuICAgICAgICAgICAgYXNzZXJ0Q3J5cHRvS2V5KGVwayk7XG4gICAgICAgICAgICBsZXQgcGFydHlVSW5mbztcbiAgICAgICAgICAgIGxldCBwYXJ0eVZJbmZvO1xuICAgICAgICAgICAgaWYgKGpvc2VIZWFkZXIuYXB1ICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgICAgICBpZiAodHlwZW9mIGpvc2VIZWFkZXIuYXB1ICE9PSAnc3RyaW5nJylcbiAgICAgICAgICAgICAgICAgICAgdGhyb3cgbmV3IEpXRUludmFsaWQoYEpPU0UgSGVhZGVyIFwiYXB1XCIgKEFncmVlbWVudCBQYXJ0eVVJbmZvKSBpbnZhbGlkYCk7XG4gICAgICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICAgICAgcGFydHlVSW5mbyA9IGI2NHUoam9zZUhlYWRlci5hcHUpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjYXRjaCB7XG4gICAgICAgICAgICAgICAgICAgIHRocm93IG5ldyBKV0VJbnZhbGlkKCdGYWlsZWQgdG8gYmFzZTY0dXJsIGRlY29kZSB0aGUgYXB1Jyk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKGpvc2VIZWFkZXIuYXB2ICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgICAgICBpZiAodHlwZW9mIGpvc2VIZWFkZXIuYXB2ICE9PSAnc3RyaW5nJylcbiAgICAgICAgICAgICAgICAgICAgdGhyb3cgbmV3IEpXRUludmFsaWQoYEpPU0UgSGVhZGVyIFwiYXB2XCIgKEFncmVlbWVudCBQYXJ0eVZJbmZvKSBpbnZhbGlkYCk7XG4gICAgICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICAgICAgcGFydHlWSW5mbyA9IGI2NHUoam9zZUhlYWRlci5hcHYpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjYXRjaCB7XG4gICAgICAgICAgICAgICAgICAgIHRocm93IG5ldyBKV0VJbnZhbGlkKCdGYWlsZWQgdG8gYmFzZTY0dXJsIGRlY29kZSB0aGUgYXB2Jyk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3Qgc2hhcmVkU2VjcmV0ID0gYXdhaXQgZWNkaGVzLmRlcml2ZUtleShlcGssIGtleSwgYWxnID09PSAnRUNESC1FUycgPyBqb3NlSGVhZGVyLmVuYyA6IGFsZywgYWxnID09PSAnRUNESC1FUycgPyBjZWtMZW5ndGgoam9zZUhlYWRlci5lbmMpIDogcGFyc2VJbnQoYWxnLnNsaWNlKC01LCAtMiksIDEwKSwgcGFydHlVSW5mbywgcGFydHlWSW5mbyk7XG4gICAgICAgICAgICBpZiAoYWxnID09PSAnRUNESC1FUycpXG4gICAgICAgICAgICAgICAgcmV0dXJuIHNoYXJlZFNlY3JldDtcbiAgICAgICAgICAgIGlmIChlbmNyeXB0ZWRLZXkgPT09IHVuZGVmaW5lZClcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgSldFSW52YWxpZCgnSldFIEVuY3J5cHRlZCBLZXkgbWlzc2luZycpO1xuICAgICAgICAgICAgcmV0dXJuIGFlc2t3LnVud3JhcChhbGcuc2xpY2UoLTYpLCBzaGFyZWRTZWNyZXQsIGVuY3J5cHRlZEtleSk7XG4gICAgICAgIH1cbiAgICAgICAgY2FzZSAnUlNBLU9BRVAnOlxuICAgICAgICBjYXNlICdSU0EtT0FFUC0yNTYnOlxuICAgICAgICBjYXNlICdSU0EtT0FFUC0zODQnOlxuICAgICAgICBjYXNlICdSU0EtT0FFUC01MTInOiB7XG4gICAgICAgICAgICBpZiAoZW5jcnlwdGVkS2V5ID09PSB1bmRlZmluZWQpXG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEpXRUludmFsaWQoJ0pXRSBFbmNyeXB0ZWQgS2V5IG1pc3NpbmcnKTtcbiAgICAgICAgICAgIGFzc2VydENyeXB0b0tleShrZXkpO1xuICAgICAgICAgICAgcmV0dXJuIHJzYWVzLmRlY3J5cHQoYWxnLCBrZXksIGVuY3J5cHRlZEtleSk7XG4gICAgICAgIH1cbiAgICAgICAgY2FzZSAnUEJFUzItSFMyNTYrQTEyOEtXJzpcbiAgICAgICAgY2FzZSAnUEJFUzItSFMzODQrQTE5MktXJzpcbiAgICAgICAgY2FzZSAnUEJFUzItSFM1MTIrQTI1NktXJzoge1xuICAgICAgICAgICAgaWYgKGVuY3J5cHRlZEtleSA9PT0gdW5kZWZpbmVkKVxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBKV0VJbnZhbGlkKCdKV0UgRW5jcnlwdGVkIEtleSBtaXNzaW5nJyk7XG4gICAgICAgICAgICBpZiAodHlwZW9mIGpvc2VIZWFkZXIucDJjICE9PSAnbnVtYmVyJylcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgSldFSW52YWxpZChgSk9TRSBIZWFkZXIgXCJwMmNcIiAoUEJFUzIgQ291bnQpIG1pc3Npbmcgb3IgaW52YWxpZGApO1xuICAgICAgICAgICAgY29uc3QgcDJjTGltaXQgPSBvcHRpb25zPy5tYXhQQkVTMkNvdW50IHx8IDEwXzAwMDtcbiAgICAgICAgICAgIGlmIChqb3NlSGVhZGVyLnAyYyA+IHAyY0xpbWl0KVxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBKV0VJbnZhbGlkKGBKT1NFIEhlYWRlciBcInAyY1wiIChQQkVTMiBDb3VudCkgb3V0IGlzIG9mIGFjY2VwdGFibGUgYm91bmRzYCk7XG4gICAgICAgICAgICBpZiAodHlwZW9mIGpvc2VIZWFkZXIucDJzICE9PSAnc3RyaW5nJylcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgSldFSW52YWxpZChgSk9TRSBIZWFkZXIgXCJwMnNcIiAoUEJFUzIgU2FsdCkgbWlzc2luZyBvciBpbnZhbGlkYCk7XG4gICAgICAgICAgICBsZXQgcDJzO1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICBwMnMgPSBiNjR1KGpvc2VIZWFkZXIucDJzKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNhdGNoIHtcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgSldFSW52YWxpZCgnRmFpbGVkIHRvIGJhc2U2NHVybCBkZWNvZGUgdGhlIHAycycpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIHBiZXMya3cudW53cmFwKGFsZywga2V5LCBlbmNyeXB0ZWRLZXksIGpvc2VIZWFkZXIucDJjLCBwMnMpO1xuICAgICAgICB9XG4gICAgICAgIGNhc2UgJ0ExMjhLVyc6XG4gICAgICAgIGNhc2UgJ0ExOTJLVyc6XG4gICAgICAgIGNhc2UgJ0EyNTZLVyc6IHtcbiAgICAgICAgICAgIGlmIChlbmNyeXB0ZWRLZXkgPT09IHVuZGVmaW5lZClcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgSldFSW52YWxpZCgnSldFIEVuY3J5cHRlZCBLZXkgbWlzc2luZycpO1xuICAgICAgICAgICAgcmV0dXJuIGFlc2t3LnVud3JhcChhbGcsIGtleSwgZW5jcnlwdGVkS2V5KTtcbiAgICAgICAgfVxuICAgICAgICBjYXNlICdBMTI4R0NNS1cnOlxuICAgICAgICBjYXNlICdBMTkyR0NNS1cnOlxuICAgICAgICBjYXNlICdBMjU2R0NNS1cnOiB7XG4gICAgICAgICAgICBpZiAoZW5jcnlwdGVkS2V5ID09PSB1bmRlZmluZWQpXG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEpXRUludmFsaWQoJ0pXRSBFbmNyeXB0ZWQgS2V5IG1pc3NpbmcnKTtcbiAgICAgICAgICAgIGlmICh0eXBlb2Ygam9zZUhlYWRlci5pdiAhPT0gJ3N0cmluZycpXG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEpXRUludmFsaWQoYEpPU0UgSGVhZGVyIFwiaXZcIiAoSW5pdGlhbGl6YXRpb24gVmVjdG9yKSBtaXNzaW5nIG9yIGludmFsaWRgKTtcbiAgICAgICAgICAgIGlmICh0eXBlb2Ygam9zZUhlYWRlci50YWcgIT09ICdzdHJpbmcnKVxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBKV0VJbnZhbGlkKGBKT1NFIEhlYWRlciBcInRhZ1wiIChBdXRoZW50aWNhdGlvbiBUYWcpIG1pc3Npbmcgb3IgaW52YWxpZGApO1xuICAgICAgICAgICAgbGV0IGl2O1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICBpdiA9IGI2NHUoam9zZUhlYWRlci5pdik7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYXRjaCB7XG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEpXRUludmFsaWQoJ0ZhaWxlZCB0byBiYXNlNjR1cmwgZGVjb2RlIHRoZSBpdicpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgbGV0IHRhZztcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgdGFnID0gYjY0dShqb3NlSGVhZGVyLnRhZyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYXRjaCB7XG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEpXRUludmFsaWQoJ0ZhaWxlZCB0byBiYXNlNjR1cmwgZGVjb2RlIHRoZSB0YWcnKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiBhZXNHY21LdyhhbGcsIGtleSwgZW5jcnlwdGVkS2V5LCBpdiwgdGFnKTtcbiAgICAgICAgfVxuICAgICAgICBkZWZhdWx0OiB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgSk9TRU5vdFN1cHBvcnRlZCgnSW52YWxpZCBvciB1bnN1cHBvcnRlZCBcImFsZ1wiIChKV0UgQWxnb3JpdGhtKSBoZWFkZXIgdmFsdWUnKTtcbiAgICAgICAgfVxuICAgIH1cbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/lib/decrypt_key_management.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/webapi/lib/digest.js":
/*!*****************************************************!*\
  !*** ./node_modules/jose/dist/webapi/lib/digest.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (algorithm, data) => {\n    const subtleDigest = `SHA-${algorithm.slice(-3)}`;\n    return new Uint8Array(await crypto.subtle.digest(subtleDigest, data));\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L3dlYmFwaS9saWIvZGlnZXN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZTtBQUNmLGdDQUFnQyxvQkFBb0I7QUFDcEQ7QUFDQSxDQUFDLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcV2luZG93cyAxMVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxJbm5vdmF0aXZlIENlbnRyZVxcY3JtLXN0YWZmLXNlcnZpY2VcXG5vZGVfbW9kdWxlc1xcam9zZVxcZGlzdFxcd2ViYXBpXFxsaWJcXGRpZ2VzdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBhc3luYyAoYWxnb3JpdGhtLCBkYXRhKSA9PiB7XG4gICAgY29uc3Qgc3VidGxlRGlnZXN0ID0gYFNIQS0ke2FsZ29yaXRobS5zbGljZSgtMyl9YDtcbiAgICByZXR1cm4gbmV3IFVpbnQ4QXJyYXkoYXdhaXQgY3J5cHRvLnN1YnRsZS5kaWdlc3Qoc3VidGxlRGlnZXN0LCBkYXRhKSk7XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/lib/digest.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/webapi/lib/ecdhes.js":
/*!*****************************************************!*\
  !*** ./node_modules/jose/dist/webapi/lib/ecdhes.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   allowed: () => (/* binding */ allowed),\n/* harmony export */   deriveKey: () => (/* binding */ deriveKey)\n/* harmony export */ });\n/* harmony import */ var _buffer_utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./buffer_utils.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/buffer_utils.js\");\n/* harmony import */ var _crypto_key_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./crypto_key.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/crypto_key.js\");\n/* harmony import */ var _digest_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./digest.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/digest.js\");\n\n\n\nfunction lengthAndInput(input) {\n    return (0,_buffer_utils_js__WEBPACK_IMPORTED_MODULE_0__.concat)((0,_buffer_utils_js__WEBPACK_IMPORTED_MODULE_0__.uint32be)(input.length), input);\n}\nasync function concatKdf(secret, bits, value) {\n    const iterations = Math.ceil((bits >> 3) / 32);\n    const res = new Uint8Array(iterations * 32);\n    for (let iter = 0; iter < iterations; iter++) {\n        const buf = new Uint8Array(4 + secret.length + value.length);\n        buf.set((0,_buffer_utils_js__WEBPACK_IMPORTED_MODULE_0__.uint32be)(iter + 1));\n        buf.set(secret, 4);\n        buf.set(value, 4 + secret.length);\n        res.set(await (0,_digest_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])('sha256', buf), iter * 32);\n    }\n    return res.slice(0, bits >> 3);\n}\nasync function deriveKey(publicKey, privateKey, algorithm, keyLength, apu = new Uint8Array(0), apv = new Uint8Array(0)) {\n    (0,_crypto_key_js__WEBPACK_IMPORTED_MODULE_2__.checkEncCryptoKey)(publicKey, 'ECDH');\n    (0,_crypto_key_js__WEBPACK_IMPORTED_MODULE_2__.checkEncCryptoKey)(privateKey, 'ECDH', 'deriveBits');\n    const value = (0,_buffer_utils_js__WEBPACK_IMPORTED_MODULE_0__.concat)(lengthAndInput(_buffer_utils_js__WEBPACK_IMPORTED_MODULE_0__.encoder.encode(algorithm)), lengthAndInput(apu), lengthAndInput(apv), (0,_buffer_utils_js__WEBPACK_IMPORTED_MODULE_0__.uint32be)(keyLength));\n    let length;\n    if (publicKey.algorithm.name === 'X25519') {\n        length = 256;\n    }\n    else {\n        length =\n            Math.ceil(parseInt(publicKey.algorithm.namedCurve.slice(-3), 10) / 8) << 3;\n    }\n    const sharedSecret = new Uint8Array(await crypto.subtle.deriveBits({\n        name: publicKey.algorithm.name,\n        public: publicKey,\n    }, privateKey, length));\n    return concatKdf(sharedSecret, keyLength, value);\n}\nfunction allowed(key) {\n    switch (key.algorithm.namedCurve) {\n        case 'P-256':\n        case 'P-384':\n        case 'P-521':\n            return true;\n        default:\n            return key.algorithm.name === 'X25519';\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/lib/ecdhes.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/webapi/lib/encrypt.js":
/*!******************************************************!*\
  !*** ./node_modules/jose/dist/webapi/lib/encrypt.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _buffer_utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./buffer_utils.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/buffer_utils.js\");\n/* harmony import */ var _check_iv_length_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./check_iv_length.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/check_iv_length.js\");\n/* harmony import */ var _check_cek_length_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./check_cek_length.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/check_cek_length.js\");\n/* harmony import */ var _crypto_key_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./crypto_key.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/crypto_key.js\");\n/* harmony import */ var _invalid_key_input_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./invalid_key_input.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/invalid_key_input.js\");\n/* harmony import */ var _iv_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./iv.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/iv.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/webapi/util/errors.js\");\n/* harmony import */ var _is_key_like_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./is_key_like.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/is_key_like.js\");\n\n\n\n\n\n\n\n\nasync function cbcEncrypt(enc, plaintext, cek, iv, aad) {\n    if (!(cek instanceof Uint8Array)) {\n        throw new TypeError((0,_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(cek, 'Uint8Array'));\n    }\n    const keySize = parseInt(enc.slice(1, 4), 10);\n    const encKey = await crypto.subtle.importKey('raw', cek.subarray(keySize >> 3), 'AES-CBC', false, ['encrypt']);\n    const macKey = await crypto.subtle.importKey('raw', cek.subarray(0, keySize >> 3), {\n        hash: `SHA-${keySize << 1}`,\n        name: 'HMAC',\n    }, false, ['sign']);\n    const ciphertext = new Uint8Array(await crypto.subtle.encrypt({\n        iv,\n        name: 'AES-CBC',\n    }, encKey, plaintext));\n    const macData = (0,_buffer_utils_js__WEBPACK_IMPORTED_MODULE_1__.concat)(aad, iv, ciphertext, (0,_buffer_utils_js__WEBPACK_IMPORTED_MODULE_1__.uint64be)(aad.length << 3));\n    const tag = new Uint8Array((await crypto.subtle.sign('HMAC', macKey, macData)).slice(0, keySize >> 3));\n    return { ciphertext, tag, iv };\n}\nasync function gcmEncrypt(enc, plaintext, cek, iv, aad) {\n    let encKey;\n    if (cek instanceof Uint8Array) {\n        encKey = await crypto.subtle.importKey('raw', cek, 'AES-GCM', false, ['encrypt']);\n    }\n    else {\n        (0,_crypto_key_js__WEBPACK_IMPORTED_MODULE_2__.checkEncCryptoKey)(cek, enc, 'encrypt');\n        encKey = cek;\n    }\n    const encrypted = new Uint8Array(await crypto.subtle.encrypt({\n        additionalData: aad,\n        iv,\n        name: 'AES-GCM',\n        tagLength: 128,\n    }, encKey, plaintext));\n    const tag = encrypted.slice(-16);\n    const ciphertext = encrypted.slice(0, -16);\n    return { ciphertext, tag, iv };\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (enc, plaintext, cek, iv, aad) => {\n    if (!(0,_is_key_like_js__WEBPACK_IMPORTED_MODULE_3__.isCryptoKey)(cek) && !(cek instanceof Uint8Array)) {\n        throw new TypeError((0,_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(cek, 'CryptoKey', 'KeyObject', 'Uint8Array', 'JSON Web Key'));\n    }\n    if (iv) {\n        (0,_check_iv_length_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(enc, iv);\n    }\n    else {\n        iv = (0,_iv_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(enc);\n    }\n    switch (enc) {\n        case 'A128CBC-HS256':\n        case 'A192CBC-HS384':\n        case 'A256CBC-HS512':\n            if (cek instanceof Uint8Array) {\n                (0,_check_cek_length_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(cek, parseInt(enc.slice(-3), 10));\n            }\n            return cbcEncrypt(enc, plaintext, cek, iv, aad);\n        case 'A128GCM':\n        case 'A192GCM':\n        case 'A256GCM':\n            if (cek instanceof Uint8Array) {\n                (0,_check_cek_length_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(cek, parseInt(enc.slice(1, 4), 10));\n            }\n            return gcmEncrypt(enc, plaintext, cek, iv, aad);\n        default:\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_7__.JOSENotSupported('Unsupported JWE Content Encryption Algorithm');\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/lib/encrypt.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/webapi/lib/encrypt_key_management.js":
/*!*********************************************************************!*\
  !*** ./node_modules/jose/dist/webapi/lib/encrypt_key_management.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _aeskw_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./aeskw.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/aeskw.js\");\n/* harmony import */ var _ecdhes_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ecdhes.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/ecdhes.js\");\n/* harmony import */ var _pbes2kw_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./pbes2kw.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/pbes2kw.js\");\n/* harmony import */ var _rsaes_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./rsaes.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/rsaes.js\");\n/* harmony import */ var _util_base64url_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../util/base64url.js */ \"(rsc)/./node_modules/jose/dist/webapi/util/base64url.js\");\n/* harmony import */ var _normalize_key_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./normalize_key.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/normalize_key.js\");\n/* harmony import */ var _lib_cek_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../lib/cek.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/cek.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/webapi/util/errors.js\");\n/* harmony import */ var _key_export_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../key/export.js */ \"(rsc)/./node_modules/jose/dist/webapi/key/export.js\");\n/* harmony import */ var _aesgcmkw_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./aesgcmkw.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/aesgcmkw.js\");\n/* harmony import */ var _is_key_like_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is_key_like.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/is_key_like.js\");\n\n\n\n\n\n\n\n\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (alg, enc, key, providedCek, providedParameters = {}) => {\n    let encryptedKey;\n    let parameters;\n    let cek;\n    switch (alg) {\n        case 'dir': {\n            cek = key;\n            break;\n        }\n        case 'ECDH-ES':\n        case 'ECDH-ES+A128KW':\n        case 'ECDH-ES+A192KW':\n        case 'ECDH-ES+A256KW': {\n            (0,_is_key_like_js__WEBPACK_IMPORTED_MODULE_0__.assertCryptoKey)(key);\n            if (!_ecdhes_js__WEBPACK_IMPORTED_MODULE_1__.allowed(key)) {\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JOSENotSupported('ECDH with the provided key is not allowed or not supported by your javascript runtime');\n            }\n            const { apu, apv } = providedParameters;\n            let ephemeralKey;\n            if (providedParameters.epk) {\n                ephemeralKey = (await (0,_normalize_key_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(providedParameters.epk, alg));\n            }\n            else {\n                ephemeralKey = (await crypto.subtle.generateKey(key.algorithm, true, ['deriveBits'])).privateKey;\n            }\n            const { x, y, crv, kty } = await (0,_key_export_js__WEBPACK_IMPORTED_MODULE_4__.exportJWK)(ephemeralKey);\n            const sharedSecret = await _ecdhes_js__WEBPACK_IMPORTED_MODULE_1__.deriveKey(key, ephemeralKey, alg === 'ECDH-ES' ? enc : alg, alg === 'ECDH-ES' ? (0,_lib_cek_js__WEBPACK_IMPORTED_MODULE_5__.bitLength)(enc) : parseInt(alg.slice(-5, -2), 10), apu, apv);\n            parameters = { epk: { x, crv, kty } };\n            if (kty === 'EC')\n                parameters.epk.y = y;\n            if (apu)\n                parameters.apu = (0,_util_base64url_js__WEBPACK_IMPORTED_MODULE_6__.encode)(apu);\n            if (apv)\n                parameters.apv = (0,_util_base64url_js__WEBPACK_IMPORTED_MODULE_6__.encode)(apv);\n            if (alg === 'ECDH-ES') {\n                cek = sharedSecret;\n                break;\n            }\n            cek = providedCek || (0,_lib_cek_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(enc);\n            const kwAlg = alg.slice(-6);\n            encryptedKey = await _aeskw_js__WEBPACK_IMPORTED_MODULE_7__.wrap(kwAlg, sharedSecret, cek);\n            break;\n        }\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512': {\n            cek = providedCek || (0,_lib_cek_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(enc);\n            (0,_is_key_like_js__WEBPACK_IMPORTED_MODULE_0__.assertCryptoKey)(key);\n            encryptedKey = await _rsaes_js__WEBPACK_IMPORTED_MODULE_8__.encrypt(alg, key, cek);\n            break;\n        }\n        case 'PBES2-HS256+A128KW':\n        case 'PBES2-HS384+A192KW':\n        case 'PBES2-HS512+A256KW': {\n            cek = providedCek || (0,_lib_cek_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(enc);\n            const { p2c, p2s } = providedParameters;\n            ({ encryptedKey, ...parameters } = await _pbes2kw_js__WEBPACK_IMPORTED_MODULE_9__.wrap(alg, key, cek, p2c, p2s));\n            break;\n        }\n        case 'A128KW':\n        case 'A192KW':\n        case 'A256KW': {\n            cek = providedCek || (0,_lib_cek_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(enc);\n            encryptedKey = await _aeskw_js__WEBPACK_IMPORTED_MODULE_7__.wrap(alg, key, cek);\n            break;\n        }\n        case 'A128GCMKW':\n        case 'A192GCMKW':\n        case 'A256GCMKW': {\n            cek = providedCek || (0,_lib_cek_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(enc);\n            const { iv } = providedParameters;\n            ({ encryptedKey, ...parameters } = await (0,_aesgcmkw_js__WEBPACK_IMPORTED_MODULE_10__.wrap)(alg, key, cek, iv));\n            break;\n        }\n        default: {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JOSENotSupported('Invalid or unsupported \"alg\" (JWE Algorithm) header value');\n        }\n    }\n    return { cek, encryptedKey, parameters };\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/lib/encrypt_key_management.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/webapi/lib/epoch.js":
/*!****************************************************!*\
  !*** ./node_modules/jose/dist/webapi/lib/epoch.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((date) => Math.floor(date.getTime() / 1000));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L3dlYmFwaS9saWIvZXBvY2guanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLDJDQUEyQyxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFdpbmRvd3MgMTFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcSW5ub3ZhdGl2ZSBDZW50cmVcXGNybS1zdGFmZi1zZXJ2aWNlXFxub2RlX21vZHVsZXNcXGpvc2VcXGRpc3RcXHdlYmFwaVxcbGliXFxlcG9jaC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCAoZGF0ZSkgPT4gTWF0aC5mbG9vcihkYXRlLmdldFRpbWUoKSAvIDEwMDApO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/lib/epoch.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/webapi/lib/invalid_key_input.js":
/*!****************************************************************!*\
  !*** ./node_modules/jose/dist/webapi/lib/invalid_key_input.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   withAlg: () => (/* binding */ withAlg)\n/* harmony export */ });\nfunction message(msg, actual, ...types) {\n    types = types.filter(Boolean);\n    if (types.length > 2) {\n        const last = types.pop();\n        msg += `one of type ${types.join(', ')}, or ${last}.`;\n    }\n    else if (types.length === 2) {\n        msg += `one of type ${types[0]} or ${types[1]}.`;\n    }\n    else {\n        msg += `of type ${types[0]}.`;\n    }\n    if (actual == null) {\n        msg += ` Received ${actual}`;\n    }\n    else if (typeof actual === 'function' && actual.name) {\n        msg += ` Received function ${actual.name}`;\n    }\n    else if (typeof actual === 'object' && actual != null) {\n        if (actual.constructor?.name) {\n            msg += ` Received an instance of ${actual.constructor.name}`;\n        }\n    }\n    return msg;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((actual, ...types) => {\n    return message('Key must be ', actual, ...types);\n});\nfunction withAlg(alg, actual, ...types) {\n    return message(`Key for the ${alg} algorithm must be `, actual, ...types);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/lib/invalid_key_input.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/webapi/lib/is_disjoint.js":
/*!**********************************************************!*\
  !*** ./node_modules/jose/dist/webapi/lib/is_disjoint.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((...headers) => {\n    const sources = headers.filter(Boolean);\n    if (sources.length === 0 || sources.length === 1) {\n        return true;\n    }\n    let acc;\n    for (const header of sources) {\n        const parameters = Object.keys(header);\n        if (!acc || acc.size === 0) {\n            acc = new Set(parameters);\n            continue;\n        }\n        for (const parameter of parameters) {\n            if (acc.has(parameter)) {\n                return false;\n            }\n            acc.add(parameter);\n        }\n    }\n    return true;\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L3dlYmFwaS9saWIvaXNfZGlzam9pbnQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcV2luZG93cyAxMVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxJbm5vdmF0aXZlIENlbnRyZVxcY3JtLXN0YWZmLXNlcnZpY2VcXG5vZGVfbW9kdWxlc1xcam9zZVxcZGlzdFxcd2ViYXBpXFxsaWJcXGlzX2Rpc2pvaW50LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0ICguLi5oZWFkZXJzKSA9PiB7XG4gICAgY29uc3Qgc291cmNlcyA9IGhlYWRlcnMuZmlsdGVyKEJvb2xlYW4pO1xuICAgIGlmIChzb3VyY2VzLmxlbmd0aCA9PT0gMCB8fCBzb3VyY2VzLmxlbmd0aCA9PT0gMSkge1xuICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9XG4gICAgbGV0IGFjYztcbiAgICBmb3IgKGNvbnN0IGhlYWRlciBvZiBzb3VyY2VzKSB7XG4gICAgICAgIGNvbnN0IHBhcmFtZXRlcnMgPSBPYmplY3Qua2V5cyhoZWFkZXIpO1xuICAgICAgICBpZiAoIWFjYyB8fCBhY2Muc2l6ZSA9PT0gMCkge1xuICAgICAgICAgICAgYWNjID0gbmV3IFNldChwYXJhbWV0ZXJzKTtcbiAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICB9XG4gICAgICAgIGZvciAoY29uc3QgcGFyYW1ldGVyIG9mIHBhcmFtZXRlcnMpIHtcbiAgICAgICAgICAgIGlmIChhY2MuaGFzKHBhcmFtZXRlcikpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBhY2MuYWRkKHBhcmFtZXRlcik7XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIHRydWU7XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/lib/is_disjoint.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/webapi/lib/is_jwk.js":
/*!*****************************************************!*\
  !*** ./node_modules/jose/dist/webapi/lib/is_jwk.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isJWK: () => (/* binding */ isJWK),\n/* harmony export */   isPrivateJWK: () => (/* binding */ isPrivateJWK),\n/* harmony export */   isPublicJWK: () => (/* binding */ isPublicJWK),\n/* harmony export */   isSecretJWK: () => (/* binding */ isSecretJWK)\n/* harmony export */ });\n/* harmony import */ var _is_object_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is_object.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/is_object.js\");\n\nfunction isJWK(key) {\n    return (0,_is_object_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(key) && typeof key.kty === 'string';\n}\nfunction isPrivateJWK(key) {\n    return key.kty !== 'oct' && typeof key.d === 'string';\n}\nfunction isPublicJWK(key) {\n    return key.kty !== 'oct' && typeof key.d === 'undefined';\n}\nfunction isSecretJWK(key) {\n    return key.kty === 'oct' && typeof key.k === 'string';\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L3dlYmFwaS9saWIvaXNfandrLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQXNDO0FBQy9CO0FBQ1AsV0FBVyx5REFBUTtBQUNuQjtBQUNPO0FBQ1A7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxXaW5kb3dzIDExXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXElubm92YXRpdmUgQ2VudHJlXFxjcm0tc3RhZmYtc2VydmljZVxcbm9kZV9tb2R1bGVzXFxqb3NlXFxkaXN0XFx3ZWJhcGlcXGxpYlxcaXNfandrLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBpc09iamVjdCBmcm9tICcuL2lzX29iamVjdC5qcyc7XG5leHBvcnQgZnVuY3Rpb24gaXNKV0soa2V5KSB7XG4gICAgcmV0dXJuIGlzT2JqZWN0KGtleSkgJiYgdHlwZW9mIGtleS5rdHkgPT09ICdzdHJpbmcnO1xufVxuZXhwb3J0IGZ1bmN0aW9uIGlzUHJpdmF0ZUpXSyhrZXkpIHtcbiAgICByZXR1cm4ga2V5Lmt0eSAhPT0gJ29jdCcgJiYgdHlwZW9mIGtleS5kID09PSAnc3RyaW5nJztcbn1cbmV4cG9ydCBmdW5jdGlvbiBpc1B1YmxpY0pXSyhrZXkpIHtcbiAgICByZXR1cm4ga2V5Lmt0eSAhPT0gJ29jdCcgJiYgdHlwZW9mIGtleS5kID09PSAndW5kZWZpbmVkJztcbn1cbmV4cG9ydCBmdW5jdGlvbiBpc1NlY3JldEpXSyhrZXkpIHtcbiAgICByZXR1cm4ga2V5Lmt0eSA9PT0gJ29jdCcgJiYgdHlwZW9mIGtleS5rID09PSAnc3RyaW5nJztcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/lib/is_jwk.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/webapi/lib/is_key_like.js":
/*!**********************************************************!*\
  !*** ./node_modules/jose/dist/webapi/lib/is_key_like.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assertCryptoKey: () => (/* binding */ assertCryptoKey),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   isCryptoKey: () => (/* binding */ isCryptoKey),\n/* harmony export */   isKeyObject: () => (/* binding */ isKeyObject)\n/* harmony export */ });\nfunction assertCryptoKey(key) {\n    if (!isCryptoKey(key)) {\n        throw new Error('CryptoKey instance expected');\n    }\n}\nfunction isCryptoKey(key) {\n    return key?.[Symbol.toStringTag] === 'CryptoKey';\n}\nfunction isKeyObject(key) {\n    return key?.[Symbol.toStringTag] === 'KeyObject';\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((key) => {\n    return isCryptoKey(key) || isKeyObject(key);\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L3dlYmFwaS9saWIvaXNfa2V5X2xpa2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQSxpRUFBZTtBQUNmO0FBQ0EsQ0FBQyxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFdpbmRvd3MgMTFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcSW5ub3ZhdGl2ZSBDZW50cmVcXGNybS1zdGFmZi1zZXJ2aWNlXFxub2RlX21vZHVsZXNcXGpvc2VcXGRpc3RcXHdlYmFwaVxcbGliXFxpc19rZXlfbGlrZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gYXNzZXJ0Q3J5cHRvS2V5KGtleSkge1xuICAgIGlmICghaXNDcnlwdG9LZXkoa2V5KSkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0NyeXB0b0tleSBpbnN0YW5jZSBleHBlY3RlZCcpO1xuICAgIH1cbn1cbmV4cG9ydCBmdW5jdGlvbiBpc0NyeXB0b0tleShrZXkpIHtcbiAgICByZXR1cm4ga2V5Py5bU3ltYm9sLnRvU3RyaW5nVGFnXSA9PT0gJ0NyeXB0b0tleSc7XG59XG5leHBvcnQgZnVuY3Rpb24gaXNLZXlPYmplY3Qoa2V5KSB7XG4gICAgcmV0dXJuIGtleT8uW1N5bWJvbC50b1N0cmluZ1RhZ10gPT09ICdLZXlPYmplY3QnO1xufVxuZXhwb3J0IGRlZmF1bHQgKGtleSkgPT4ge1xuICAgIHJldHVybiBpc0NyeXB0b0tleShrZXkpIHx8IGlzS2V5T2JqZWN0KGtleSk7XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/lib/is_key_like.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/webapi/lib/is_object.js":
/*!********************************************************!*\
  !*** ./node_modules/jose/dist/webapi/lib/is_object.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nfunction isObjectLike(value) {\n    return typeof value === 'object' && value !== null;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((input) => {\n    if (!isObjectLike(input) || Object.prototype.toString.call(input) !== '[object Object]') {\n        return false;\n    }\n    if (Object.getPrototypeOf(input) === null) {\n        return true;\n    }\n    let proto = input;\n    while (Object.getPrototypeOf(proto) !== null) {\n        proto = Object.getPrototypeOf(proto);\n    }\n    return Object.getPrototypeOf(input) === proto;\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L3dlYmFwaS9saWIvaXNfb2JqZWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQSxpRUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcV2luZG93cyAxMVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxJbm5vdmF0aXZlIENlbnRyZVxcY3JtLXN0YWZmLXNlcnZpY2VcXG5vZGVfbW9kdWxlc1xcam9zZVxcZGlzdFxcd2ViYXBpXFxsaWJcXGlzX29iamVjdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiBpc09iamVjdExpa2UodmFsdWUpIHtcbiAgICByZXR1cm4gdHlwZW9mIHZhbHVlID09PSAnb2JqZWN0JyAmJiB2YWx1ZSAhPT0gbnVsbDtcbn1cbmV4cG9ydCBkZWZhdWx0IChpbnB1dCkgPT4ge1xuICAgIGlmICghaXNPYmplY3RMaWtlKGlucHV0KSB8fCBPYmplY3QucHJvdG90eXBlLnRvU3RyaW5nLmNhbGwoaW5wdXQpICE9PSAnW29iamVjdCBPYmplY3RdJykge1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIGlmIChPYmplY3QuZ2V0UHJvdG90eXBlT2YoaW5wdXQpID09PSBudWxsKSB7XG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cbiAgICBsZXQgcHJvdG8gPSBpbnB1dDtcbiAgICB3aGlsZSAoT2JqZWN0LmdldFByb3RvdHlwZU9mKHByb3RvKSAhPT0gbnVsbCkge1xuICAgICAgICBwcm90byA9IE9iamVjdC5nZXRQcm90b3R5cGVPZihwcm90byk7XG4gICAgfVxuICAgIHJldHVybiBPYmplY3QuZ2V0UHJvdG90eXBlT2YoaW5wdXQpID09PSBwcm90bztcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/lib/is_object.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/webapi/lib/iv.js":
/*!*************************************************!*\
  !*** ./node_modules/jose/dist/webapi/lib/iv.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bitLength: () => (/* binding */ bitLength),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/webapi/util/errors.js\");\n\nfunction bitLength(alg) {\n    switch (alg) {\n        case 'A128GCM':\n        case 'A128GCMKW':\n        case 'A192GCM':\n        case 'A192GCMKW':\n        case 'A256GCM':\n        case 'A256GCMKW':\n            return 96;\n        case 'A128CBC-HS256':\n        case 'A192CBC-HS384':\n        case 'A256CBC-HS512':\n            return 128;\n        default:\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JOSENotSupported(`Unsupported JWE Algorithm: ${alg}`);\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((alg) => crypto.getRandomValues(new Uint8Array(bitLength(alg) >> 3)));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L3dlYmFwaS9saWIvaXYuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXFEO0FBQzlDO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsNkRBQWdCLCtCQUErQixJQUFJO0FBQ3pFO0FBQ0E7QUFDQSxpRUFBZSxvRUFBb0UsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxXaW5kb3dzIDExXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXElubm92YXRpdmUgQ2VudHJlXFxjcm0tc3RhZmYtc2VydmljZVxcbm9kZV9tb2R1bGVzXFxqb3NlXFxkaXN0XFx3ZWJhcGlcXGxpYlxcaXYuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgSk9TRU5vdFN1cHBvcnRlZCB9IGZyb20gJy4uL3V0aWwvZXJyb3JzLmpzJztcbmV4cG9ydCBmdW5jdGlvbiBiaXRMZW5ndGgoYWxnKSB7XG4gICAgc3dpdGNoIChhbGcpIHtcbiAgICAgICAgY2FzZSAnQTEyOEdDTSc6XG4gICAgICAgIGNhc2UgJ0ExMjhHQ01LVyc6XG4gICAgICAgIGNhc2UgJ0ExOTJHQ00nOlxuICAgICAgICBjYXNlICdBMTkyR0NNS1cnOlxuICAgICAgICBjYXNlICdBMjU2R0NNJzpcbiAgICAgICAgY2FzZSAnQTI1NkdDTUtXJzpcbiAgICAgICAgICAgIHJldHVybiA5NjtcbiAgICAgICAgY2FzZSAnQTEyOENCQy1IUzI1Nic6XG4gICAgICAgIGNhc2UgJ0ExOTJDQkMtSFMzODQnOlxuICAgICAgICBjYXNlICdBMjU2Q0JDLUhTNTEyJzpcbiAgICAgICAgICAgIHJldHVybiAxMjg7XG4gICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICB0aHJvdyBuZXcgSk9TRU5vdFN1cHBvcnRlZChgVW5zdXBwb3J0ZWQgSldFIEFsZ29yaXRobTogJHthbGd9YCk7XG4gICAgfVxufVxuZXhwb3J0IGRlZmF1bHQgKGFsZykgPT4gY3J5cHRvLmdldFJhbmRvbVZhbHVlcyhuZXcgVWludDhBcnJheShiaXRMZW5ndGgoYWxnKSA+PiAzKSk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/lib/iv.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/webapi/lib/jwk_to_key.js":
/*!*********************************************************!*\
  !*** ./node_modules/jose/dist/webapi/lib/jwk_to_key.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/webapi/util/errors.js\");\n\nfunction subtleMapping(jwk) {\n    let algorithm;\n    let keyUsages;\n    switch (jwk.kty) {\n        case 'RSA': {\n            switch (jwk.alg) {\n                case 'PS256':\n                case 'PS384':\n                case 'PS512':\n                    algorithm = { name: 'RSA-PSS', hash: `SHA-${jwk.alg.slice(-3)}` };\n                    keyUsages = jwk.d ? ['sign'] : ['verify'];\n                    break;\n                case 'RS256':\n                case 'RS384':\n                case 'RS512':\n                    algorithm = { name: 'RSASSA-PKCS1-v1_5', hash: `SHA-${jwk.alg.slice(-3)}` };\n                    keyUsages = jwk.d ? ['sign'] : ['verify'];\n                    break;\n                case 'RSA-OAEP':\n                case 'RSA-OAEP-256':\n                case 'RSA-OAEP-384':\n                case 'RSA-OAEP-512':\n                    algorithm = {\n                        name: 'RSA-OAEP',\n                        hash: `SHA-${parseInt(jwk.alg.slice(-3), 10) || 1}`,\n                    };\n                    keyUsages = jwk.d ? ['decrypt', 'unwrapKey'] : ['encrypt', 'wrapKey'];\n                    break;\n                default:\n                    throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JOSENotSupported('Invalid or unsupported JWK \"alg\" (Algorithm) Parameter value');\n            }\n            break;\n        }\n        case 'EC': {\n            switch (jwk.alg) {\n                case 'ES256':\n                    algorithm = { name: 'ECDSA', namedCurve: 'P-256' };\n                    keyUsages = jwk.d ? ['sign'] : ['verify'];\n                    break;\n                case 'ES384':\n                    algorithm = { name: 'ECDSA', namedCurve: 'P-384' };\n                    keyUsages = jwk.d ? ['sign'] : ['verify'];\n                    break;\n                case 'ES512':\n                    algorithm = { name: 'ECDSA', namedCurve: 'P-521' };\n                    keyUsages = jwk.d ? ['sign'] : ['verify'];\n                    break;\n                case 'ECDH-ES':\n                case 'ECDH-ES+A128KW':\n                case 'ECDH-ES+A192KW':\n                case 'ECDH-ES+A256KW':\n                    algorithm = { name: 'ECDH', namedCurve: jwk.crv };\n                    keyUsages = jwk.d ? ['deriveBits'] : [];\n                    break;\n                default:\n                    throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JOSENotSupported('Invalid or unsupported JWK \"alg\" (Algorithm) Parameter value');\n            }\n            break;\n        }\n        case 'OKP': {\n            switch (jwk.alg) {\n                case 'Ed25519':\n                case 'EdDSA':\n                    algorithm = { name: 'Ed25519' };\n                    keyUsages = jwk.d ? ['sign'] : ['verify'];\n                    break;\n                case 'ECDH-ES':\n                case 'ECDH-ES+A128KW':\n                case 'ECDH-ES+A192KW':\n                case 'ECDH-ES+A256KW':\n                    algorithm = { name: jwk.crv };\n                    keyUsages = jwk.d ? ['deriveBits'] : [];\n                    break;\n                default:\n                    throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JOSENotSupported('Invalid or unsupported JWK \"alg\" (Algorithm) Parameter value');\n            }\n            break;\n        }\n        default:\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JOSENotSupported('Invalid or unsupported JWK \"kty\" (Key Type) Parameter value');\n    }\n    return { algorithm, keyUsages };\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (jwk) => {\n    if (!jwk.alg) {\n        throw new TypeError('\"alg\" argument is required when \"jwk.alg\" is not present');\n    }\n    const { algorithm, keyUsages } = subtleMapping(jwk);\n    const keyData = { ...jwk };\n    delete keyData.alg;\n    delete keyData.use;\n    return crypto.subtle.importKey('jwk', keyData, algorithm, jwk.ext ?? (jwk.d ? false : true), jwk.key_ops ?? keyUsages);\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L3dlYmFwaS9saWIvandrX3RvX2tleS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFxRDtBQUNyRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQ0FBa0MsOEJBQThCLGtCQUFrQjtBQUNsRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0NBQWtDLHdDQUF3QyxrQkFBa0I7QUFDNUY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFDQUFxQyxxQ0FBcUM7QUFDMUU7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4QkFBOEIsNkRBQWdCO0FBQzlDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtDQUFrQztBQUNsQztBQUNBO0FBQ0E7QUFDQSxrQ0FBa0M7QUFDbEM7QUFDQTtBQUNBO0FBQ0Esa0NBQWtDO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtDQUFrQztBQUNsQztBQUNBO0FBQ0E7QUFDQSw4QkFBOEIsNkRBQWdCO0FBQzlDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0NBQWtDO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtDQUFrQztBQUNsQztBQUNBO0FBQ0E7QUFDQSw4QkFBOEIsNkRBQWdCO0FBQzlDO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLDZEQUFnQjtBQUN0QztBQUNBLGFBQWE7QUFDYjtBQUNBLGlFQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0EsWUFBWSx1QkFBdUI7QUFDbkMsc0JBQXNCO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBLENBQUMsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxXaW5kb3dzIDExXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXElubm92YXRpdmUgQ2VudHJlXFxjcm0tc3RhZmYtc2VydmljZVxcbm9kZV9tb2R1bGVzXFxqb3NlXFxkaXN0XFx3ZWJhcGlcXGxpYlxcandrX3RvX2tleS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBKT1NFTm90U3VwcG9ydGVkIH0gZnJvbSAnLi4vdXRpbC9lcnJvcnMuanMnO1xuZnVuY3Rpb24gc3VidGxlTWFwcGluZyhqd2spIHtcbiAgICBsZXQgYWxnb3JpdGhtO1xuICAgIGxldCBrZXlVc2FnZXM7XG4gICAgc3dpdGNoIChqd2sua3R5KSB7XG4gICAgICAgIGNhc2UgJ1JTQSc6IHtcbiAgICAgICAgICAgIHN3aXRjaCAoandrLmFsZykge1xuICAgICAgICAgICAgICAgIGNhc2UgJ1BTMjU2JzpcbiAgICAgICAgICAgICAgICBjYXNlICdQUzM4NCc6XG4gICAgICAgICAgICAgICAgY2FzZSAnUFM1MTInOlxuICAgICAgICAgICAgICAgICAgICBhbGdvcml0aG0gPSB7IG5hbWU6ICdSU0EtUFNTJywgaGFzaDogYFNIQS0ke2p3ay5hbGcuc2xpY2UoLTMpfWAgfTtcbiAgICAgICAgICAgICAgICAgICAga2V5VXNhZ2VzID0gandrLmQgPyBbJ3NpZ24nXSA6IFsndmVyaWZ5J107XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIGNhc2UgJ1JTMjU2JzpcbiAgICAgICAgICAgICAgICBjYXNlICdSUzM4NCc6XG4gICAgICAgICAgICAgICAgY2FzZSAnUlM1MTInOlxuICAgICAgICAgICAgICAgICAgICBhbGdvcml0aG0gPSB7IG5hbWU6ICdSU0FTU0EtUEtDUzEtdjFfNScsIGhhc2g6IGBTSEEtJHtqd2suYWxnLnNsaWNlKC0zKX1gIH07XG4gICAgICAgICAgICAgICAgICAgIGtleVVzYWdlcyA9IGp3ay5kID8gWydzaWduJ10gOiBbJ3ZlcmlmeSddO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICBjYXNlICdSU0EtT0FFUCc6XG4gICAgICAgICAgICAgICAgY2FzZSAnUlNBLU9BRVAtMjU2JzpcbiAgICAgICAgICAgICAgICBjYXNlICdSU0EtT0FFUC0zODQnOlxuICAgICAgICAgICAgICAgIGNhc2UgJ1JTQS1PQUVQLTUxMic6XG4gICAgICAgICAgICAgICAgICAgIGFsZ29yaXRobSA9IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIG5hbWU6ICdSU0EtT0FFUCcsXG4gICAgICAgICAgICAgICAgICAgICAgICBoYXNoOiBgU0hBLSR7cGFyc2VJbnQoandrLmFsZy5zbGljZSgtMyksIDEwKSB8fCAxfWAsXG4gICAgICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgICAgIGtleVVzYWdlcyA9IGp3ay5kID8gWydkZWNyeXB0JywgJ3Vud3JhcEtleSddIDogWydlbmNyeXB0JywgJ3dyYXBLZXknXTtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgICAgICAgICAgdGhyb3cgbmV3IEpPU0VOb3RTdXBwb3J0ZWQoJ0ludmFsaWQgb3IgdW5zdXBwb3J0ZWQgSldLIFwiYWxnXCIgKEFsZ29yaXRobSkgUGFyYW1ldGVyIHZhbHVlJyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgfVxuICAgICAgICBjYXNlICdFQyc6IHtcbiAgICAgICAgICAgIHN3aXRjaCAoandrLmFsZykge1xuICAgICAgICAgICAgICAgIGNhc2UgJ0VTMjU2JzpcbiAgICAgICAgICAgICAgICAgICAgYWxnb3JpdGhtID0geyBuYW1lOiAnRUNEU0EnLCBuYW1lZEN1cnZlOiAnUC0yNTYnIH07XG4gICAgICAgICAgICAgICAgICAgIGtleVVzYWdlcyA9IGp3ay5kID8gWydzaWduJ10gOiBbJ3ZlcmlmeSddO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICBjYXNlICdFUzM4NCc6XG4gICAgICAgICAgICAgICAgICAgIGFsZ29yaXRobSA9IHsgbmFtZTogJ0VDRFNBJywgbmFtZWRDdXJ2ZTogJ1AtMzg0JyB9O1xuICAgICAgICAgICAgICAgICAgICBrZXlVc2FnZXMgPSBqd2suZCA/IFsnc2lnbiddIDogWyd2ZXJpZnknXTtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgY2FzZSAnRVM1MTInOlxuICAgICAgICAgICAgICAgICAgICBhbGdvcml0aG0gPSB7IG5hbWU6ICdFQ0RTQScsIG5hbWVkQ3VydmU6ICdQLTUyMScgfTtcbiAgICAgICAgICAgICAgICAgICAga2V5VXNhZ2VzID0gandrLmQgPyBbJ3NpZ24nXSA6IFsndmVyaWZ5J107XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIGNhc2UgJ0VDREgtRVMnOlxuICAgICAgICAgICAgICAgIGNhc2UgJ0VDREgtRVMrQTEyOEtXJzpcbiAgICAgICAgICAgICAgICBjYXNlICdFQ0RILUVTK0ExOTJLVyc6XG4gICAgICAgICAgICAgICAgY2FzZSAnRUNESC1FUytBMjU2S1cnOlxuICAgICAgICAgICAgICAgICAgICBhbGdvcml0aG0gPSB7IG5hbWU6ICdFQ0RIJywgbmFtZWRDdXJ2ZTogandrLmNydiB9O1xuICAgICAgICAgICAgICAgICAgICBrZXlVc2FnZXMgPSBqd2suZCA/IFsnZGVyaXZlQml0cyddIDogW107XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICAgICAgICAgIHRocm93IG5ldyBKT1NFTm90U3VwcG9ydGVkKCdJbnZhbGlkIG9yIHVuc3VwcG9ydGVkIEpXSyBcImFsZ1wiIChBbGdvcml0aG0pIFBhcmFtZXRlciB2YWx1ZScpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgIH1cbiAgICAgICAgY2FzZSAnT0tQJzoge1xuICAgICAgICAgICAgc3dpdGNoIChqd2suYWxnKSB7XG4gICAgICAgICAgICAgICAgY2FzZSAnRWQyNTUxOSc6XG4gICAgICAgICAgICAgICAgY2FzZSAnRWREU0EnOlxuICAgICAgICAgICAgICAgICAgICBhbGdvcml0aG0gPSB7IG5hbWU6ICdFZDI1NTE5JyB9O1xuICAgICAgICAgICAgICAgICAgICBrZXlVc2FnZXMgPSBqd2suZCA/IFsnc2lnbiddIDogWyd2ZXJpZnknXTtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgY2FzZSAnRUNESC1FUyc6XG4gICAgICAgICAgICAgICAgY2FzZSAnRUNESC1FUytBMTI4S1cnOlxuICAgICAgICAgICAgICAgIGNhc2UgJ0VDREgtRVMrQTE5MktXJzpcbiAgICAgICAgICAgICAgICBjYXNlICdFQ0RILUVTK0EyNTZLVyc6XG4gICAgICAgICAgICAgICAgICAgIGFsZ29yaXRobSA9IHsgbmFtZTogandrLmNydiB9O1xuICAgICAgICAgICAgICAgICAgICBrZXlVc2FnZXMgPSBqd2suZCA/IFsnZGVyaXZlQml0cyddIDogW107XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICAgICAgICAgIHRocm93IG5ldyBKT1NFTm90U3VwcG9ydGVkKCdJbnZhbGlkIG9yIHVuc3VwcG9ydGVkIEpXSyBcImFsZ1wiIChBbGdvcml0aG0pIFBhcmFtZXRlciB2YWx1ZScpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgIH1cbiAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgIHRocm93IG5ldyBKT1NFTm90U3VwcG9ydGVkKCdJbnZhbGlkIG9yIHVuc3VwcG9ydGVkIEpXSyBcImt0eVwiIChLZXkgVHlwZSkgUGFyYW1ldGVyIHZhbHVlJyk7XG4gICAgfVxuICAgIHJldHVybiB7IGFsZ29yaXRobSwga2V5VXNhZ2VzIH07XG59XG5leHBvcnQgZGVmYXVsdCBhc3luYyAoandrKSA9PiB7XG4gICAgaWYgKCFqd2suYWxnKSB7XG4gICAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoJ1wiYWxnXCIgYXJndW1lbnQgaXMgcmVxdWlyZWQgd2hlbiBcImp3ay5hbGdcIiBpcyBub3QgcHJlc2VudCcpO1xuICAgIH1cbiAgICBjb25zdCB7IGFsZ29yaXRobSwga2V5VXNhZ2VzIH0gPSBzdWJ0bGVNYXBwaW5nKGp3ayk7XG4gICAgY29uc3Qga2V5RGF0YSA9IHsgLi4uandrIH07XG4gICAgZGVsZXRlIGtleURhdGEuYWxnO1xuICAgIGRlbGV0ZSBrZXlEYXRhLnVzZTtcbiAgICByZXR1cm4gY3J5cHRvLnN1YnRsZS5pbXBvcnRLZXkoJ2p3aycsIGtleURhdGEsIGFsZ29yaXRobSwgandrLmV4dCA/PyAoandrLmQgPyBmYWxzZSA6IHRydWUpLCBqd2sua2V5X29wcyA/PyBrZXlVc2FnZXMpO1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/lib/jwk_to_key.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/webapi/lib/jwt_claims_set.js":
/*!*************************************************************!*\
  !*** ./node_modules/jose/dist/webapi/lib/jwt_claims_set.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   JWTClaimsBuilder: () => (/* binding */ JWTClaimsBuilder),\n/* harmony export */   validateClaimsSet: () => (/* binding */ validateClaimsSet)\n/* harmony export */ });\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/webapi/util/errors.js\");\n/* harmony import */ var _buffer_utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./buffer_utils.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/buffer_utils.js\");\n/* harmony import */ var _epoch_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./epoch.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/epoch.js\");\n/* harmony import */ var _secs_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./secs.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/secs.js\");\n/* harmony import */ var _is_object_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./is_object.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/is_object.js\");\n\n\n\n\n\n\nfunction validateInput(label, input) {\n    if (!Number.isFinite(input)) {\n        throw new TypeError(`Invalid ${label} input`);\n    }\n    return input;\n}\nconst normalizeTyp = (value) => {\n    if (value.includes('/')) {\n        return value.toLowerCase();\n    }\n    return `application/${value.toLowerCase()}`;\n};\nconst checkAudiencePresence = (audPayload, audOption) => {\n    if (typeof audPayload === 'string') {\n        return audOption.includes(audPayload);\n    }\n    if (Array.isArray(audPayload)) {\n        return audOption.some(Set.prototype.has.bind(new Set(audPayload)));\n    }\n    return false;\n};\nfunction validateClaimsSet(protectedHeader, encodedPayload, options = {}) {\n    let payload;\n    try {\n        payload = JSON.parse(_buffer_utils_js__WEBPACK_IMPORTED_MODULE_0__.decoder.decode(encodedPayload));\n    }\n    catch {\n    }\n    if (!(0,_is_object_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(payload)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTInvalid('JWT Claims Set must be a top-level JSON object');\n    }\n    const { typ } = options;\n    if (typ &&\n        (typeof protectedHeader.typ !== 'string' ||\n            normalizeTyp(protectedHeader.typ) !== normalizeTyp(typ))) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('unexpected \"typ\" JWT header value', payload, 'typ', 'check_failed');\n    }\n    const { requiredClaims = [], issuer, subject, audience, maxTokenAge } = options;\n    const presenceCheck = [...requiredClaims];\n    if (maxTokenAge !== undefined)\n        presenceCheck.push('iat');\n    if (audience !== undefined)\n        presenceCheck.push('aud');\n    if (subject !== undefined)\n        presenceCheck.push('sub');\n    if (issuer !== undefined)\n        presenceCheck.push('iss');\n    for (const claim of new Set(presenceCheck.reverse())) {\n        if (!(claim in payload)) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed(`missing required \"${claim}\" claim`, payload, claim, 'missing');\n        }\n    }\n    if (issuer &&\n        !(Array.isArray(issuer) ? issuer : [issuer]).includes(payload.iss)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('unexpected \"iss\" claim value', payload, 'iss', 'check_failed');\n    }\n    if (subject && payload.sub !== subject) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('unexpected \"sub\" claim value', payload, 'sub', 'check_failed');\n    }\n    if (audience &&\n        !checkAudiencePresence(payload.aud, typeof audience === 'string' ? [audience] : audience)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('unexpected \"aud\" claim value', payload, 'aud', 'check_failed');\n    }\n    let tolerance;\n    switch (typeof options.clockTolerance) {\n        case 'string':\n            tolerance = (0,_secs_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(options.clockTolerance);\n            break;\n        case 'number':\n            tolerance = options.clockTolerance;\n            break;\n        case 'undefined':\n            tolerance = 0;\n            break;\n        default:\n            throw new TypeError('Invalid clockTolerance option type');\n    }\n    const { currentDate } = options;\n    const now = (0,_epoch_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(currentDate || new Date());\n    if ((payload.iat !== undefined || maxTokenAge) && typeof payload.iat !== 'number') {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('\"iat\" claim must be a number', payload, 'iat', 'invalid');\n    }\n    if (payload.nbf !== undefined) {\n        if (typeof payload.nbf !== 'number') {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('\"nbf\" claim must be a number', payload, 'nbf', 'invalid');\n        }\n        if (payload.nbf > now + tolerance) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('\"nbf\" claim timestamp check failed', payload, 'nbf', 'check_failed');\n        }\n    }\n    if (payload.exp !== undefined) {\n        if (typeof payload.exp !== 'number') {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('\"exp\" claim must be a number', payload, 'exp', 'invalid');\n        }\n        if (payload.exp <= now - tolerance) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTExpired('\"exp\" claim timestamp check failed', payload, 'exp', 'check_failed');\n        }\n    }\n    if (maxTokenAge) {\n        const age = now - payload.iat;\n        const max = typeof maxTokenAge === 'number' ? maxTokenAge : (0,_secs_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(maxTokenAge);\n        if (age - tolerance > max) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTExpired('\"iat\" claim timestamp check failed (too far in the past)', payload, 'iat', 'check_failed');\n        }\n        if (age < 0 - tolerance) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('\"iat\" claim timestamp check failed (it should be in the past)', payload, 'iat', 'check_failed');\n        }\n    }\n    return payload;\n}\nclass JWTClaimsBuilder {\n    #payload;\n    constructor(payload) {\n        if (!(0,_is_object_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(payload)) {\n            throw new TypeError('JWT Claims Set MUST be an object');\n        }\n        this.#payload = structuredClone(payload);\n    }\n    data() {\n        return _buffer_utils_js__WEBPACK_IMPORTED_MODULE_0__.encoder.encode(JSON.stringify(this.#payload));\n    }\n    get iss() {\n        return this.#payload.iss;\n    }\n    set iss(value) {\n        this.#payload.iss = value;\n    }\n    get sub() {\n        return this.#payload.sub;\n    }\n    set sub(value) {\n        this.#payload.sub = value;\n    }\n    get aud() {\n        return this.#payload.aud;\n    }\n    set aud(value) {\n        this.#payload.aud = value;\n    }\n    set jti(value) {\n        this.#payload.jti = value;\n    }\n    set nbf(value) {\n        if (typeof value === 'number') {\n            this.#payload.nbf = validateInput('setNotBefore', value);\n        }\n        else if (value instanceof Date) {\n            this.#payload.nbf = validateInput('setNotBefore', (0,_epoch_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(value));\n        }\n        else {\n            this.#payload.nbf = (0,_epoch_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(new Date()) + (0,_secs_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(value);\n        }\n    }\n    set exp(value) {\n        if (typeof value === 'number') {\n            this.#payload.exp = validateInput('setExpirationTime', value);\n        }\n        else if (value instanceof Date) {\n            this.#payload.exp = validateInput('setExpirationTime', (0,_epoch_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(value));\n        }\n        else {\n            this.#payload.exp = (0,_epoch_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(new Date()) + (0,_secs_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(value);\n        }\n    }\n    set iat(value) {\n        if (typeof value === 'undefined') {\n            this.#payload.iat = (0,_epoch_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(new Date());\n        }\n        else if (value instanceof Date) {\n            this.#payload.iat = validateInput('setIssuedAt', (0,_epoch_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(value));\n        }\n        else if (typeof value === 'string') {\n            this.#payload.iat = validateInput('setIssuedAt', (0,_epoch_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(new Date()) + (0,_secs_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(value));\n        }\n        else {\n            this.#payload.iat = validateInput('setIssuedAt', value);\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/lib/jwt_claims_set.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/webapi/lib/key_to_jwk.js":
/*!*********************************************************!*\
  !*** ./node_modules/jose/dist/webapi/lib/key_to_jwk.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ keyToJWK)\n/* harmony export */ });\n/* harmony import */ var _invalid_key_input_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./invalid_key_input.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/invalid_key_input.js\");\n/* harmony import */ var _util_base64url_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/base64url.js */ \"(rsc)/./node_modules/jose/dist/webapi/util/base64url.js\");\n/* harmony import */ var _is_key_like_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is_key_like.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/is_key_like.js\");\n\n\n\nasync function keyToJWK(key) {\n    if ((0,_is_key_like_js__WEBPACK_IMPORTED_MODULE_0__.isKeyObject)(key)) {\n        if (key.type === 'secret') {\n            key = key.export();\n        }\n        else {\n            return key.export({ format: 'jwk' });\n        }\n    }\n    if (key instanceof Uint8Array) {\n        return {\n            kty: 'oct',\n            k: (0,_util_base64url_js__WEBPACK_IMPORTED_MODULE_1__.encode)(key),\n        };\n    }\n    if (!(0,_is_key_like_js__WEBPACK_IMPORTED_MODULE_0__.isCryptoKey)(key)) {\n        throw new TypeError((0,_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(key, 'CryptoKey', 'KeyObject', 'Uint8Array'));\n    }\n    if (!key.extractable) {\n        throw new TypeError('non-extractable CryptoKey cannot be exported as a JWK');\n    }\n    const { ext, key_ops, alg, use, ...jwk } = await crypto.subtle.exportKey('jwk', key);\n    return jwk;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L3dlYmFwaS9saWIva2V5X3RvX2p3ay5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXFEO0FBQ0M7QUFDTTtBQUM3QztBQUNmLFFBQVEsNERBQVc7QUFDbkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQ0FBZ0MsZUFBZTtBQUMvQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSwwREFBSTtBQUNuQjtBQUNBO0FBQ0EsU0FBUyw0REFBVztBQUNwQiw0QkFBNEIsaUVBQWU7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLGlDQUFpQztBQUM3QztBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFdpbmRvd3MgMTFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcSW5ub3ZhdGl2ZSBDZW50cmVcXGNybS1zdGFmZi1zZXJ2aWNlXFxub2RlX21vZHVsZXNcXGpvc2VcXGRpc3RcXHdlYmFwaVxcbGliXFxrZXlfdG9fandrLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBpbnZhbGlkS2V5SW5wdXQgZnJvbSAnLi9pbnZhbGlkX2tleV9pbnB1dC5qcyc7XG5pbXBvcnQgeyBlbmNvZGUgYXMgYjY0dSB9IGZyb20gJy4uL3V0aWwvYmFzZTY0dXJsLmpzJztcbmltcG9ydCB7IGlzQ3J5cHRvS2V5LCBpc0tleU9iamVjdCB9IGZyb20gJy4vaXNfa2V5X2xpa2UuanMnO1xuZXhwb3J0IGRlZmF1bHQgYXN5bmMgZnVuY3Rpb24ga2V5VG9KV0soa2V5KSB7XG4gICAgaWYgKGlzS2V5T2JqZWN0KGtleSkpIHtcbiAgICAgICAgaWYgKGtleS50eXBlID09PSAnc2VjcmV0Jykge1xuICAgICAgICAgICAga2V5ID0ga2V5LmV4cG9ydCgpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgcmV0dXJuIGtleS5leHBvcnQoeyBmb3JtYXQ6ICdqd2snIH0pO1xuICAgICAgICB9XG4gICAgfVxuICAgIGlmIChrZXkgaW5zdGFuY2VvZiBVaW50OEFycmF5KSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICBrdHk6ICdvY3QnLFxuICAgICAgICAgICAgazogYjY0dShrZXkpLFxuICAgICAgICB9O1xuICAgIH1cbiAgICBpZiAoIWlzQ3J5cHRvS2V5KGtleSkpIHtcbiAgICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcihpbnZhbGlkS2V5SW5wdXQoa2V5LCAnQ3J5cHRvS2V5JywgJ0tleU9iamVjdCcsICdVaW50OEFycmF5JykpO1xuICAgIH1cbiAgICBpZiAoIWtleS5leHRyYWN0YWJsZSkge1xuICAgICAgICB0aHJvdyBuZXcgVHlwZUVycm9yKCdub24tZXh0cmFjdGFibGUgQ3J5cHRvS2V5IGNhbm5vdCBiZSBleHBvcnRlZCBhcyBhIEpXSycpO1xuICAgIH1cbiAgICBjb25zdCB7IGV4dCwga2V5X29wcywgYWxnLCB1c2UsIC4uLmp3ayB9ID0gYXdhaXQgY3J5cHRvLnN1YnRsZS5leHBvcnRLZXkoJ2p3aycsIGtleSk7XG4gICAgcmV0dXJuIGp3aztcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/lib/key_to_jwk.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/webapi/lib/normalize_key.js":
/*!************************************************************!*\
  !*** ./node_modules/jose/dist/webapi/lib/normalize_key.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _is_jwk_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./is_jwk.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/is_jwk.js\");\n/* harmony import */ var _util_base64url_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../util/base64url.js */ \"(rsc)/./node_modules/jose/dist/webapi/util/base64url.js\");\n/* harmony import */ var _jwk_to_key_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./jwk_to_key.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/jwk_to_key.js\");\n/* harmony import */ var _is_key_like_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./is_key_like.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/is_key_like.js\");\n\n\n\n\nlet cache;\nconst handleJWK = async (key, jwk, alg, freeze = false) => {\n    cache ||= new WeakMap();\n    let cached = cache.get(key);\n    if (cached?.[alg]) {\n        return cached[alg];\n    }\n    const cryptoKey = await (0,_jwk_to_key_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({ ...jwk, alg });\n    if (freeze)\n        Object.freeze(key);\n    if (!cached) {\n        cache.set(key, { [alg]: cryptoKey });\n    }\n    else {\n        cached[alg] = cryptoKey;\n    }\n    return cryptoKey;\n};\nconst handleKeyObject = (keyObject, alg) => {\n    cache ||= new WeakMap();\n    let cached = cache.get(keyObject);\n    if (cached?.[alg]) {\n        return cached[alg];\n    }\n    const isPublic = keyObject.type === 'public';\n    const extractable = isPublic ? true : false;\n    let cryptoKey;\n    if (keyObject.asymmetricKeyType === 'x25519') {\n        switch (alg) {\n            case 'ECDH-ES':\n            case 'ECDH-ES+A128KW':\n            case 'ECDH-ES+A192KW':\n            case 'ECDH-ES+A256KW':\n                break;\n            default:\n                throw new TypeError('given KeyObject instance cannot be used for this algorithm');\n        }\n        cryptoKey = keyObject.toCryptoKey(keyObject.asymmetricKeyType, extractable, isPublic ? [] : ['deriveBits']);\n    }\n    if (keyObject.asymmetricKeyType === 'ed25519') {\n        if (alg !== 'EdDSA' && alg !== 'Ed25519') {\n            throw new TypeError('given KeyObject instance cannot be used for this algorithm');\n        }\n        cryptoKey = keyObject.toCryptoKey(keyObject.asymmetricKeyType, extractable, [\n            isPublic ? 'verify' : 'sign',\n        ]);\n    }\n    if (keyObject.asymmetricKeyType === 'rsa') {\n        let hash;\n        switch (alg) {\n            case 'RSA-OAEP':\n                hash = 'SHA-1';\n                break;\n            case 'RS256':\n            case 'PS256':\n            case 'RSA-OAEP-256':\n                hash = 'SHA-256';\n                break;\n            case 'RS384':\n            case 'PS384':\n            case 'RSA-OAEP-384':\n                hash = 'SHA-384';\n                break;\n            case 'RS512':\n            case 'PS512':\n            case 'RSA-OAEP-512':\n                hash = 'SHA-512';\n                break;\n            default:\n                throw new TypeError('given KeyObject instance cannot be used for this algorithm');\n        }\n        if (alg.startsWith('RSA-OAEP')) {\n            return keyObject.toCryptoKey({\n                name: 'RSA-OAEP',\n                hash,\n            }, extractable, isPublic ? ['encrypt'] : ['decrypt']);\n        }\n        cryptoKey = keyObject.toCryptoKey({\n            name: alg.startsWith('PS') ? 'RSA-PSS' : 'RSASSA-PKCS1-v1_5',\n            hash,\n        }, extractable, [isPublic ? 'verify' : 'sign']);\n    }\n    if (keyObject.asymmetricKeyType === 'ec') {\n        const nist = new Map([\n            ['prime256v1', 'P-256'],\n            ['secp384r1', 'P-384'],\n            ['secp521r1', 'P-521'],\n        ]);\n        const namedCurve = nist.get(keyObject.asymmetricKeyDetails?.namedCurve);\n        if (!namedCurve) {\n            throw new TypeError('given KeyObject instance cannot be used for this algorithm');\n        }\n        if (alg === 'ES256' && namedCurve === 'P-256') {\n            cryptoKey = keyObject.toCryptoKey({\n                name: 'ECDSA',\n                namedCurve,\n            }, extractable, [isPublic ? 'verify' : 'sign']);\n        }\n        if (alg === 'ES384' && namedCurve === 'P-384') {\n            cryptoKey = keyObject.toCryptoKey({\n                name: 'ECDSA',\n                namedCurve,\n            }, extractable, [isPublic ? 'verify' : 'sign']);\n        }\n        if (alg === 'ES512' && namedCurve === 'P-521') {\n            cryptoKey = keyObject.toCryptoKey({\n                name: 'ECDSA',\n                namedCurve,\n            }, extractable, [isPublic ? 'verify' : 'sign']);\n        }\n        if (alg.startsWith('ECDH-ES')) {\n            cryptoKey = keyObject.toCryptoKey({\n                name: 'ECDH',\n                namedCurve,\n            }, extractable, isPublic ? [] : ['deriveBits']);\n        }\n    }\n    if (!cryptoKey) {\n        throw new TypeError('given KeyObject instance cannot be used for this algorithm');\n    }\n    if (!cached) {\n        cache.set(keyObject, { [alg]: cryptoKey });\n    }\n    else {\n        cached[alg] = cryptoKey;\n    }\n    return cryptoKey;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (key, alg) => {\n    if (key instanceof Uint8Array) {\n        return key;\n    }\n    if ((0,_is_key_like_js__WEBPACK_IMPORTED_MODULE_1__.isCryptoKey)(key)) {\n        return key;\n    }\n    if ((0,_is_key_like_js__WEBPACK_IMPORTED_MODULE_1__.isKeyObject)(key)) {\n        if (key.type === 'secret') {\n            return key.export();\n        }\n        if ('toCryptoKey' in key && typeof key.toCryptoKey === 'function') {\n            try {\n                return handleKeyObject(key, alg);\n            }\n            catch (err) {\n                if (err instanceof TypeError) {\n                    throw err;\n                }\n            }\n        }\n        let jwk = key.export({ format: 'jwk' });\n        return handleJWK(key, jwk, alg);\n    }\n    if ((0,_is_jwk_js__WEBPACK_IMPORTED_MODULE_2__.isJWK)(key)) {\n        if (key.k) {\n            return (0,_util_base64url_js__WEBPACK_IMPORTED_MODULE_3__.decode)(key.k);\n        }\n        return handleJWK(key, key, alg, true);\n    }\n    throw new Error('unreachable');\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/lib/normalize_key.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/webapi/lib/pbes2kw.js":
/*!******************************************************!*\
  !*** ./node_modules/jose/dist/webapi/lib/pbes2kw.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   unwrap: () => (/* binding */ unwrap),\n/* harmony export */   wrap: () => (/* binding */ wrap)\n/* harmony export */ });\n/* harmony import */ var _util_base64url_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../util/base64url.js */ \"(rsc)/./node_modules/jose/dist/webapi/util/base64url.js\");\n/* harmony import */ var _aeskw_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./aeskw.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/aeskw.js\");\n/* harmony import */ var _crypto_key_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./crypto_key.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/crypto_key.js\");\n/* harmony import */ var _buffer_utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./buffer_utils.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/buffer_utils.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/webapi/util/errors.js\");\n\n\n\n\n\nfunction getCryptoKey(key, alg) {\n    if (key instanceof Uint8Array) {\n        return crypto.subtle.importKey('raw', key, 'PBKDF2', false, ['deriveBits']);\n    }\n    (0,_crypto_key_js__WEBPACK_IMPORTED_MODULE_0__.checkEncCryptoKey)(key, alg, 'deriveBits');\n    return key;\n}\nconst concatSalt = (alg, p2sInput) => (0,_buffer_utils_js__WEBPACK_IMPORTED_MODULE_1__.concat)(_buffer_utils_js__WEBPACK_IMPORTED_MODULE_1__.encoder.encode(alg), new Uint8Array([0]), p2sInput);\nasync function deriveKey(p2s, alg, p2c, key) {\n    if (!(p2s instanceof Uint8Array) || p2s.length < 8) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid('PBES2 Salt Input must be 8 or more octets');\n    }\n    const salt = concatSalt(alg, p2s);\n    const keylen = parseInt(alg.slice(13, 16), 10);\n    const subtleAlg = {\n        hash: `SHA-${alg.slice(8, 11)}`,\n        iterations: p2c,\n        name: 'PBKDF2',\n        salt,\n    };\n    const cryptoKey = await getCryptoKey(key, alg);\n    return new Uint8Array(await crypto.subtle.deriveBits(subtleAlg, cryptoKey, keylen));\n}\nasync function wrap(alg, key, cek, p2c = 2048, p2s = crypto.getRandomValues(new Uint8Array(16))) {\n    const derived = await deriveKey(p2s, alg, p2c, key);\n    const encryptedKey = await _aeskw_js__WEBPACK_IMPORTED_MODULE_3__.wrap(alg.slice(-6), derived, cek);\n    return { encryptedKey, p2c, p2s: (0,_util_base64url_js__WEBPACK_IMPORTED_MODULE_4__.encode)(p2s) };\n}\nasync function unwrap(alg, key, encryptedKey, p2c, p2s) {\n    const derived = await deriveKey(p2s, alg, p2c, key);\n    return _aeskw_js__WEBPACK_IMPORTED_MODULE_3__.unwrap(alg.slice(-6), derived, encryptedKey);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/lib/pbes2kw.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/webapi/lib/private_symbols.js":
/*!**************************************************************!*\
  !*** ./node_modules/jose/dist/webapi/lib/private_symbols.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   unprotected: () => (/* binding */ unprotected)\n/* harmony export */ });\nconst unprotected = Symbol();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L3dlYmFwaS9saWIvcHJpdmF0ZV9zeW1ib2xzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxXaW5kb3dzIDExXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXElubm92YXRpdmUgQ2VudHJlXFxjcm0tc3RhZmYtc2VydmljZVxcbm9kZV9tb2R1bGVzXFxqb3NlXFxkaXN0XFx3ZWJhcGlcXGxpYlxccHJpdmF0ZV9zeW1ib2xzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCB1bnByb3RlY3RlZCA9IFN5bWJvbCgpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/lib/private_symbols.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/webapi/lib/rsaes.js":
/*!****************************************************!*\
  !*** ./node_modules/jose/dist/webapi/lib/rsaes.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decrypt: () => (/* binding */ decrypt),\n/* harmony export */   encrypt: () => (/* binding */ encrypt)\n/* harmony export */ });\n/* harmony import */ var _crypto_key_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./crypto_key.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/crypto_key.js\");\n/* harmony import */ var _check_key_length_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./check_key_length.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/check_key_length.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/webapi/util/errors.js\");\n\n\n\nconst subtleAlgorithm = (alg) => {\n    switch (alg) {\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512':\n            return 'RSA-OAEP';\n        default:\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JOSENotSupported(`alg ${alg} is not supported either by JOSE or your javascript runtime`);\n    }\n};\nasync function encrypt(alg, key, cek) {\n    (0,_crypto_key_js__WEBPACK_IMPORTED_MODULE_1__.checkEncCryptoKey)(key, alg, 'encrypt');\n    (0,_check_key_length_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(alg, key);\n    return new Uint8Array(await crypto.subtle.encrypt(subtleAlgorithm(alg), key, cek));\n}\nasync function decrypt(alg, key, encryptedKey) {\n    (0,_crypto_key_js__WEBPACK_IMPORTED_MODULE_1__.checkEncCryptoKey)(key, alg, 'decrypt');\n    (0,_check_key_length_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(alg, key);\n    return new Uint8Array(await crypto.subtle.decrypt(subtleAlgorithm(alg), key, encryptedKey));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L3dlYmFwaS9saWIvcnNhZXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBb0Q7QUFDRDtBQUNFO0FBQ3JEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsNkRBQWdCLFFBQVEsS0FBSztBQUNuRDtBQUNBO0FBQ087QUFDUCxJQUFJLGlFQUFpQjtBQUNyQixJQUFJLGdFQUFjO0FBQ2xCO0FBQ0E7QUFDTztBQUNQLElBQUksaUVBQWlCO0FBQ3JCLElBQUksZ0VBQWM7QUFDbEI7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxXaW5kb3dzIDExXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXElubm92YXRpdmUgQ2VudHJlXFxjcm0tc3RhZmYtc2VydmljZVxcbm9kZV9tb2R1bGVzXFxqb3NlXFxkaXN0XFx3ZWJhcGlcXGxpYlxccnNhZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY2hlY2tFbmNDcnlwdG9LZXkgfSBmcm9tICcuL2NyeXB0b19rZXkuanMnO1xuaW1wb3J0IGNoZWNrS2V5TGVuZ3RoIGZyb20gJy4vY2hlY2tfa2V5X2xlbmd0aC5qcyc7XG5pbXBvcnQgeyBKT1NFTm90U3VwcG9ydGVkIH0gZnJvbSAnLi4vdXRpbC9lcnJvcnMuanMnO1xuY29uc3Qgc3VidGxlQWxnb3JpdGhtID0gKGFsZykgPT4ge1xuICAgIHN3aXRjaCAoYWxnKSB7XG4gICAgICAgIGNhc2UgJ1JTQS1PQUVQJzpcbiAgICAgICAgY2FzZSAnUlNBLU9BRVAtMjU2JzpcbiAgICAgICAgY2FzZSAnUlNBLU9BRVAtMzg0JzpcbiAgICAgICAgY2FzZSAnUlNBLU9BRVAtNTEyJzpcbiAgICAgICAgICAgIHJldHVybiAnUlNBLU9BRVAnO1xuICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgdGhyb3cgbmV3IEpPU0VOb3RTdXBwb3J0ZWQoYGFsZyAke2FsZ30gaXMgbm90IHN1cHBvcnRlZCBlaXRoZXIgYnkgSk9TRSBvciB5b3VyIGphdmFzY3JpcHQgcnVudGltZWApO1xuICAgIH1cbn07XG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZW5jcnlwdChhbGcsIGtleSwgY2VrKSB7XG4gICAgY2hlY2tFbmNDcnlwdG9LZXkoa2V5LCBhbGcsICdlbmNyeXB0Jyk7XG4gICAgY2hlY2tLZXlMZW5ndGgoYWxnLCBrZXkpO1xuICAgIHJldHVybiBuZXcgVWludDhBcnJheShhd2FpdCBjcnlwdG8uc3VidGxlLmVuY3J5cHQoc3VidGxlQWxnb3JpdGhtKGFsZyksIGtleSwgY2VrKSk7XG59XG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZGVjcnlwdChhbGcsIGtleSwgZW5jcnlwdGVkS2V5KSB7XG4gICAgY2hlY2tFbmNDcnlwdG9LZXkoa2V5LCBhbGcsICdkZWNyeXB0Jyk7XG4gICAgY2hlY2tLZXlMZW5ndGgoYWxnLCBrZXkpO1xuICAgIHJldHVybiBuZXcgVWludDhBcnJheShhd2FpdCBjcnlwdG8uc3VidGxlLmRlY3J5cHQoc3VidGxlQWxnb3JpdGhtKGFsZyksIGtleSwgZW5jcnlwdGVkS2V5KSk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/lib/rsaes.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/webapi/lib/secs.js":
/*!***************************************************!*\
  !*** ./node_modules/jose/dist/webapi/lib/secs.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst minute = 60;\nconst hour = minute * 60;\nconst day = hour * 24;\nconst week = day * 7;\nconst year = day * 365.25;\nconst REGEX = /^(\\+|\\-)? ?(\\d+|\\d+\\.\\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((str) => {\n    const matched = REGEX.exec(str);\n    if (!matched || (matched[4] && matched[1])) {\n        throw new TypeError('Invalid time period format');\n    }\n    const value = parseFloat(matched[2]);\n    const unit = matched[3].toLowerCase();\n    let numericDate;\n    switch (unit) {\n        case 'sec':\n        case 'secs':\n        case 'second':\n        case 'seconds':\n        case 's':\n            numericDate = Math.round(value);\n            break;\n        case 'minute':\n        case 'minutes':\n        case 'min':\n        case 'mins':\n        case 'm':\n            numericDate = Math.round(value * minute);\n            break;\n        case 'hour':\n        case 'hours':\n        case 'hr':\n        case 'hrs':\n        case 'h':\n            numericDate = Math.round(value * hour);\n            break;\n        case 'day':\n        case 'days':\n        case 'd':\n            numericDate = Math.round(value * day);\n            break;\n        case 'week':\n        case 'weeks':\n        case 'w':\n            numericDate = Math.round(value * week);\n            break;\n        default:\n            numericDate = Math.round(value * year);\n            break;\n    }\n    if (matched[1] === '-' || matched[4] === 'ago') {\n        return -numericDate;\n    }\n    return numericDate;\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/lib/secs.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/webapi/lib/validate_algorithms.js":
/*!******************************************************************!*\
  !*** ./node_modules/jose/dist/webapi/lib/validate_algorithms.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((option, algorithms) => {\n    if (algorithms !== undefined &&\n        (!Array.isArray(algorithms) || algorithms.some((s) => typeof s !== 'string'))) {\n        throw new TypeError(`\"${option}\" option must be an array of strings`);\n    }\n    if (!algorithms) {\n        return undefined;\n    }\n    return new Set(algorithms);\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L3dlYmFwaS9saWIvdmFsaWRhdGVfYWxnb3JpdGhtcy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWU7QUFDZjtBQUNBO0FBQ0EsZ0NBQWdDLE9BQU87QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxXaW5kb3dzIDExXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXElubm92YXRpdmUgQ2VudHJlXFxjcm0tc3RhZmYtc2VydmljZVxcbm9kZV9tb2R1bGVzXFxqb3NlXFxkaXN0XFx3ZWJhcGlcXGxpYlxcdmFsaWRhdGVfYWxnb3JpdGhtcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCAob3B0aW9uLCBhbGdvcml0aG1zKSA9PiB7XG4gICAgaWYgKGFsZ29yaXRobXMgIT09IHVuZGVmaW5lZCAmJlxuICAgICAgICAoIUFycmF5LmlzQXJyYXkoYWxnb3JpdGhtcykgfHwgYWxnb3JpdGhtcy5zb21lKChzKSA9PiB0eXBlb2YgcyAhPT0gJ3N0cmluZycpKSkge1xuICAgICAgICB0aHJvdyBuZXcgVHlwZUVycm9yKGBcIiR7b3B0aW9ufVwiIG9wdGlvbiBtdXN0IGJlIGFuIGFycmF5IG9mIHN0cmluZ3NgKTtcbiAgICB9XG4gICAgaWYgKCFhbGdvcml0aG1zKSB7XG4gICAgICAgIHJldHVybiB1bmRlZmluZWQ7XG4gICAgfVxuICAgIHJldHVybiBuZXcgU2V0KGFsZ29yaXRobXMpO1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/lib/validate_algorithms.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/webapi/lib/validate_crit.js":
/*!************************************************************!*\
  !*** ./node_modules/jose/dist/webapi/lib/validate_crit.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/webapi/util/errors.js\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((Err, recognizedDefault, recognizedOption, protectedHeader, joseHeader) => {\n    if (joseHeader.crit !== undefined && protectedHeader?.crit === undefined) {\n        throw new Err('\"crit\" (Critical) Header Parameter MUST be integrity protected');\n    }\n    if (!protectedHeader || protectedHeader.crit === undefined) {\n        return new Set();\n    }\n    if (!Array.isArray(protectedHeader.crit) ||\n        protectedHeader.crit.length === 0 ||\n        protectedHeader.crit.some((input) => typeof input !== 'string' || input.length === 0)) {\n        throw new Err('\"crit\" (Critical) Header Parameter MUST be an array of non-empty strings when present');\n    }\n    let recognized;\n    if (recognizedOption !== undefined) {\n        recognized = new Map([...Object.entries(recognizedOption), ...recognizedDefault.entries()]);\n    }\n    else {\n        recognized = recognizedDefault;\n    }\n    for (const parameter of protectedHeader.crit) {\n        if (!recognized.has(parameter)) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JOSENotSupported(`Extension Header Parameter \"${parameter}\" is not recognized`);\n        }\n        if (joseHeader[parameter] === undefined) {\n            throw new Err(`Extension Header Parameter \"${parameter}\" is missing`);\n        }\n        if (recognized.get(parameter) && protectedHeader[parameter] === undefined) {\n            throw new Err(`Extension Header Parameter \"${parameter}\" MUST be integrity protected`);\n        }\n    }\n    return new Set(protectedHeader.crit);\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/lib/validate_crit.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/webapi/util/base64url.js":
/*!*********************************************************!*\
  !*** ./node_modules/jose/dist/webapi/util/base64url.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decode: () => (/* binding */ decode),\n/* harmony export */   encode: () => (/* binding */ encode)\n/* harmony export */ });\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib/buffer_utils.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/buffer_utils.js\");\n/* harmony import */ var _lib_base64_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/base64.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/base64.js\");\n\n\nfunction decode(input) {\n    if (Uint8Array.fromBase64) {\n        return Uint8Array.fromBase64(typeof input === 'string' ? input : _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_0__.decoder.decode(input), {\n            alphabet: 'base64url',\n        });\n    }\n    let encoded = input;\n    if (encoded instanceof Uint8Array) {\n        encoded = _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_0__.decoder.decode(encoded);\n    }\n    encoded = encoded.replace(/-/g, '+').replace(/_/g, '/').replace(/\\s/g, '');\n    try {\n        return (0,_lib_base64_js__WEBPACK_IMPORTED_MODULE_1__.decodeBase64)(encoded);\n    }\n    catch {\n        throw new TypeError('The input to be decoded is not correctly encoded.');\n    }\n}\nfunction encode(input) {\n    let unencoded = input;\n    if (typeof unencoded === 'string') {\n        unencoded = _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_0__.encoder.encode(unencoded);\n    }\n    if (Uint8Array.prototype.toBase64) {\n        return unencoded.toBase64({ alphabet: 'base64url', omitPadding: true });\n    }\n    return (0,_lib_base64_js__WEBPACK_IMPORTED_MODULE_1__.encodeBase64)(unencoded).replace(/=/g, '').replace(/\\+/g, '-').replace(/\\//g, '_');\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L3dlYmFwaS91dGlsL2Jhc2U2NHVybC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQTBEO0FBQ0k7QUFDdkQ7QUFDUDtBQUNBLHlFQUF5RSx5REFBTztBQUNoRjtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQSxrQkFBa0IseURBQU87QUFDekI7QUFDQTtBQUNBO0FBQ0EsZUFBZSw0REFBWTtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0Esb0JBQW9CLHlEQUFPO0FBQzNCO0FBQ0E7QUFDQSxvQ0FBb0MsMENBQTBDO0FBQzlFO0FBQ0EsV0FBVyw0REFBWTtBQUN2QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxXaW5kb3dzIDExXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXElubm92YXRpdmUgQ2VudHJlXFxjcm0tc3RhZmYtc2VydmljZVxcbm9kZV9tb2R1bGVzXFxqb3NlXFxkaXN0XFx3ZWJhcGlcXHV0aWxcXGJhc2U2NHVybC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBlbmNvZGVyLCBkZWNvZGVyIH0gZnJvbSAnLi4vbGliL2J1ZmZlcl91dGlscy5qcyc7XG5pbXBvcnQgeyBlbmNvZGVCYXNlNjQsIGRlY29kZUJhc2U2NCB9IGZyb20gJy4uL2xpYi9iYXNlNjQuanMnO1xuZXhwb3J0IGZ1bmN0aW9uIGRlY29kZShpbnB1dCkge1xuICAgIGlmIChVaW50OEFycmF5LmZyb21CYXNlNjQpIHtcbiAgICAgICAgcmV0dXJuIFVpbnQ4QXJyYXkuZnJvbUJhc2U2NCh0eXBlb2YgaW5wdXQgPT09ICdzdHJpbmcnID8gaW5wdXQgOiBkZWNvZGVyLmRlY29kZShpbnB1dCksIHtcbiAgICAgICAgICAgIGFscGhhYmV0OiAnYmFzZTY0dXJsJyxcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIGxldCBlbmNvZGVkID0gaW5wdXQ7XG4gICAgaWYgKGVuY29kZWQgaW5zdGFuY2VvZiBVaW50OEFycmF5KSB7XG4gICAgICAgIGVuY29kZWQgPSBkZWNvZGVyLmRlY29kZShlbmNvZGVkKTtcbiAgICB9XG4gICAgZW5jb2RlZCA9IGVuY29kZWQucmVwbGFjZSgvLS9nLCAnKycpLnJlcGxhY2UoL18vZywgJy8nKS5yZXBsYWNlKC9cXHMvZywgJycpO1xuICAgIHRyeSB7XG4gICAgICAgIHJldHVybiBkZWNvZGVCYXNlNjQoZW5jb2RlZCk7XG4gICAgfVxuICAgIGNhdGNoIHtcbiAgICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcignVGhlIGlucHV0IHRvIGJlIGRlY29kZWQgaXMgbm90IGNvcnJlY3RseSBlbmNvZGVkLicpO1xuICAgIH1cbn1cbmV4cG9ydCBmdW5jdGlvbiBlbmNvZGUoaW5wdXQpIHtcbiAgICBsZXQgdW5lbmNvZGVkID0gaW5wdXQ7XG4gICAgaWYgKHR5cGVvZiB1bmVuY29kZWQgPT09ICdzdHJpbmcnKSB7XG4gICAgICAgIHVuZW5jb2RlZCA9IGVuY29kZXIuZW5jb2RlKHVuZW5jb2RlZCk7XG4gICAgfVxuICAgIGlmIChVaW50OEFycmF5LnByb3RvdHlwZS50b0Jhc2U2NCkge1xuICAgICAgICByZXR1cm4gdW5lbmNvZGVkLnRvQmFzZTY0KHsgYWxwaGFiZXQ6ICdiYXNlNjR1cmwnLCBvbWl0UGFkZGluZzogdHJ1ZSB9KTtcbiAgICB9XG4gICAgcmV0dXJuIGVuY29kZUJhc2U2NCh1bmVuY29kZWQpLnJlcGxhY2UoLz0vZywgJycpLnJlcGxhY2UoL1xcKy9nLCAnLScpLnJlcGxhY2UoL1xcLy9nLCAnXycpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/util/base64url.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/webapi/util/decode_jwt.js":
/*!**********************************************************!*\
  !*** ./node_modules/jose/dist/webapi/util/decode_jwt.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decodeJwt: () => (/* binding */ decodeJwt)\n/* harmony export */ });\n/* harmony import */ var _base64url_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./base64url.js */ \"(rsc)/./node_modules/jose/dist/webapi/util/base64url.js\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/buffer_utils.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/buffer_utils.js\");\n/* harmony import */ var _lib_is_object_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/is_object.js */ \"(rsc)/./node_modules/jose/dist/webapi/lib/is_object.js\");\n/* harmony import */ var _errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./errors.js */ \"(rsc)/./node_modules/jose/dist/webapi/util/errors.js\");\n\n\n\n\nfunction decodeJwt(jwt) {\n    if (typeof jwt !== 'string')\n        throw new _errors_js__WEBPACK_IMPORTED_MODULE_0__.JWTInvalid('JWTs must use Compact JWS serialization, JWT must be a string');\n    const { 1: payload, length } = jwt.split('.');\n    if (length === 5)\n        throw new _errors_js__WEBPACK_IMPORTED_MODULE_0__.JWTInvalid('Only JWTs using Compact JWS serialization can be decoded');\n    if (length !== 3)\n        throw new _errors_js__WEBPACK_IMPORTED_MODULE_0__.JWTInvalid('Invalid JWT');\n    if (!payload)\n        throw new _errors_js__WEBPACK_IMPORTED_MODULE_0__.JWTInvalid('JWTs must contain a payload');\n    let decoded;\n    try {\n        decoded = (0,_base64url_js__WEBPACK_IMPORTED_MODULE_1__.decode)(payload);\n    }\n    catch {\n        throw new _errors_js__WEBPACK_IMPORTED_MODULE_0__.JWTInvalid('Failed to base64url decode the payload');\n    }\n    let result;\n    try {\n        result = JSON.parse(_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_2__.decoder.decode(decoded));\n    }\n    catch {\n        throw new _errors_js__WEBPACK_IMPORTED_MODULE_0__.JWTInvalid('Failed to parse the decoded payload as JSON');\n    }\n    if (!(0,_lib_is_object_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(result))\n        throw new _errors_js__WEBPACK_IMPORTED_MODULE_0__.JWTInvalid('Invalid JWT Claims Set');\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/util/decode_jwt.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/webapi/util/errors.js":
/*!******************************************************!*\
  !*** ./node_modules/jose/dist/webapi/util/errors.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   JOSEAlgNotAllowed: () => (/* binding */ JOSEAlgNotAllowed),\n/* harmony export */   JOSEError: () => (/* binding */ JOSEError),\n/* harmony export */   JOSENotSupported: () => (/* binding */ JOSENotSupported),\n/* harmony export */   JWEDecryptionFailed: () => (/* binding */ JWEDecryptionFailed),\n/* harmony export */   JWEInvalid: () => (/* binding */ JWEInvalid),\n/* harmony export */   JWKInvalid: () => (/* binding */ JWKInvalid),\n/* harmony export */   JWKSInvalid: () => (/* binding */ JWKSInvalid),\n/* harmony export */   JWKSMultipleMatchingKeys: () => (/* binding */ JWKSMultipleMatchingKeys),\n/* harmony export */   JWKSNoMatchingKey: () => (/* binding */ JWKSNoMatchingKey),\n/* harmony export */   JWKSTimeout: () => (/* binding */ JWKSTimeout),\n/* harmony export */   JWSInvalid: () => (/* binding */ JWSInvalid),\n/* harmony export */   JWSSignatureVerificationFailed: () => (/* binding */ JWSSignatureVerificationFailed),\n/* harmony export */   JWTClaimValidationFailed: () => (/* binding */ JWTClaimValidationFailed),\n/* harmony export */   JWTExpired: () => (/* binding */ JWTExpired),\n/* harmony export */   JWTInvalid: () => (/* binding */ JWTInvalid)\n/* harmony export */ });\nclass JOSEError extends Error {\n    static code = 'ERR_JOSE_GENERIC';\n    code = 'ERR_JOSE_GENERIC';\n    constructor(message, options) {\n        super(message, options);\n        this.name = this.constructor.name;\n        Error.captureStackTrace?.(this, this.constructor);\n    }\n}\nclass JWTClaimValidationFailed extends JOSEError {\n    static code = 'ERR_JWT_CLAIM_VALIDATION_FAILED';\n    code = 'ERR_JWT_CLAIM_VALIDATION_FAILED';\n    claim;\n    reason;\n    payload;\n    constructor(message, payload, claim = 'unspecified', reason = 'unspecified') {\n        super(message, { cause: { claim, reason, payload } });\n        this.claim = claim;\n        this.reason = reason;\n        this.payload = payload;\n    }\n}\nclass JWTExpired extends JOSEError {\n    static code = 'ERR_JWT_EXPIRED';\n    code = 'ERR_JWT_EXPIRED';\n    claim;\n    reason;\n    payload;\n    constructor(message, payload, claim = 'unspecified', reason = 'unspecified') {\n        super(message, { cause: { claim, reason, payload } });\n        this.claim = claim;\n        this.reason = reason;\n        this.payload = payload;\n    }\n}\nclass JOSEAlgNotAllowed extends JOSEError {\n    static code = 'ERR_JOSE_ALG_NOT_ALLOWED';\n    code = 'ERR_JOSE_ALG_NOT_ALLOWED';\n}\nclass JOSENotSupported extends JOSEError {\n    static code = 'ERR_JOSE_NOT_SUPPORTED';\n    code = 'ERR_JOSE_NOT_SUPPORTED';\n}\nclass JWEDecryptionFailed extends JOSEError {\n    static code = 'ERR_JWE_DECRYPTION_FAILED';\n    code = 'ERR_JWE_DECRYPTION_FAILED';\n    constructor(message = 'decryption operation failed', options) {\n        super(message, options);\n    }\n}\nclass JWEInvalid extends JOSEError {\n    static code = 'ERR_JWE_INVALID';\n    code = 'ERR_JWE_INVALID';\n}\nclass JWSInvalid extends JOSEError {\n    static code = 'ERR_JWS_INVALID';\n    code = 'ERR_JWS_INVALID';\n}\nclass JWTInvalid extends JOSEError {\n    static code = 'ERR_JWT_INVALID';\n    code = 'ERR_JWT_INVALID';\n}\nclass JWKInvalid extends JOSEError {\n    static code = 'ERR_JWK_INVALID';\n    code = 'ERR_JWK_INVALID';\n}\nclass JWKSInvalid extends JOSEError {\n    static code = 'ERR_JWKS_INVALID';\n    code = 'ERR_JWKS_INVALID';\n}\nclass JWKSNoMatchingKey extends JOSEError {\n    static code = 'ERR_JWKS_NO_MATCHING_KEY';\n    code = 'ERR_JWKS_NO_MATCHING_KEY';\n    constructor(message = 'no applicable key found in the JSON Web Key Set', options) {\n        super(message, options);\n    }\n}\nclass JWKSMultipleMatchingKeys extends JOSEError {\n    [Symbol.asyncIterator];\n    static code = 'ERR_JWKS_MULTIPLE_MATCHING_KEYS';\n    code = 'ERR_JWKS_MULTIPLE_MATCHING_KEYS';\n    constructor(message = 'multiple matching keys found in the JSON Web Key Set', options) {\n        super(message, options);\n    }\n}\nclass JWKSTimeout extends JOSEError {\n    static code = 'ERR_JWKS_TIMEOUT';\n    code = 'ERR_JWKS_TIMEOUT';\n    constructor(message = 'request timed out', options) {\n        super(message, options);\n    }\n}\nclass JWSSignatureVerificationFailed extends JOSEError {\n    static code = 'ERR_JWS_SIGNATURE_VERIFICATION_FAILED';\n    code = 'ERR_JWS_SIGNATURE_VERIFICATION_FAILED';\n    constructor(message = 'signature verification failed', options) {\n        super(message, options);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/webapi/util/errors.js\n");

/***/ })

};
;