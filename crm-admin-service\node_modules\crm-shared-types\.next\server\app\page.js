(()=>{var e={};e.id=974,e.ids=[974],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1204:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});var s=t(7413);function n(){return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-4xl font-bold mb-4",children:"CRM Shared Types"}),(0,s.jsx)("p",{className:"text-lg text-gray-600",children:"Shared TypeScript types and interfaces for Innovative Centre CRM"}),(0,s.jsxs)("div",{className:"mt-8 space-y-2",children:[(0,s.jsx)("p",{className:"text-sm",children:"This package provides:"}),(0,s.jsxs)("ul",{className:"text-sm text-gray-500 space-y-1",children:[(0,s.jsx)("li",{children:"• User types and roles"}),(0,s.jsx)("li",{children:"• Database entity interfaces"}),(0,s.jsx)("li",{children:"• API request/response types"}),(0,s.jsx)("li",{children:"• Shared enums and constants"})]})]})]})})}},1838:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>c,pages:()=>p,routeModule:()=>m,tree:()=>l});var s=t(5239),n=t(8088),i=t(8170),o=t.n(i),a=t(893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);t.d(r,d);let l=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1204)),"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"]}],p=["C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\app\\page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},3020:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i,metadata:()=>n});var s=t(7413);t(5692);let n={title:"CRM Shared Types",description:"Shared TypeScript types for Innovative Centre CRM"};function i({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{children:e})})}},5692:()=>{},6172:()=>{},6487:()=>{},8003:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},8331:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))},8335:()=>{},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[825],()=>t(1838));module.exports=s})();