(()=>{var e={};e.id=318,e.ids=[318],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2909:(e,r,t)=>{"use strict";t.d(r,{Y9:()=>d,j2:()=>u});var s=t(8643),a=t(189),n=t(6467),i=t(5069),o=t(5663);let{handlers:d,auth:u,signIn:l,signOut:p}=(0,s.Ay)({adapter:(0,n.y)(i.z),session:{strategy:"jwt",maxAge:1800},pages:{signIn:"/auth/signin",error:"/auth/error"},providers:[(0,a.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"},mfaCode:{label:"MFA Code",type:"text"}},async authorize(e){if(!e?.email||!e?.password)return null;try{let r=await i.z.adminUser.findUnique({where:{email:e.email}});if(!r||!r.isActive||!await o.Ay.compare(e.password,r.passwordHash))return null;return r.mfaEnabled&&!e.mfaCode&&console.warn("MFA required but not implemented yet"),await i.z.adminUser.update({where:{id:r.id},data:{lastLogin:new Date}}),await i.z.auditLog.create({data:{userId:r.id,action:"LOGIN",resourceType:"AUTH",resourceId:r.id,newValues:{timestamp:new Date,success:!0},ipAddress:"127.0.0.1",userAgent:"Unknown"}}),{id:r.id,email:r.email,name:`${r.firstName} ${r.lastName}`,role:r.role}}catch(e){return console.error("Authentication error:",e),null}}})],callbacks:{jwt:async({token:e,user:r})=>(r&&(e.role=r.role,e.id=r.id),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.id,e.user.role=r.role),e),async authorized({auth:e,request:{nextUrl:r}}){let t=!!e?.user,s=r.pathname.startsWith("/dashboard"),a=r.pathname.startsWith("/auth");return s?!!t:!a||!t||Response.redirect(new URL("/dashboard",r))}},events:{async signOut(e){let r="token"in e&&e.token?.id?e.token.id:"session"in e&&e.session?.user?.id?e.session.user.id:null;r&&await i.z.auditLog.create({data:{userId:r,action:"LOGOUT",resourceType:"AUTH",resourceId:r,newValues:{timestamp:new Date},ipAddress:"127.0.0.1",userAgent:"Unknown"}})}}}),{GET:c,POST:m}=d},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5069:(e,r,t)=>{"use strict";t.d(r,{db:()=>n,z:()=>a});var s=t(6330);let a=globalThis.prisma??new s.PrismaClient({log:["query","error","warn"]}),n=a},5511:e=>{"use strict";e.exports=require("crypto")},6330:e=>{"use strict";e.exports=require("@prisma/client")},6487:()=>{},7813:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>y,routeModule:()=>h,serverHooks:()=>f,workAsyncStorage:()=>A,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{GET:()=>w,POST:()=>g});var a=t(6559),n=t(8088),i=t(7719),o=t(2190),d=t(2909),u=t(5069),l=t(6330),p=t(5697),c=t(5663);let m=p.z.object({email:p.z.string().email("Invalid email address"),password:p.z.string().min(8,"Password must be at least 8 characters"),role:p.z.nativeEnum(l.AdminRole),firstName:p.z.string().min(1,"First name is required"),lastName:p.z.string().min(1,"Last name is required"),phone:p.z.string().optional(),mfaEnabled:p.z.boolean().default(!1)});async function w(e){try{let r=await (0,d.j2)();if(!r?.user)return o.NextResponse.json({error:"Unauthorized"},{status:401});if("admin"!==r.user.role)return o.NextResponse.json({error:"Forbidden"},{status:403});let{searchParams:t}=new URL(e.url),s=parseInt(t.get("page")||"1"),a=parseInt(t.get("limit")||"10"),n=t.get("role"),i=t.get("isActive"),l=(s-1)*a,p={};n&&(p.role=n),null!==i&&(p.isActive="true"===i);let[c,m]=await Promise.all([u.z.adminUser.findMany({where:p,skip:l,take:a,select:{id:!0,email:!0,role:!0,firstName:!0,lastName:!0,phone:!0,mfaEnabled:!0,lastLogin:!0,isActive:!0,createdAt:!0,updatedAt:!0},orderBy:{createdAt:"desc"}}),u.z.adminUser.count({where:p})]);return await u.z.auditLog.create({data:{userId:r.user.id,action:"VIEW_ADMIN_USERS",resourceType:"ADMIN_USER",newValues:{page:s,limit:a,filters:{role:n,isActive:i}},ipAddress:"127.0.0.1",userAgent:e.headers.get("user-agent")||"Unknown"}}),o.NextResponse.json({success:!0,data:{users:c,pagination:{page:s,limit:a,total:m,pages:Math.ceil(m/a)}}})}catch(e){return console.error("Error fetching users:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}async function g(e){try{let r=await (0,d.j2)();if(!r?.user)return o.NextResponse.json({error:"Unauthorized"},{status:401});if("admin"!==r.user.role)return o.NextResponse.json({error:"Forbidden"},{status:403});let t=await e.json(),s=m.parse(t);if(await u.z.adminUser.findUnique({where:{email:s.email}}))return o.NextResponse.json({error:"Email already exists"},{status:400});let a=await c.Ay.hash(s.password,12),{password:n,...i}=s,l=await u.z.adminUser.create({data:{...i,passwordHash:a},select:{id:!0,email:!0,role:!0,firstName:!0,lastName:!0,phone:!0,mfaEnabled:!0,isActive:!0,createdAt:!0}});return await u.z.auditLog.create({data:{userId:r.user.id,action:"CREATE_ADMIN_USER",resourceType:"ADMIN_USER",resourceId:l.id,newValues:{...i,passwordHash:"[REDACTED]"},ipAddress:"127.0.0.1",userAgent:e.headers.get("user-agent")||"Unknown"}}),o.NextResponse.json({success:!0,data:l})}catch(e){if(e instanceof p.z.ZodError)return o.NextResponse.json({error:"Validation error",details:e.errors},{status:400});return console.error("Error creating user:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}p.z.object({email:p.z.string().email().optional(),role:p.z.nativeEnum(l.AdminRole).optional(),firstName:p.z.string().min(1).optional(),lastName:p.z.string().min(1).optional(),phone:p.z.string().optional(),mfaEnabled:p.z.boolean().optional(),isActive:p.z.boolean().optional()});let h=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/users/route",pathname:"/api/users",filename:"route",bundlePath:"app/api/users/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-admin-service\\src\\app\\api\\users\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:A,workUnitAsyncStorage:x,serverHooks:f}=h;function y(){return(0,i.patchFetch)({workAsyncStorage:A,workUnitAsyncStorage:x})}},8335:()=>{},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,199,828,697],()=>t(7813));module.exports=s})();