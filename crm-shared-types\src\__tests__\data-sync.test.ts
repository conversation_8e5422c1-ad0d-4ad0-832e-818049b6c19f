import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { DataSyncManager, createDataSyncManager } from '../lib/data-sync';

// Mock the service client factory
jest.mock('../lib/service-clients');

describe('DataSyncManager', () => {
  let syncManager: DataSyncManager;
  let mockServiceFactory: any;
  let mockAdminClient: any;
  let mockStaffClient: any;
  let mockStudentClient: any;

  beforeEach(() => {
    // Create mock clients
    mockAdminClient = {
      post: jest.fn().mockImplementation(async () => ({ success: true })),
    } as any;
    mockStaffClient = {
      post: jest.fn().mockImplementation(async () => ({ success: true })),
    } as any;
    mockStudentClient = {
      post: jest.fn().mockImplementation(async () => ({ success: true })),
    } as any;

    // Create mock service factory
    mockServiceFactory = {
      getAdminClient: jest.fn().mockReturnValue(mockAdminClient),
      getStaffClient: jest.fn().mockReturnValue(mockStaffClient),
      getStudentClient: jest.fn().mockReturnValue(mockStudentClient),
    } as any;

    syncManager = new DataSyncManager(mockServiceFactory);
  });

  describe('queueSync', () => {
    it('should queue sync event and process it', async () => {
      const syncEvent = {
        type: 'create' as const,
        entity: 'lead' as const,
        entityId: 'lead-1',
        data: { name: 'John Doe', email: '<EMAIL>' },
        sourceService: 'crm-staff-service',
        version: 1,
      };

      await syncManager.queueSync(syncEvent);

      // Wait for processing
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(mockAdminClient.post).toHaveBeenCalledWith(
        '/api/internal/leads/sync',
        expect.objectContaining({
          leadId: 'lead-1',
          data: syncEvent.data,
          action: 'create',
        })
      );
    });

    it('should handle multiple sync events in queue', async () => {
      const events = [
        {
          type: 'create' as const,
          entity: 'lead' as const,
          entityId: 'lead-1',
          data: { name: 'John Doe' },
          sourceService: 'crm-staff-service',
          version: 1,
        },
        {
          type: 'update' as const,
          entity: 'student' as const,
          entityId: 'student-1',
          data: { status: 'active' },
          sourceService: 'crm-admin-service',
          version: 2,
        },
      ];

      await Promise.all(events.map(event => syncManager.queueSync(event)));

      // Wait for processing
      await new Promise(resolve => setTimeout(resolve, 200));

      expect(mockAdminClient.post).toHaveBeenCalledTimes(1);
      expect(mockStaffClient.post).toHaveBeenCalledTimes(1);
      expect(mockStudentClient.post).toHaveBeenCalledTimes(1);
    });
  });

  describe('lead synchronization', () => {
    it('should sync lead to admin service for reporting', async () => {
      const syncEvent = {
        type: 'create' as const,
        entity: 'lead' as const,
        entityId: 'lead-1',
        data: { name: 'John Doe', email: '<EMAIL>' },
        sourceService: 'crm-staff-service',
        version: 1,
      };

      await syncManager.queueSync(syncEvent);
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(mockAdminClient.post).toHaveBeenCalledWith(
        '/api/internal/leads/sync',
        {
          leadId: 'lead-1',
          data: syncEvent.data,
          action: 'create',
        }
      );
    });

    it('should not sync lead to source service', async () => {
      const syncEvent = {
        type: 'create' as const,
        entity: 'lead' as const,
        entityId: 'lead-1',
        data: { name: 'John Doe' },
        sourceService: 'crm-admin-service',
        version: 1,
      };

      await syncManager.queueSync(syncEvent);
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(mockAdminClient.post).not.toHaveBeenCalled();
    });
  });

  describe('student synchronization', () => {
    it('should sync student to all other services', async () => {
      const syncEvent = {
        type: 'create' as const,
        entity: 'student' as const,
        entityId: 'student-1',
        data: { name: 'Jane Smith', email: '<EMAIL>' },
        sourceService: 'crm-staff-service',
        version: 1,
      };

      await syncManager.queueSync(syncEvent);
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(mockAdminClient.post).toHaveBeenCalledWith(
        '/api/internal/students/sync',
        {
          studentId: 'student-1',
          data: syncEvent.data,
          action: 'create',
        }
      );

      expect(mockStudentClient.post).toHaveBeenCalledWith(
        '/api/internal/students/sync',
        {
          studentId: 'student-1',
          data: syncEvent.data,
          action: 'create',
        }
      );
    });
  });

  describe('course synchronization', () => {
    it('should sync course from staff to student service', async () => {
      const syncEvent = {
        type: 'create' as const,
        entity: 'course' as const,
        entityId: 'course-1',
        data: { title: 'Advanced English', duration: 40 },
        sourceService: 'crm-staff-service',
        version: 1,
      };

      await syncManager.queueSync(syncEvent);
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(mockStudentClient.post).toHaveBeenCalledWith(
        '/api/internal/courses/sync',
        {
          courseId: 'course-1',
          data: syncEvent.data,
          action: 'create',
        }
      );
    });
  });

  describe('assignment synchronization', () => {
    it('should sync assignment from staff to student service', async () => {
      const syncEvent = {
        type: 'create' as const,
        entity: 'assignment' as const,
        entityId: 'assignment-1',
        data: { title: 'Essay Writing', dueDate: '2024-01-15' },
        sourceService: 'crm-staff-service',
        version: 1,
      };

      await syncManager.queueSync(syncEvent);
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(mockStudentClient.post).toHaveBeenCalledWith(
        '/api/assignments/sync',
        {
          assignmentId: 'assignment-1',
          data: syncEvent.data,
          action: 'create',
        }
      );
    });
  });

  describe('error handling', () => {
    it('should handle sync errors gracefully', async () => {
      mockAdminClient.post.mockRejectedValueOnce(new Error('Network error'));

      const syncEvent = {
        type: 'create' as const,
        entity: 'lead' as const,
        entityId: 'lead-1',
        data: { name: 'John Doe' },
        sourceService: 'crm-staff-service',
        version: 1,
      };

      await syncManager.queueSync(syncEvent);
      await new Promise(resolve => setTimeout(resolve, 100));

      // Should not throw error, but handle it internally
      expect(mockAdminClient.post).toHaveBeenCalled();
    });
  });

  describe('queue status', () => {
    it('should return queue status', () => {
      const status = syncManager.getQueueStatus();
      
      expect(status).toHaveProperty('queueLength');
      expect(status).toHaveProperty('isProcessing');
      expect(typeof status.queueLength).toBe('number');
      expect(typeof status.isProcessing).toBe('boolean');
    });
  });

  describe('factory function', () => {
    it('should create DataSyncManager instance', () => {
      const manager = createDataSyncManager(mockServiceFactory);
      expect(manager).toBeInstanceOf(DataSyncManager);
    });
  });
});
