"use strict";(()=>{var e={};e.id=580,e.ids=[580],e.modules={846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6599:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>S,routeModule:()=>m,serverHooks:()=>x,workAsyncStorage:()=>k,workUnitAsyncStorage:()=>E});var s={};r.r(s),r.d(s,{GET:()=>h,POST:()=>v});var o=r(6559),a=r(8088),n=r(7719),i=r(2190),c=r(8731),p=r(7582);let u=new p.ServiceClientFactory({adminServiceUrl:process.env.ADMIN_SERVICE_URL||"http://localhost:3001",staffServiceUrl:process.env.STAFF_SERVICE_URL||"http://localhost:3002",studentServiceUrl:process.env.STUDENT_SERVICE_URL||"http://localhost:3003",serviceName:"crm-admin-service",apiKey:process.env.SERVICE_API_KEY||"default-api-key"}),d=(0,p.createDataSyncManager)(u),l=(0,p.createWebhookManager)(u,d);l.registerWebhook({url:`${process.env.ADMIN_SERVICE_URL}/api/webhooks/receive`,events:["lead.created","lead.updated","student.created","student.updated","course.created","course.updated"],service:"crm-admin-service",isActive:!0,secret:process.env.WEBHOOK_SECRET});let v=(0,c.A0)(async e=>{try{let{type:t,action:r,entityId:s,data:o}=await e.json();return await l.emitEvent({type:`${t}.${r}`,source:"crm-admin-service",data:{id:s,...o}}),i.NextResponse.json({success:!0,message:"Webhook event emitted successfully"})}catch(e){return console.error("Webhook emission failed:",e),i.NextResponse.json({success:!1,error:{code:"WEBHOOK_FAILED",message:"Failed to emit webhook event"}},{status:500})}}),h=(0,c.A0)(async()=>{try{let e=l.getWebhookStats(),t=l.listSubscriptions(),r=l.listRecentDeliveries(10);return i.NextResponse.json({success:!0,data:{stats:e,subscriptions:t,recentDeliveries:r}})}catch(e){return console.error("Failed to get webhook status:",e),i.NextResponse.json({success:!1,error:{code:"WEBHOOK_STATUS_FAILED",message:"Failed to get webhook status"}},{status:500})}}),m=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/webhooks/route",pathname:"/api/webhooks",filename:"route",bundlePath:"app/api/webhooks/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-admin-service\\src\\app\\api\\webhooks\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:k,workUnitAsyncStorage:E,serverHooks:x}=m;function S(){return(0,n.patchFetch)({workAsyncStorage:k,workUnitAsyncStorage:E})}},9294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,199,399],()=>r(6599));module.exports=s})();