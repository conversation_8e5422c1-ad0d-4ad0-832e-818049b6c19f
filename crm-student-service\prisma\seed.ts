import { PrismaClient, StudentStatus, SubmissionStatus } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Seeding student database...');

  // Create default student users
  const hashedPassword = await bcrypt.hash('student123', 12);
  
  // Create student user 1
  const studentUser1 = await prisma.studentUser.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      passwordHash: hashedPassword,
      firstName: 'John',
      lastName: 'Doe',
      phone: '+1234567890',
      isActive: true,
    },
  });

  // Create student profile for user 1
  const student1 = await prisma.student.upsert({
    where: { studentUserId: studentUser1.id },
    update: {},
    create: {
      studentUserId: studentUser1.id,
      studentId: 'STU001',
      dateOfBirth: new Date('1995-05-15'),
      emergencyContact: {
        name: '<PERSON>',
        relationship: 'Mother',
        phone: '+1234567899',
        email: '<EMAIL>'
      },
      enrollmentDate: new Date('2024-01-20'),
      currentLevel: 'Beginner',
      status: StudentStatus.active,
    },
  });

  console.log('✅ Created student user 1:', studentUser1.email);

  // Create student user 2
  const studentUser2 = await prisma.studentUser.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      passwordHash: hashedPassword,
      firstName: 'Jane',
      lastName: 'Smith',
      phone: '+1234567891',
      isActive: true,
    },
  });

  // Create student profile for user 2
  const student2 = await prisma.student.upsert({
    where: { studentUserId: studentUser2.id },
    update: {},
    create: {
      studentUserId: studentUser2.id,
      studentId: 'STU002',
      dateOfBirth: new Date('1992-08-22'),
      emergencyContact: {
        name: 'Robert Smith',
        relationship: 'Father',
        phone: '+1234567898',
        email: '<EMAIL>'
      },
      enrollmentDate: new Date('2024-01-25'),
      currentLevel: 'Advanced',
      status: StudentStatus.active,
    },
  });

  console.log('✅ Created student user 2:', studentUser2.email);

  // Create student user 3
  const studentUser3 = await prisma.studentUser.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      passwordHash: hashedPassword,
      firstName: 'Bob',
      lastName: 'Wilson',
      phone: '+1234567892',
      isActive: true,
    },
  });

  // Create student profile for user 3
  const student3 = await prisma.student.upsert({
    where: { studentUserId: studentUser3.id },
    update: {},
    create: {
      studentUserId: studentUser3.id,
      studentId: 'STU003',
      dateOfBirth: new Date('1988-12-10'),
      emergencyContact: {
        name: 'Mary Wilson',
        relationship: 'Wife',
        phone: '+1234567897',
        email: '<EMAIL>'
      },
      enrollmentDate: new Date('2024-01-30'),
      currentLevel: 'Beginner',
      status: StudentStatus.active,
    },
  });

  console.log('✅ Created student user 3:', studentUser3.email);

  // Create sample student progress records
  const progressRecords = [
    {
      studentId: student1.id,
      assignmentId: 'assignment-intro-essay',
      progressPercentage: 85,
      grade: 'B+',
      feedback: 'Good work on the introduction essay. Grammar needs improvement.',
      submittedAt: new Date('2024-02-10'),
      gradedAt: new Date('2024-02-12'),
    },
    {
      studentId: student2.id,
      assignmentId: 'assignment-advanced-grammar',
      progressPercentage: 92,
      grade: 'A-',
      feedback: 'Excellent understanding of advanced grammar concepts.',
      submittedAt: new Date('2024-02-28'),
      gradedAt: new Date('2024-03-02'),
    },
    {
      studentId: student3.id,
      assignmentId: 'assignment-vocab-quiz',
      progressPercentage: 78,
      grade: 'B',
      feedback: 'Good vocabulary retention. Practice pronunciation more.',
      submittedAt: new Date('2024-02-18'),
      gradedAt: new Date('2024-02-20'),
    },
  ];

  for (const progress of progressRecords) {
    await prisma.studentProgress.create({
      data: progress,
    });
  }

  console.log('✅ Created sample progress records');

  // Create sample assignment submissions
  const submissions = [
    {
      studentId: student1.id,
      assignmentId: 'assignment-intro-essay',
      submissionContent: 'My name is John Doe and I am excited to learn English...',
      submittedAt: new Date('2024-02-10'),
      status: SubmissionStatus.graded,
      teacherFeedback: 'Good effort! Work on grammar and sentence structure.',
      grade: 'B+',
      gradedAt: new Date('2024-02-12'),
    },
    {
      studentId: student2.id,
      assignmentId: 'assignment-advanced-grammar',
      submissionContent: 'Advanced grammar exercise completed with detailed explanations...',
      submittedAt: new Date('2024-02-28'),
      status: SubmissionStatus.graded,
      teacherFeedback: 'Excellent work! Shows deep understanding.',
      grade: 'A-',
      gradedAt: new Date('2024-03-02'),
    },
    {
      studentId: student3.id,
      assignmentId: 'assignment-vocab-quiz',
      submissionContent: 'Vocabulary quiz answers: 1. Hello, 2. Goodbye, 3. Thank you...',
      submittedAt: new Date('2024-02-18'),
      status: SubmissionStatus.graded,
      teacherFeedback: 'Good vocabulary knowledge. Practice pronunciation.',
      grade: 'B',
      gradedAt: new Date('2024-02-20'),
    },
  ];

  for (const submission of submissions) {
    await prisma.assignmentSubmission.create({
      data: submission,
    });
  }

  console.log('✅ Created sample assignment submissions');

  // Create sample resource access records
  const resourceAccess = [
    {
      studentId: student1.id,
      resourceId: 'resource-grammar-basics',
      accessedAt: new Date('2024-02-05'),
      accessDuration: 45,
    },
    {
      studentId: student1.id,
      resourceId: 'resource-pronunciation-guide',
      accessedAt: new Date('2024-02-08'),
      accessDuration: 30,
    },
    {
      studentId: student2.id,
      resourceId: 'resource-advanced-grammar',
      accessedAt: new Date('2024-02-25'),
      accessDuration: 60,
    },
    {
      studentId: student3.id,
      resourceId: 'resource-grammar-basics',
      accessedAt: new Date('2024-02-15'),
      accessDuration: 35,
    },
  ];

  for (const access of resourceAccess) {
    await prisma.studentResourceAccess.create({
      data: access,
    });
  }

  console.log('✅ Created sample resource access records');

  // Create sample student schedules
  const schedules = [
    {
      studentId: student1.id,
      groupId: 'group-eng101-morning',
      courseTitle: 'English for Beginners',
      teacherName: 'Alice Teacher',
      cabinetNumber: 'A101',
      schedule: {
        days: ['Monday', 'Wednesday', 'Friday'],
        time: '09:00-11:00',
        timezone: 'UTC'
      },
      startDate: new Date('2024-02-01'),
      endDate: new Date('2024-04-30'),
      isActive: true,
    },
    {
      studentId: student2.id,
      groupId: 'group-eng201-evening',
      courseTitle: 'English Advanced',
      teacherName: 'Alice Teacher',
      cabinetNumber: 'A102',
      schedule: {
        days: ['Tuesday', 'Thursday'],
        time: '18:00-20:00',
        timezone: 'UTC'
      },
      startDate: new Date('2024-02-15'),
      endDate: new Date('2024-06-15'),
      isActive: true,
    },
    {
      studentId: student3.id,
      groupId: 'group-eng101-morning',
      courseTitle: 'English for Beginners',
      teacherName: 'Alice Teacher',
      cabinetNumber: 'A101',
      schedule: {
        days: ['Monday', 'Wednesday', 'Friday'],
        time: '09:00-11:00',
        timezone: 'UTC'
      },
      startDate: new Date('2024-02-01'),
      endDate: new Date('2024-04-30'),
      isActive: true,
    },
  ];

  for (const schedule of schedules) {
    await prisma.studentSchedule.create({
      data: schedule,
    });
  }

  console.log('✅ Created sample student schedules');

  console.log('🎉 Seeding completed successfully!');
  console.log('');
  console.log('Default users created:');
  console.log('- Student 1: <EMAIL> / student123 (STU001)');
  console.log('- Student 2: <EMAIL> / student123 (STU002)');
  console.log('- Student 3: <EMAIL> / student123 (STU003)');
  console.log('');
  console.log('Sample data created:');
  console.log('- 3 student profiles with emergency contacts');
  console.log('- 3 progress records with grades and feedback');
  console.log('- 3 assignment submissions');
  console.log('- 4 resource access tracking records');
  console.log('- 3 student schedules');
}

main()
  .catch((e) => {
    console.error('❌ Seeding failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
