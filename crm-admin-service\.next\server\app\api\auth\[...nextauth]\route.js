(()=>{var e={};e.id=14,e.ids=[14],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2909:(e,r,t)=>{"use strict";t.d(r,{Y9:()=>u,j2:()=>d});var s=t(8643),a=t(189),i=t(6467),n=t(5069),o=t(5663);let{handlers:u,auth:d,signIn:p,signOut:l}=(0,s.Ay)({adapter:(0,i.y)(n.z),session:{strategy:"jwt",maxAge:1800},pages:{signIn:"/auth/signin",error:"/auth/error"},providers:[(0,a.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"},mfaCode:{label:"MFA Code",type:"text"}},async authorize(e){if(!e?.email||!e?.password)return null;try{let r=await n.z.adminUser.findUnique({where:{email:e.email}});if(!r||!r.isActive||!await o.Ay.compare(e.password,r.passwordHash))return null;return r.mfaEnabled&&!e.mfaCode&&console.warn("MFA required but not implemented yet"),await n.z.adminUser.update({where:{id:r.id},data:{lastLogin:new Date}}),await n.z.auditLog.create({data:{userId:r.id,action:"LOGIN",resourceType:"AUTH",resourceId:r.id,newValues:{timestamp:new Date,success:!0},ipAddress:"127.0.0.1",userAgent:"Unknown"}}),{id:r.id,email:r.email,name:`${r.firstName} ${r.lastName}`,role:r.role}}catch(e){return console.error("Authentication error:",e),null}}})],callbacks:{jwt:async({token:e,user:r})=>(r&&(e.role=r.role,e.id=r.id),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.id,e.user.role=r.role),e),async authorized({auth:e,request:{nextUrl:r}}){let t=!!e?.user,s=r.pathname.startsWith("/dashboard"),a=r.pathname.startsWith("/auth");return s?!!t:!a||!t||Response.redirect(new URL("/dashboard",r))}},events:{async signOut(e){let r="token"in e&&e.token?.id?e.token.id:"session"in e&&e.session?.user?.id?e.session.user.id:null;r&&await n.z.auditLog.create({data:{userId:r,action:"LOGOUT",resourceType:"AUTH",resourceId:r,newValues:{timestamp:new Date},ipAddress:"127.0.0.1",userAgent:"Unknown"}})}}}),{GET:c,POST:m}=u},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5069:(e,r,t)=>{"use strict";t.d(r,{db:()=>i,z:()=>a});var s=t(6330);let a=globalThis.prisma??new s.PrismaClient({log:["query","error","warn"]}),i=a},5511:e=>{"use strict";e.exports=require("crypto")},5649:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>m,routeModule:()=>d,serverHooks:()=>c,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{GET:()=>o,POST:()=>u});var a=t(6559),i=t(8088),n=t(7719);let{GET:o,POST:u}=t(2909).Y9,d=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/auth/[...nextauth]/route",pathname:"/api/auth/[...nextauth]",filename:"route",bundlePath:"app/api/auth/[...nextauth]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-admin-service\\src\\app\\api\\auth\\[...nextauth]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:p,workUnitAsyncStorage:l,serverHooks:c}=d;function m(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:l})}},6330:e=>{"use strict";e.exports=require("@prisma/client")},6487:()=>{},8335:()=>{},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,199,828],()=>t(5649));module.exports=s})();