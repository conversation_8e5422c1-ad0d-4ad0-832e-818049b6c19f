{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "d9f26dc1dd23d23eeaaaf581154890f0", "previewModeSigningKey": "fcbac102632e48f3b059c7b8fbcd37373fc4367700f59c7b8a40ecdfacb20864", "previewModeEncryptionKey": "16846e2f6fa3023ecf83125aded18744a27048493aef6ede453b1c468d78df62"}}