{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "4d58ec97101f651da783e41703e2f314", "previewModeSigningKey": "d6eb4d8710d6c95ad5926424c037fdfbffeb61cc5cf52cdc9785aaef23428b13", "previewModeEncryptionKey": "9a4fec86ce08e216f94f95a21d5d14a0423c724034020d64bb6193aea47c223a"}}