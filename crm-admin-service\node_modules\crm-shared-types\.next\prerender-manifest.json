{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "5753c9758e634fb0e6766946aa693316", "previewModeSigningKey": "b2e78b7bb0db2bca4b5d1624990e4ac07c46200db43b5bf6fa4e55c6580e15c7", "previewModeEncryptionKey": "c3658eae0ae02850c51eafa5f06e27a2365acd8504f53abbdf671f838f4d6d07"}}