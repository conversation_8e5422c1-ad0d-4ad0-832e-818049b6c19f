"use strict";(()=>{var e={};e.id=306,e.ids=[306],e.modules={846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2151:(e,r,s)=>{s.r(r),s.d(r,{patchFetch:()=>R,routeModule:()=>l,serverHooks:()=>x,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>g});var t={};s.r(t),s.d(t,{GET:()=>d,POST:()=>p});var a=s(6559),n=s(8088),o=s(7719),i=s(2190),u=s(8731),c=s(5069);let d=(0,u.A0)(async e=>{try{let r=(0,u.TB)(e);if(!r)return i.NextResponse.json({success:!1,error:{code:"UNAUTHORIZED",message:"Service authentication required"}},{status:401});let{searchParams:s}=new URL(e.url),t=s.get("service"),a=parseInt(s.get("page")||"1"),n=parseInt(s.get("limit")||"20"),o=(a-1)*n,d=t?{serviceName:t}:{},[p,l]=await Promise.all([c.z.userManagement.findMany({where:d,skip:o,take:n,orderBy:{createdAt:"desc"},include:{managedByUser:{select:{firstName:!0,lastName:!0,email:!0}}}}),c.z.userManagement.count({where:d})]);return i.NextResponse.json({success:!0,data:p,meta:{page:a,limit:n,total:l,totalPages:Math.ceil(l/n),requestedBy:r}})}catch(e){return console.error("Error fetching users:",e),i.NextResponse.json({success:!1,error:{code:"FETCH_USERS_ERROR",message:"Failed to fetch users"}},{status:500})}}),p=(0,u.A0)(async e=>{try{let r=(0,u.TB)(e);if(!r)return i.NextResponse.json({success:!1,error:{code:"UNAUTHORIZED",message:"Service authentication required"}},{status:401});let{serviceName:s,serviceUserId:t,email:a,role:n,isActive:o=!0}=await e.json(),d="temp-admin-user-id",p=await c.z.userManagement.create({data:{serviceName:s,serviceUserId:t,email:a,role:n,isActive:o,managedBy:d},include:{managedByUser:{select:{firstName:!0,lastName:!0,email:!0}}}});return await c.z.auditLog.create({data:{userId:d,action:"CREATE_USER_MANAGEMENT",resourceType:"UserManagement",resourceId:p.id,newValues:p,ipAddress:e.headers.get("x-forwarded-for")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"}}),i.NextResponse.json({success:!0,data:p,meta:{createdBy:r}},{status:201})}catch(e){return console.error("Error creating user:",e),i.NextResponse.json({success:!1,error:{code:"CREATE_USER_ERROR",message:"Failed to create user"}},{status:500})}}),l=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/service/users/route",pathname:"/api/service/users",filename:"route",bundlePath:"app/api/service/users/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-admin-service\\src\\app\\api\\service\\users\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:m,workUnitAsyncStorage:g,serverHooks:x}=l;function R(){return(0,o.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:g})}},3033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5069:(e,r,s)=>{s.d(r,{db:()=>n,z:()=>a});var t=s(6330);let a=globalThis.prisma??new t.PrismaClient({log:["query","error","warn"]}),n=a},6330:e=>{e.exports=require("@prisma/client")},9294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[447,199,399],()=>s(2151));module.exports=t})();