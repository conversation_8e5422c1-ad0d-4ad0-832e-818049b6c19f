"use strict";var Bt=Object.defineProperty;var f=(s,e)=>Bt(s,"name",{value:e,configurable:!0});var Ne=require("node:url"),ne=require("esbuild"),Jt=require("node:crypto"),j=require("node:fs"),X=require("node:path"),Gt=require("node:os"),zt=require("./temporary-directory-B83uKxJF.cjs");const Ie=f(s=>Jt.createHash("sha1").update(s).digest("hex"),"sha1"),Me=44,Ht=59,Ue="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",je=new Uint8Array(64),$e=new Uint8Array(128);for(let s=0;s<Ue.length;s++){const e=Ue.charCodeAt(s);je[s]=e,$e[e]=s}const ke=typeof TextDecoder<"u"?new TextDecoder:typeof Buffer<"u"?{decode(s){return Buffer.from(s.buffer,s.byteOffset,s.byteLength).toString()}}:{decode(s){let e="";for(let r=0;r<s.length;r++)e+=String.fromCharCode(s[r]);return e}};function Xt(s){const e=new Int32Array(5),r=[];let i=0;do{const o=Kt(s,i),c=[];let u=!0,p=0;e[0]=0;for(let g=i;g<o;g++){let b;g=K(s,g,e,0);const d=e[0];d<p&&(u=!1),p=d,De(s,g,o)?(g=K(s,g,e,1),g=K(s,g,e,2),g=K(s,g,e,3),De(s,g,o)?(g=K(s,g,e,4),b=[d,e[1],e[2],e[3],e[4]]):b=[d,e[1],e[2],e[3]]):b=[d],c.push(b)}u||Yt(c),r.push(c),i=o+1}while(i<=s.length);return r}f(Xt,"decode");function Kt(s,e){const r=s.indexOf(";",e);return r===-1?s.length:r}f(Kt,"indexOf");function K(s,e,r,i){let o=0,c=0,u=0;do{const g=s.charCodeAt(e++);u=$e[g],o|=(u&31)<<c,c+=5}while(u&32);const p=o&1;return o>>>=1,p&&(o=-2147483648|-o),r[i]+=o,e}f(K,"decodeInteger");function De(s,e,r){return e>=r?!1:s.charCodeAt(e)!==Me}f(De,"hasMoreVlq");function Yt(s){s.sort(Qt)}f(Yt,"sort");function Qt(s,e){return s[0]-e[0]}f(Qt,"sortComparator$1");function Te(s){const e=new Int32Array(5),r=1024*16,i=r-36,o=new Uint8Array(r),c=o.subarray(0,i);let u=0,p="";for(let g=0;g<s.length;g++){const b=s[g];if(g>0&&(u===r&&(p+=ke.decode(o),u=0),o[u++]=Ht),b.length!==0){e[0]=0;for(let d=0;d<b.length;d++){const n=b[d];u>i&&(p+=ke.decode(c),o.copyWithin(0,i,u),u-=i),d>0&&(o[u++]=Me),u=Y(o,u,e,n,0),n.length!==1&&(u=Y(o,u,e,n,1),u=Y(o,u,e,n,2),u=Y(o,u,e,n,3),n.length!==4&&(u=Y(o,u,e,n,4)))}}}return p+ke.decode(o.subarray(0,u))}f(Te,"encode");function Y(s,e,r,i,o){const c=i[o];let u=c-r[o];r[o]=c,u=u<0?-u<<1|1:u<<1;do{let p=u&31;u>>>=5,u>0&&(p|=32),s[e++]=je[p]}while(u>0);return e}f(Y,"encodeInteger");class ce{static{f(this,"BitSet")}constructor(e){this.bits=e instanceof ce?e.bits.slice():[]}add(e){this.bits[e>>5]|=1<<(e&31)}has(e){return!!(this.bits[e>>5]&1<<(e&31))}}class ee{static{f(this,"Chunk")}constructor(e,r,i){this.start=e,this.end=r,this.original=i,this.intro="",this.outro="",this.content=i,this.storeName=!1,this.edited=!1,this.previous=null,this.next=null}appendLeft(e){this.outro+=e}appendRight(e){this.intro=this.intro+e}clone(){const e=new ee(this.start,this.end,this.original);return e.intro=this.intro,e.outro=this.outro,e.content=this.content,e.storeName=this.storeName,e.edited=this.edited,e}contains(e){return this.start<e&&e<this.end}eachNext(e){let r=this;for(;r;)e(r),r=r.next}eachPrevious(e){let r=this;for(;r;)e(r),r=r.previous}edit(e,r,i){return this.content=e,i||(this.intro="",this.outro=""),this.storeName=r,this.edited=!0,this}prependLeft(e){this.outro=e+this.outro}prependRight(e){this.intro=e+this.intro}reset(){this.intro="",this.outro="",this.edited&&(this.content=this.original,this.storeName=!1,this.edited=!1)}split(e){const r=e-this.start,i=this.original.slice(0,r),o=this.original.slice(r);this.original=i;const c=new ee(e,this.end,o);return c.outro=this.outro,this.outro="",this.end=e,this.edited?(c.edit("",!1),this.content=""):this.content=i,c.next=this.next,c.next&&(c.next.previous=c),c.previous=this,this.next=c,c}toString(){return this.intro+this.content+this.outro}trimEnd(e){if(this.outro=this.outro.replace(e,""),this.outro.length)return!0;const r=this.content.replace(e,"");if(r.length)return r!==this.content&&(this.split(this.start+r.length).edit("",void 0,!0),this.edited&&this.edit(r,this.storeName,!0)),!0;if(this.edit("",void 0,!0),this.intro=this.intro.replace(e,""),this.intro.length)return!0}trimStart(e){if(this.intro=this.intro.replace(e,""),this.intro.length)return!0;const r=this.content.replace(e,"");if(r.length){if(r!==this.content){const i=this.split(this.end-r.length);this.edited&&i.edit(r,this.storeName,!0),this.edit("",void 0,!0)}return!0}else if(this.edit("",void 0,!0),this.outro=this.outro.replace(e,""),this.outro.length)return!0}}function Zt(){return typeof globalThis<"u"&&typeof globalThis.btoa=="function"?s=>globalThis.btoa(unescape(encodeURIComponent(s))):typeof Buffer=="function"?s=>Buffer.from(s,"utf-8").toString("base64"):()=>{throw new Error("Unsupported environment: `window.btoa` or `Buffer` should be supported.")}}f(Zt,"getBtoa");const Vt=Zt();let er=class{static{f(this,"SourceMap")}constructor(e){this.version=3,this.file=e.file,this.sources=e.sources,this.sourcesContent=e.sourcesContent,this.names=e.names,this.mappings=Te(e.mappings),typeof e.x_google_ignoreList<"u"&&(this.x_google_ignoreList=e.x_google_ignoreList)}toString(){return JSON.stringify(this)}toUrl(){return"data:application/json;charset=utf-8;base64,"+Vt(this.toString())}};function tr(s){const e=s.split(`
`),r=e.filter(c=>/^\t+/.test(c)),i=e.filter(c=>/^ {2,}/.test(c));if(r.length===0&&i.length===0)return null;if(r.length>=i.length)return"	";const o=i.reduce((c,u)=>{const p=/^ +/.exec(u)[0].length;return Math.min(p,c)},1/0);return new Array(o+1).join(" ")}f(tr,"guessIndent");function rr(s,e){const r=s.split(/[/\\]/),i=e.split(/[/\\]/);for(r.pop();r[0]===i[0];)r.shift(),i.shift();if(r.length){let o=r.length;for(;o--;)r[o]=".."}return r.concat(i).join("/")}f(rr,"getRelativePath");const nr=Object.prototype.toString;function ir(s){return nr.call(s)==="[object Object]"}f(ir,"isObject");function Fe(s){const e=s.split(`
`),r=[];for(let i=0,o=0;i<e.length;i++)r.push(o),o+=e[i].length+1;return f(function(o){let c=0,u=r.length;for(;c<u;){const b=c+u>>1;o<r[b]?u=b:c=b+1}const p=c-1,g=o-r[p];return{line:p,column:g}},"locate")}f(Fe,"getLocator");const sr=/\w/;class or{static{f(this,"Mappings")}constructor(e){this.hires=e,this.generatedCodeLine=0,this.generatedCodeColumn=0,this.raw=[],this.rawSegments=this.raw[this.generatedCodeLine]=[],this.pending=null}addEdit(e,r,i,o){if(r.length){const c=r.length-1;let u=r.indexOf(`
`,0),p=-1;for(;u>=0&&c>u;){const b=[this.generatedCodeColumn,e,i.line,i.column];o>=0&&b.push(o),this.rawSegments.push(b),this.generatedCodeLine+=1,this.raw[this.generatedCodeLine]=this.rawSegments=[],this.generatedCodeColumn=0,p=u,u=r.indexOf(`
`,u+1)}const g=[this.generatedCodeColumn,e,i.line,i.column];o>=0&&g.push(o),this.rawSegments.push(g),this.advance(r.slice(p+1))}else this.pending&&(this.rawSegments.push(this.pending),this.advance(r));this.pending=null}addUneditedChunk(e,r,i,o,c){let u=r.start,p=!0,g=!1;for(;u<r.end;){if(this.hires||p||c.has(u)){const b=[this.generatedCodeColumn,e,o.line,o.column];this.hires==="boundary"?sr.test(i[u])?g||(this.rawSegments.push(b),g=!0):(this.rawSegments.push(b),g=!1):this.rawSegments.push(b)}i[u]===`
`?(o.line+=1,o.column=0,this.generatedCodeLine+=1,this.raw[this.generatedCodeLine]=this.rawSegments=[],this.generatedCodeColumn=0,p=!0):(o.column+=1,this.generatedCodeColumn+=1,p=!1),u+=1}this.pending=null}advance(e){if(!e)return;const r=e.split(`
`);if(r.length>1){for(let i=0;i<r.length-1;i++)this.generatedCodeLine++,this.raw[this.generatedCodeLine]=this.rawSegments=[];this.generatedCodeColumn=0}this.generatedCodeColumn+=r[r.length-1].length}}const Q=`
`,B={insertLeft:!1,insertRight:!1,storeName:!1};class Ee{static{f(this,"MagicString")}constructor(e,r={}){const i=new ee(0,e.length,e);Object.defineProperties(this,{original:{writable:!0,value:e},outro:{writable:!0,value:""},intro:{writable:!0,value:""},firstChunk:{writable:!0,value:i},lastChunk:{writable:!0,value:i},lastSearchedChunk:{writable:!0,value:i},byStart:{writable:!0,value:{}},byEnd:{writable:!0,value:{}},filename:{writable:!0,value:r.filename},indentExclusionRanges:{writable:!0,value:r.indentExclusionRanges},sourcemapLocations:{writable:!0,value:new ce},storedNames:{writable:!0,value:{}},indentStr:{writable:!0,value:void 0},ignoreList:{writable:!0,value:r.ignoreList}}),this.byStart[0]=i,this.byEnd[e.length]=i}addSourcemapLocation(e){this.sourcemapLocations.add(e)}append(e){if(typeof e!="string")throw new TypeError("outro content must be a string");return this.outro+=e,this}appendLeft(e,r){if(typeof r!="string")throw new TypeError("inserted content must be a string");this._split(e);const i=this.byEnd[e];return i?i.appendLeft(r):this.intro+=r,this}appendRight(e,r){if(typeof r!="string")throw new TypeError("inserted content must be a string");this._split(e);const i=this.byStart[e];return i?i.appendRight(r):this.outro+=r,this}clone(){const e=new Ee(this.original,{filename:this.filename});let r=this.firstChunk,i=e.firstChunk=e.lastSearchedChunk=r.clone();for(;r;){e.byStart[i.start]=i,e.byEnd[i.end]=i;const o=r.next,c=o&&o.clone();c&&(i.next=c,c.previous=i,i=c),r=o}return e.lastChunk=i,this.indentExclusionRanges&&(e.indentExclusionRanges=this.indentExclusionRanges.slice()),e.sourcemapLocations=new ce(this.sourcemapLocations),e.intro=this.intro,e.outro=this.outro,e}generateDecodedMap(e){e=e||{};const r=0,i=Object.keys(this.storedNames),o=new or(e.hires),c=Fe(this.original);return this.intro&&o.advance(this.intro),this.firstChunk.eachNext(u=>{const p=c(u.start);u.intro.length&&o.advance(u.intro),u.edited?o.addEdit(r,u.content,p,u.storeName?i.indexOf(u.original):-1):o.addUneditedChunk(r,u,this.original,p,this.sourcemapLocations),u.outro.length&&o.advance(u.outro)}),{file:e.file?e.file.split(/[/\\]/).pop():void 0,sources:[e.source?rr(e.file||"",e.source):e.file||""],sourcesContent:e.includeContent?[this.original]:void 0,names:i,mappings:o.raw,x_google_ignoreList:this.ignoreList?[r]:void 0}}generateMap(e){return new er(this.generateDecodedMap(e))}_ensureindentStr(){this.indentStr===void 0&&(this.indentStr=tr(this.original))}_getRawIndentString(){return this._ensureindentStr(),this.indentStr}getIndentString(){return this._ensureindentStr(),this.indentStr===null?"	":this.indentStr}indent(e,r){const i=/^[^\r\n]/gm;if(ir(e)&&(r=e,e=void 0),e===void 0&&(this._ensureindentStr(),e=this.indentStr||"	"),e==="")return this;r=r||{};const o={};r.exclude&&(typeof r.exclude[0]=="number"?[r.exclude]:r.exclude).forEach(d=>{for(let n=d[0];n<d[1];n+=1)o[n]=!0});let c=r.indentStart!==!1;const u=f(b=>c?`${e}${b}`:(c=!0,b),"replacer");this.intro=this.intro.replace(i,u);let p=0,g=this.firstChunk;for(;g;){const b=g.end;if(g.edited)o[p]||(g.content=g.content.replace(i,u),g.content.length&&(c=g.content[g.content.length-1]===`
`));else for(p=g.start;p<b;){if(!o[p]){const d=this.original[p];d===`
`?c=!0:d!=="\r"&&c&&(c=!1,p===g.start||(this._splitChunk(g,p),g=g.next),g.prependRight(e))}p+=1}p=g.end,g=g.next}return this.outro=this.outro.replace(i,u),this}insert(){throw new Error("magicString.insert(...) is deprecated. Use prependRight(...) or appendLeft(...)")}insertLeft(e,r){return B.insertLeft||(console.warn("magicString.insertLeft(...) is deprecated. Use magicString.appendLeft(...) instead"),B.insertLeft=!0),this.appendLeft(e,r)}insertRight(e,r){return B.insertRight||(console.warn("magicString.insertRight(...) is deprecated. Use magicString.prependRight(...) instead"),B.insertRight=!0),this.prependRight(e,r)}move(e,r,i){if(i>=e&&i<=r)throw new Error("Cannot move a selection inside itself");this._split(e),this._split(r),this._split(i);const o=this.byStart[e],c=this.byEnd[r],u=o.previous,p=c.next,g=this.byStart[i];if(!g&&c===this.lastChunk)return this;const b=g?g.previous:this.lastChunk;return u&&(u.next=p),p&&(p.previous=u),b&&(b.next=o),g&&(g.previous=c),o.previous||(this.firstChunk=c.next),c.next||(this.lastChunk=o.previous,this.lastChunk.next=null),o.previous=b,c.next=g||null,b||(this.firstChunk=o),g||(this.lastChunk=c),this}overwrite(e,r,i,o){return o=o||{},this.update(e,r,i,{...o,overwrite:!o.contentOnly})}update(e,r,i,o){if(typeof i!="string")throw new TypeError("replacement content must be a string");for(;e<0;)e+=this.original.length;for(;r<0;)r+=this.original.length;if(r>this.original.length)throw new Error("end is out of bounds");if(e===r)throw new Error("Cannot overwrite a zero-length range \u2013 use appendLeft or prependRight instead");this._split(e),this._split(r),o===!0&&(B.storeName||(console.warn("The final argument to magicString.overwrite(...) should be an options object. See https://github.com/rich-harris/magic-string"),B.storeName=!0),o={storeName:!0});const c=o!==void 0?o.storeName:!1,u=o!==void 0?o.overwrite:!1;if(c){const b=this.original.slice(e,r);Object.defineProperty(this.storedNames,b,{writable:!0,value:!0,enumerable:!0})}const p=this.byStart[e],g=this.byEnd[r];if(p){let b=p;for(;b!==g;){if(b.next!==this.byStart[b.end])throw new Error("Cannot overwrite across a split point");b=b.next,b.edit("",!1)}p.edit(i,c,!u)}else{const b=new ee(e,r,"").edit(i,c);g.next=b,b.previous=g}return this}prepend(e){if(typeof e!="string")throw new TypeError("outro content must be a string");return this.intro=e+this.intro,this}prependLeft(e,r){if(typeof r!="string")throw new TypeError("inserted content must be a string");this._split(e);const i=this.byEnd[e];return i?i.prependLeft(r):this.intro=r+this.intro,this}prependRight(e,r){if(typeof r!="string")throw new TypeError("inserted content must be a string");this._split(e);const i=this.byStart[e];return i?i.prependRight(r):this.outro=r+this.outro,this}remove(e,r){for(;e<0;)e+=this.original.length;for(;r<0;)r+=this.original.length;if(e===r)return this;if(e<0||r>this.original.length)throw new Error("Character is out of bounds");if(e>r)throw new Error("end must be greater than start");this._split(e),this._split(r);let i=this.byStart[e];for(;i;)i.intro="",i.outro="",i.edit(""),i=r>i.end?this.byStart[i.end]:null;return this}reset(e,r){for(;e<0;)e+=this.original.length;for(;r<0;)r+=this.original.length;if(e===r)return this;if(e<0||r>this.original.length)throw new Error("Character is out of bounds");if(e>r)throw new Error("end must be greater than start");this._split(e),this._split(r);let i=this.byStart[e];for(;i;)i.reset(),i=r>i.end?this.byStart[i.end]:null;return this}lastChar(){if(this.outro.length)return this.outro[this.outro.length-1];let e=this.lastChunk;do{if(e.outro.length)return e.outro[e.outro.length-1];if(e.content.length)return e.content[e.content.length-1];if(e.intro.length)return e.intro[e.intro.length-1]}while(e=e.previous);return this.intro.length?this.intro[this.intro.length-1]:""}lastLine(){let e=this.outro.lastIndexOf(Q);if(e!==-1)return this.outro.substr(e+1);let r=this.outro,i=this.lastChunk;do{if(i.outro.length>0){if(e=i.outro.lastIndexOf(Q),e!==-1)return i.outro.substr(e+1)+r;r=i.outro+r}if(i.content.length>0){if(e=i.content.lastIndexOf(Q),e!==-1)return i.content.substr(e+1)+r;r=i.content+r}if(i.intro.length>0){if(e=i.intro.lastIndexOf(Q),e!==-1)return i.intro.substr(e+1)+r;r=i.intro+r}}while(i=i.previous);return e=this.intro.lastIndexOf(Q),e!==-1?this.intro.substr(e+1)+r:this.intro+r}slice(e=0,r=this.original.length){for(;e<0;)e+=this.original.length;for(;r<0;)r+=this.original.length;let i="",o=this.firstChunk;for(;o&&(o.start>e||o.end<=e);){if(o.start<r&&o.end>=r)return i;o=o.next}if(o&&o.edited&&o.start!==e)throw new Error(`Cannot use replaced character ${e} as slice start anchor.`);const c=o;for(;o;){o.intro&&(c!==o||o.start===e)&&(i+=o.intro);const u=o.start<r&&o.end>=r;if(u&&o.edited&&o.end!==r)throw new Error(`Cannot use replaced character ${r} as slice end anchor.`);const p=c===o?e-o.start:0,g=u?o.content.length+r-o.end:o.content.length;if(i+=o.content.slice(p,g),o.outro&&(!u||o.end===r)&&(i+=o.outro),u)break;o=o.next}return i}snip(e,r){const i=this.clone();return i.remove(0,e),i.remove(r,i.original.length),i}_split(e){if(this.byStart[e]||this.byEnd[e])return;let r=this.lastSearchedChunk;const i=e>r.end;for(;r;){if(r.contains(e))return this._splitChunk(r,e);r=i?this.byStart[r.end]:this.byEnd[r.start]}}_splitChunk(e,r){if(e.edited&&e.content.length){const o=Fe(this.original)(r);throw new Error(`Cannot split a chunk that has already been edited (${o.line}:${o.column} \u2013 "${e.original}")`)}const i=e.split(r);return this.byEnd[r]=e,this.byStart[r]=i,this.byEnd[i.end]=i,e===this.lastChunk&&(this.lastChunk=i),this.lastSearchedChunk=e,!0}toString(){let e=this.intro,r=this.firstChunk;for(;r;)e+=r.toString(),r=r.next;return e+this.outro}isEmpty(){let e=this.firstChunk;do if(e.intro.length&&e.intro.trim()||e.content.length&&e.content.trim()||e.outro.length&&e.outro.trim())return!1;while(e=e.next);return!0}length(){let e=this.firstChunk,r=0;do r+=e.intro.length+e.content.length+e.outro.length;while(e=e.next);return r}trimLines(){return this.trim("[\\r\\n]")}trim(e){return this.trimStart(e).trimEnd(e)}trimEndAborted(e){const r=new RegExp((e||"\\s")+"+$");if(this.outro=this.outro.replace(r,""),this.outro.length)return!0;let i=this.lastChunk;do{const o=i.end,c=i.trimEnd(r);if(i.end!==o&&(this.lastChunk===i&&(this.lastChunk=i.next),this.byEnd[i.end]=i,this.byStart[i.next.start]=i.next,this.byEnd[i.next.end]=i.next),c)return!0;i=i.previous}while(i);return!1}trimEnd(e){return this.trimEndAborted(e),this}trimStartAborted(e){const r=new RegExp("^"+(e||"\\s")+"+");if(this.intro=this.intro.replace(r,""),this.intro.length)return!0;let i=this.firstChunk;do{const o=i.end,c=i.trimStart(r);if(i.end!==o&&(i===this.lastChunk&&(this.lastChunk=i.next),this.byEnd[i.end]=i,this.byStart[i.next.start]=i.next,this.byEnd[i.next.end]=i.next),c)return!0;i=i.next}while(i);return!1}trimStart(e){return this.trimStartAborted(e),this}hasChanged(){return this.original!==this.toString()}_replaceRegexp(e,r){function i(c,u){return typeof r=="string"?r.replace(/\$(\$|&|\d+)/g,(p,g)=>g==="$"?"$":g==="&"?c[0]:+g<c.length?c[+g]:`$${g}`):r(...c,c.index,u,c.groups)}f(i,"getReplacement");function o(c,u){let p;const g=[];for(;p=c.exec(u);)g.push(p);return g}if(f(o,"matchAll"),e.global)o(e,this.original).forEach(u=>{if(u.index!=null){const p=i(u,this.original);p!==u[0]&&this.overwrite(u.index,u.index+u[0].length,p)}});else{const c=this.original.match(e);if(c&&c.index!=null){const u=i(c,this.original);u!==c[0]&&this.overwrite(c.index,c.index+c[0].length,u)}}return this}_replaceString(e,r){const{original:i}=this,o=i.indexOf(e);return o!==-1&&this.overwrite(o,o+e.length,r),this}replace(e,r){return typeof e=="string"?this._replaceString(e,r):this._replaceRegexp(e,r)}_replaceAllString(e,r){const{original:i}=this,o=e.length;for(let c=i.indexOf(e);c!==-1;c=i.indexOf(e,c+o))i.slice(c,c+o)!==r&&this.overwrite(c,c+o,r);return this}replaceAll(e,r){if(typeof e=="string")return this._replaceAllString(e,r);if(!e.global)throw new TypeError("MagicString.prototype.replaceAll called with a non-global RegExp argument");return this._replaceRegexp(e,r)}}let x,ie,ye,Z=2<<19;const Pe=new Uint8Array(new Uint16Array([1]).buffer)[0]===1?function(s,e){const r=s.length;let i=0;for(;i<r;)e[i]=s.charCodeAt(i++)}:function(s,e){const r=s.length;let i=0;for(;i<r;){const o=s.charCodeAt(i);e[i++]=(255&o)<<8|o>>>8}},ar="xportmportlassforetaourceromsyncunctionssertvoyiedelecontininstantybreareturdebuggeawaithrwhileifcatcfinallels";let _,We,y;function cr(s,e="@"){_=s,We=e;const r=2*_.length+(2<<18);if(r>Z||!x){for(;r>Z;)Z*=2;ie=new ArrayBuffer(Z),Pe(ar,new Uint16Array(ie,16,110)),x=function(u,p,g){var b=new u.Int8Array(g),d=new u.Int16Array(g),n=new u.Int32Array(g),R=new u.Uint8Array(g),L=new u.Uint16Array(g),E=1040;function N(){var t=0,a=0,h=0,l=0,w=0,m=0,C=0;C=E,E=E+10240|0,b[804]=1,b[803]=0,d[399]=0,d[400]=0,n[69]=n[2],b[805]=0,n[68]=0,b[802]=0,n[70]=C+2048,n[71]=C,b[806]=0,t=(n[3]|0)+-2|0,n[72]=t,a=t+(n[66]<<1)|0,n[73]=a;e:for(;;){if(h=t+2|0,n[72]=h,t>>>0>=a>>>0){l=18;break}t:do switch(d[h>>1]|0){case 9:case 10:case 11:case 12:case 13:case 32:break;case 101:{if(!(d[400]|0)&&z(h)|0&&!(A(t+4|0,16,10)|0)&&(U(),(b[804]|0)==0)){l=9;break e}else l=17;break}case 105:{z(h)|0&&!(A(t+4|0,26,10)|0)&&P(),l=17;break}case 59:{l=17;break}case 47:switch(d[t+4>>1]|0){case 47:{de();break t}case 42:{he(1);break t}default:{l=16;break e}}default:{l=16;break e}}while(!1);(l|0)==17&&(l=0,n[69]=n[72]),t=n[72]|0,a=n[73]|0}(l|0)==9?(t=n[72]|0,n[69]=t,l=19):(l|0)==16?(b[804]=0,n[72]=t,l=19):(l|0)==18&&(b[802]|0?t=0:(t=h,l=19));do if((l|0)==19){e:for(;;){if(a=t+2|0,n[72]=a,t>>>0>=(n[73]|0)>>>0){l=92;break}t:do switch(d[a>>1]|0){case 9:case 10:case 11:case 12:case 13:case 32:break;case 101:{!(d[400]|0)&&z(a)|0&&!(A(t+4|0,16,10)|0)&&U(),l=91;break}case 105:{z(a)|0&&!(A(t+4|0,26,10)|0)&&P(),l=91;break}case 99:{z(a)|0&&!(A(t+4|0,36,8)|0)&&q(d[t+12>>1]|0)|0&&(b[806]=1),l=91;break}case 40:{h=n[70]|0,t=d[400]|0,l=t&65535,n[h+(l<<3)>>2]=1,a=n[69]|0,d[400]=t+1<<16>>16,n[h+(l<<3)+4>>2]=a,l=91;break}case 41:{if(a=d[400]|0,!(a<<16>>16)){l=36;break e}h=a+-1<<16>>16,d[400]=h,l=d[399]|0,a=l&65535,l<<16>>16&&(n[(n[70]|0)+((h&65535)<<3)>>2]|0)==5&&(a=n[(n[71]|0)+(a+-1<<2)>>2]|0,h=a+4|0,n[h>>2]|0||(n[h>>2]=(n[69]|0)+2),n[a+12>>2]=t+4,d[399]=l+-1<<16>>16),l=91;break}case 123:{l=n[69]|0,h=n[63]|0,t=l;do if((d[l>>1]|0)==41&(h|0)!=0&&(n[h+4>>2]|0)==(l|0))if(a=n[64]|0,n[63]=a,a){n[a+32>>2]=0;break}else{n[59]=0;break}while(!1);h=n[70]|0,a=d[400]|0,l=a&65535,n[h+(l<<3)>>2]=b[806]|0?6:2,d[400]=a+1<<16>>16,n[h+(l<<3)+4>>2]=t,b[806]=0,l=91;break}case 125:{if(t=d[400]|0,!(t<<16>>16)){l=49;break e}h=n[70]|0,l=t+-1<<16>>16,d[400]=l,(n[h+((l&65535)<<3)>>2]|0)==4&&Le(),l=91;break}case 39:{I(39),l=91;break}case 34:{I(34),l=91;break}case 47:switch(d[t+4>>1]|0){case 47:{de();break t}case 42:{he(1);break t}default:{t=n[69]|0,a=d[t>>1]|0;r:do if(!(yt(a)|0))a<<16>>16==41?(h=d[400]|0,xt(n[(n[70]|0)+((h&65535)<<3)+4>>2]|0)|0||(l=65)):l=64;else switch(a<<16>>16){case 46:if(((d[t+-2>>1]|0)+-48&65535)<10){l=64;break r}else break r;case 43:if((d[t+-2>>1]|0)==43){l=64;break r}else break r;case 45:if((d[t+-2>>1]|0)==45){l=64;break r}else break r;default:break r}while(!1);(l|0)==64&&(h=d[400]|0,l=65);r:do if((l|0)==65){if(l=0,h<<16>>16&&(w=n[70]|0,m=(h&65535)+-1|0,a<<16>>16==102?(n[w+(m<<3)>>2]|0)==1:0)){if((d[t+-2>>1]|0)==111&&O(n[w+(m<<3)+4>>2]|0,44,3)|0)break}else l=69;if((l|0)==69&&a<<16>>16==125&&(l=n[70]|0,h=h&65535,kt(n[l+(h<<3)+4>>2]|0)|0||(n[l+(h<<3)>>2]|0)==6))break;if(!(wt(t)|0)){switch(a<<16>>16){case 0:break r;case 47:{if(b[805]|0)break r;break}default:}if(l=n[65]|0,l|0&&t>>>0>=(n[l>>2]|0)>>>0&&t>>>0<=(n[l+4>>2]|0)>>>0){le(),b[805]=0,l=91;break t}h=n[3]|0;do{if(t>>>0<=h>>>0)break;t=t+-2|0,n[69]=t,a=d[t>>1]|0}while(!(fe(a)|0));if(re(a)|0){do{if(t>>>0<=h>>>0)break;t=t+-2|0,n[69]=t}while(re(d[t>>1]|0)|0);if(St(t)|0){le(),b[805]=0,l=91;break t}}b[805]=1,l=91;break t}}while(!1);le(),b[805]=0,l=91;break t}}case 96:{h=n[70]|0,a=d[400]|0,l=a&65535,n[h+(l<<3)+4>>2]=n[69],d[400]=a+1<<16>>16,n[h+(l<<3)>>2]=3,Le(),l=91;break}default:l=91}while(!1);(l|0)==91&&(l=0,n[69]=n[72]),t=n[72]|0}if((l|0)==36){M(),t=0;break}else if((l|0)==49){M(),t=0;break}else if((l|0)==92){t=b[802]|0?0:(d[399]|d[400])<<16>>16==0;break}}while(!1);return E=C,t|0}f(N,"b");function U(){var t=0,a=0,h=0,l=0,w=0,m=0,C=0,T=0,be=0,pe=0,we=0,me=0,S=0,v=0;T=n[72]|0,be=n[65]|0,v=T+12|0,n[72]=v,h=k(1)|0,t=n[72]|0,(t|0)==(v|0)&&!(te(h)|0)||(S=3);e:do if((S|0)==3){t:do switch(h<<16>>16){case 123:{for(n[72]=t+2,t=k(1)|0,a=n[72]|0;;){if(H(t)|0?(I(t),t=(n[72]|0)+2|0,n[72]=t):($(t)|0,t=n[72]|0),k(1)|0,t=Oe(a,t)|0,t<<16>>16==44&&(n[72]=(n[72]|0)+2,t=k(1)|0),t<<16>>16==125){S=15;break}if(v=a,a=n[72]|0,(a|0)==(v|0)){S=12;break}if(a>>>0>(n[73]|0)>>>0){S=14;break}}if((S|0)==12){M();break e}else if((S|0)==14){M();break e}else if((S|0)==15){b[803]=1,n[72]=(n[72]|0)+2;break t}break}case 42:{n[72]=t+2,k(1)|0,v=n[72]|0,Oe(v,v)|0;break}default:{switch(b[804]=0,h<<16>>16){case 100:{switch(T=t+14|0,n[72]=T,(k(1)|0)<<16>>16){case 97:{a=n[72]|0,!(A(a+2|0,72,8)|0)&&(w=a+10|0,re(d[w>>1]|0)|0)&&(n[72]=w,k(0)|0,S=22);break}case 102:{S=22;break}case 99:{a=n[72]|0,!(A(a+2|0,36,8)|0)&&(l=a+10|0,v=d[l>>1]|0,q(v)|0|v<<16>>16==123)&&(n[72]=l,m=k(1)|0,m<<16>>16!=123)&&(me=m,S=31);break}default:}r:do if((S|0)==22&&(C=n[72]|0,(A(C+2|0,80,14)|0)==0)){if(h=C+16|0,a=d[h>>1]|0,!(q(a)|0))switch(a<<16>>16){case 40:case 42:break;default:break r}n[72]=h,a=k(1)|0,a<<16>>16==42&&(n[72]=(n[72]|0)+2,a=k(1)|0),a<<16>>16!=40&&(me=a,S=31)}while(!1);if((S|0)==31&&(pe=n[72]|0,$(me)|0,we=n[72]|0,we>>>0>pe>>>0)){W(t,T,pe,we),n[72]=(n[72]|0)+-2;break e}W(t,T,0,0),n[72]=t+12;break e}case 97:{n[72]=t+10,k(0)|0,t=n[72]|0,S=35;break}case 102:{S=35;break}case 99:{if(!(A(t+2|0,36,8)|0)&&(a=t+10|0,fe(d[a>>1]|0)|0)){n[72]=a,v=k(1)|0,S=n[72]|0,$(v)|0,v=n[72]|0,W(S,v,S,v),n[72]=(n[72]|0)+-2;break e}t=t+4|0,n[72]=t;break}case 108:case 118:break;default:break e}if((S|0)==35){n[72]=t+16,t=k(1)|0,t<<16>>16==42&&(n[72]=(n[72]|0)+2,t=k(1)|0),S=n[72]|0,$(t)|0,v=n[72]|0,W(S,v,S,v),n[72]=(n[72]|0)+-2;break e}n[72]=t+6,b[804]=0,h=k(1)|0,t=n[72]|0,h=($(h)|0|32)<<16>>16==123,l=n[72]|0,h&&(n[72]=l+2,v=k(1)|0,t=n[72]|0,$(v)|0);r:for(;a=n[72]|0,(a|0)!=(t|0);){if(W(t,a,t,a),a=k(1)|0,h)switch(a<<16>>16){case 93:case 125:break e;default:}if(t=n[72]|0,a<<16>>16!=44){S=51;break}switch(n[72]=t+2,a=k(1)|0,t=n[72]|0,a<<16>>16){case 91:case 123:{S=51;break r}default:}$(a)|0}if((S|0)==51&&(n[72]=t+-2),!h)break e;n[72]=l+-2;break e}}while(!1);if(v=(k(1)|0)<<16>>16==102,t=n[72]|0,v&&!(A(t+2|0,66,6)|0))for(n[72]=t+8,J(T,k(1)|0,0),t=be|0?be+16|0:240;;){if(t=n[t>>2]|0,!t)break e;n[t+12>>2]=0,n[t+8>>2]=0,t=t+16|0}n[72]=t+-2}while(!1)}f(U,"k");function P(){var t=0,a=0,h=0,l=0,w=0,m=0,C=0;w=n[72]|0,h=w+12|0,n[72]=h,l=k(1)|0,a=n[72]|0;e:do if(l<<16>>16!=46)l<<16>>16==115&a>>>0>h>>>0?!(A(a+2|0,56,10)|0)&&(t=a+12|0,q(d[t>>1]|0)|0)?m=14:(a=6,h=0,m=46):(t=l,h=0,m=15);else switch(n[72]=a+2,(k(1)|0)<<16>>16){case 109:{if(t=n[72]|0,A(t+2|0,50,6)|0||(a=n[69]|0,!(ge(a)|0)&&(d[a>>1]|0)==46))break e;ue(w,w,t+8|0,2);break e}case 115:{if(t=n[72]|0,A(t+2|0,56,10)|0||(a=n[69]|0,!(ge(a)|0)&&(d[a>>1]|0)==46))break e;t=t+12|0,m=14;break e}default:break e}while(!1);(m|0)==14&&(n[72]=t,t=k(1)|0,h=1,m=15);e:do if((m|0)==15)switch(t<<16>>16){case 40:{if(a=n[70]|0,C=d[400]|0,l=C&65535,n[a+(l<<3)>>2]=5,t=n[72]|0,d[400]=C+1<<16>>16,n[a+(l<<3)+4>>2]=t,(d[n[69]>>1]|0)==46)break e;switch(n[72]=t+2,a=k(1)|0,ue(w,n[72]|0,0,t),h?(t=n[63]|0,n[t+28>>2]=5):t=n[63]|0,w=n[71]|0,C=d[399]|0,d[399]=C+1<<16>>16,n[w+((C&65535)<<2)>>2]=t,a<<16>>16){case 39:{I(39);break}case 34:{I(34);break}default:{n[72]=(n[72]|0)+-2;break e}}switch(t=(n[72]|0)+2|0,n[72]=t,(k(1)|0)<<16>>16){case 44:{n[72]=(n[72]|0)+2,k(1)|0,w=n[63]|0,n[w+4>>2]=t,C=n[72]|0,n[w+16>>2]=C,b[w+24>>0]=1,n[72]=C+-2;break e}case 41:{d[400]=(d[400]|0)+-1<<16>>16,C=n[63]|0,n[C+4>>2]=t,n[C+12>>2]=(n[72]|0)+2,b[C+24>>0]=1,d[399]=(d[399]|0)+-1<<16>>16;break e}default:{n[72]=(n[72]|0)+-2;break e}}}case 123:{if(h){a=12,h=1,m=46;break e}if(t=n[72]|0,d[400]|0){n[72]=t+-2;break e}for(;!(t>>>0>=(n[73]|0)>>>0);){if(t=k(1)|0,H(t)|0)I(t);else if(t<<16>>16==125){m=36;break}t=(n[72]|0)+2|0,n[72]=t}if((m|0)==36&&(n[72]=(n[72]|0)+2),C=(k(1)|0)<<16>>16==102,t=n[72]|0,C&&A(t+2|0,66,6)|0){M();break e}if(n[72]=t+8,t=k(1)|0,H(t)|0){J(w,t,0);break e}else{M();break e}}default:{if(h){a=12,h=1,m=46;break e}switch(t<<16>>16){case 42:case 39:case 34:{h=0,m=48;break e}default:{a=6,h=0,m=46;break e}}}}while(!1);(m|0)==46&&(t=n[72]|0,(t|0)==(w+(a<<1)|0)?n[72]=t+-2:m=48);do if((m|0)==48){if(d[400]|0){n[72]=(n[72]|0)+-2;break}for(t=n[73]|0,a=n[72]|0;;){if(a>>>0>=t>>>0){m=55;break}if(l=d[a>>1]|0,H(l)|0){m=53;break}C=a+2|0,n[72]=C,a=C}if((m|0)==53){J(w,l,h);break}else if((m|0)==55){M();break}}while(!1)}f(P,"l");function J(t,a,h){t=t|0,a=a|0,h=h|0;var l=0,w=0;switch(l=(n[72]|0)+2|0,a<<16>>16){case 39:{I(39),w=5;break}case 34:{I(34),w=5;break}default:M()}do if((w|0)==5){if(ue(t,l,n[72]|0,1),h&&(n[(n[63]|0)+28>>2]=4),n[72]=(n[72]|0)+2,a=k(0)|0,h=a<<16>>16==97,h?(l=n[72]|0,A(l+2|0,94,10)|0&&(w=13)):(l=n[72]|0,a<<16>>16==119&&(d[l+2>>1]|0)==105&&(d[l+4>>1]|0)==116&&(d[l+6>>1]|0)==104||(w=13)),(w|0)==13){n[72]=l+-2;break}if(n[72]=l+((h?6:4)<<1),(k(1)|0)<<16>>16!=123){n[72]=l;break}h=n[72]|0,a=h;e:for(;;){switch(n[72]=a+2,a=k(1)|0,a<<16>>16){case 39:{I(39),n[72]=(n[72]|0)+2,a=k(1)|0;break}case 34:{I(34),n[72]=(n[72]|0)+2,a=k(1)|0;break}default:a=$(a)|0}if(a<<16>>16!=58){w=22;break}switch(n[72]=(n[72]|0)+2,(k(1)|0)<<16>>16){case 39:{I(39);break}case 34:{I(34);break}default:{w=26;break e}}switch(n[72]=(n[72]|0)+2,(k(1)|0)<<16>>16){case 125:{w=31;break e}case 44:break;default:{w=30;break e}}if(n[72]=(n[72]|0)+2,(k(1)|0)<<16>>16==125){w=31;break}a=n[72]|0}if((w|0)==22){n[72]=l;break}else if((w|0)==26){n[72]=l;break}else if((w|0)==30){n[72]=l;break}else if((w|0)==31){w=n[63]|0,n[w+16>>2]=h,n[w+12>>2]=(n[72]|0)+2;break}}while(!1)}f(J,"u");function wt(t){t=t|0;e:do switch(d[t>>1]|0){case 100:switch(d[t+-2>>1]|0){case 105:{t=O(t+-4|0,104,2)|0;break e}case 108:{t=O(t+-4|0,108,3)|0;break e}default:{t=0;break e}}case 101:switch(d[t+-2>>1]|0){case 115:switch(d[t+-4>>1]|0){case 108:{t=G(t+-6|0,101)|0;break e}case 97:{t=G(t+-6|0,99)|0;break e}default:{t=0;break e}}case 116:{t=O(t+-4|0,114,4)|0;break e}case 117:{t=O(t+-4|0,122,6)|0;break e}default:{t=0;break e}}case 102:{if((d[t+-2>>1]|0)==111&&(d[t+-4>>1]|0)==101)switch(d[t+-6>>1]|0){case 99:{t=O(t+-8|0,134,6)|0;break e}case 112:{t=O(t+-8|0,146,2)|0;break e}default:{t=0;break e}}else t=0;break}case 107:{t=O(t+-2|0,150,4)|0;break}case 110:{t=t+-2|0,G(t,105)|0?t=1:t=O(t,158,5)|0;break}case 111:{t=G(t+-2|0,100)|0;break}case 114:{t=O(t+-2|0,168,7)|0;break}case 116:{t=O(t+-2|0,182,4)|0;break}case 119:switch(d[t+-2>>1]|0){case 101:{t=G(t+-4|0,110)|0;break e}case 111:{t=O(t+-4|0,190,3)|0;break e}default:{t=0;break e}}default:t=0}while(!1);return t|0}f(wt,"o");function Le(){var t=0,a=0,h=0,l=0;a=n[73]|0,h=n[72]|0;e:for(;;){if(t=h+2|0,h>>>0>=a>>>0){a=10;break}switch(d[t>>1]|0){case 96:{a=7;break e}case 36:{if((d[h+4>>1]|0)==123){a=6;break e}break}case 92:{t=h+4|0;break}default:}h=t}(a|0)==6?(t=h+4|0,n[72]=t,a=n[70]|0,l=d[400]|0,h=l&65535,n[a+(h<<3)>>2]=4,d[400]=l+1<<16>>16,n[a+(h<<3)+4>>2]=t):(a|0)==7?(n[72]=t,h=n[70]|0,l=(d[400]|0)+-1<<16>>16,d[400]=l,(n[h+((l&65535)<<3)>>2]|0)!=3&&M()):(a|0)==10&&(n[72]=t,M())}f(Le,"h");function k(t){t=t|0;var a=0,h=0,l=0;h=n[72]|0;e:do{a=d[h>>1]|0;t:do if(a<<16>>16!=47)if(t){if(q(a)|0)break;break e}else{if(re(a)|0)break;break e}else switch(d[h+2>>1]|0){case 47:{de();break t}case 42:{he(t);break t}default:{a=47;break e}}while(!1);l=n[72]|0,h=l+2|0,n[72]=h}while(l>>>0<(n[73]|0)>>>0);return a|0}f(k,"w");function ue(t,a,h,l){t=t|0,a=a|0,h=h|0,l=l|0;var w=0,m=0;m=n[67]|0,n[67]=m+36,w=n[63]|0,n[(w|0?w+32|0:236)>>2]=m,n[64]=w,n[63]=m,n[m+8>>2]=t,(l|0)==2?(t=3,w=h):(w=(l|0)==1,t=w?1:2,w=w?h+2|0:0),n[m+12>>2]=w,n[m+28>>2]=t,n[m>>2]=a,n[m+4>>2]=h,n[m+16>>2]=0,n[m+20>>2]=l,a=(l|0)==1,b[m+24>>0]=a&1,n[m+32>>2]=0,a|(l|0)==2&&(b[803]=1)}f(ue,"d");function I(t){t=t|0;var a=0,h=0,l=0,w=0;for(w=n[73]|0,a=n[72]|0;;){if(l=a+2|0,a>>>0>=w>>>0){a=9;break}if(h=d[l>>1]|0,h<<16>>16==t<<16>>16){a=10;break}if(h<<16>>16==92)h=a+4|0,(d[h>>1]|0)==13?(a=a+6|0,a=(d[a>>1]|0)==10?a:h):a=h;else if(Ae(h)|0){a=9;break}else a=l}(a|0)==9?(n[72]=l,M()):(a|0)==10&&(n[72]=l)}f(I,"v");function Oe(t,a){t=t|0,a=a|0;var h=0,l=0,w=0,m=0;return h=n[72]|0,l=d[h>>1]|0,m=(t|0)==(a|0),w=m?0:t,m=m?0:a,l<<16>>16==97&&(n[72]=h+4,h=k(1)|0,t=n[72]|0,H(h)|0?(I(h),a=(n[72]|0)+2|0,n[72]=a):($(h)|0,a=n[72]|0),l=k(1)|0,h=n[72]|0),(h|0)!=(t|0)&&W(t,a,w,m),l|0}f(Oe,"A");function mt(){var t=0,a=0,h=0;h=n[73]|0,a=n[72]|0;e:for(;;){if(t=a+2|0,a>>>0>=h>>>0){a=6;break}switch(d[t>>1]|0){case 13:case 10:{a=6;break e}case 93:{a=7;break e}case 92:{t=a+4|0;break}default:}a=t}return(a|0)==6?(n[72]=t,M(),t=0):(a|0)==7&&(n[72]=t,t=93),t|0}f(mt,"C");function le(){var t=0,a=0,h=0;e:for(;;){if(t=n[72]|0,a=t+2|0,n[72]=a,t>>>0>=(n[73]|0)>>>0){h=7;break}switch(d[a>>1]|0){case 13:case 10:{h=7;break e}case 47:break e;case 91:{mt()|0;break}case 92:{n[72]=t+4;break}default:}}(h|0)==7&&M()}f(le,"g");function kt(t){switch(t=t|0,d[t>>1]|0){case 62:{t=(d[t+-2>>1]|0)==61;break}case 41:case 59:{t=1;break}case 104:{t=O(t+-2|0,210,4)|0;break}case 121:{t=O(t+-2|0,218,6)|0;break}case 101:{t=O(t+-2|0,230,3)|0;break}default:t=0}return t|0}f(kt,"p");function he(t){t=t|0;var a=0,h=0,l=0,w=0,m=0;for(w=(n[72]|0)+2|0,n[72]=w,h=n[73]|0;a=w+2|0,!(w>>>0>=h>>>0||(l=d[a>>1]|0,!t&&Ae(l)|0));){if(l<<16>>16==42&&(d[w+4>>1]|0)==47){m=8;break}w=a}(m|0)==8&&(n[72]=a,a=w+4|0),n[72]=a}f(he,"y");function A(t,a,h){t=t|0,a=a|0,h=h|0;var l=0,w=0;e:do if(!h)t=0;else{for(;l=b[t>>0]|0,w=b[a>>0]|0,l<<24>>24==w<<24>>24;)if(h=h+-1|0,h)t=t+1|0,a=a+1|0;else{t=0;break e}t=(l&255)-(w&255)|0}while(!1);return t|0}f(A,"m");function te(t){t=t|0;e:do switch(t<<16>>16){case 38:case 37:case 33:{t=1;break}default:if((t&-8)<<16>>16==40|(t+-58&65535)<6)t=1;else{switch(t<<16>>16){case 91:case 93:case 94:{t=1;break e}default:}t=(t+-123&65535)<4}}while(!1);return t|0}f(te,"I");function yt(t){t=t|0;e:do switch(t<<16>>16){case 38:case 37:case 33:break;default:if(!((t+-58&65535)<6|(t+-40&65535)<7&t<<16>>16!=41)){switch(t<<16>>16){case 91:case 94:break e;default:}return t<<16>>16!=125&(t+-123&65535)<4|0}}while(!1);return 1}f(yt,"U");function Re(t){t=t|0;var a=0;a=d[t>>1]|0;e:do if((a+-9&65535)>=5){switch(a<<16>>16){case 160:case 32:{a=1;break e}default:}if(te(a)|0)return a<<16>>16!=46|(ge(t)|0)|0;a=0}else a=1;while(!1);return a|0}f(Re,"x");function Ct(t){t=t|0;var a=0,h=0,l=0,w=0;return h=E,E=E+16|0,l=h,n[l>>2]=0,n[66]=t,a=n[3]|0,w=a+(t<<1)|0,t=w+2|0,d[w>>1]=0,n[l>>2]=t,n[67]=t,n[59]=0,n[63]=0,n[61]=0,n[60]=0,n[65]=0,n[62]=0,E=h,a|0}f(Ct,"S");function W(t,a,h,l){t=t|0,a=a|0,h=h|0,l=l|0;var w=0,m=0;w=n[67]|0,n[67]=w+20,m=n[65]|0,n[(m|0?m+16|0:240)>>2]=w,n[65]=w,n[w>>2]=t,n[w+4>>2]=a,n[w+8>>2]=h,n[w+12>>2]=l,n[w+16>>2]=0,b[803]=1}f(W,"O");function O(t,a,h){t=t|0,a=a|0,h=h|0;var l=0,w=0;return l=t+(0-h<<1)|0,w=l+2|0,t=n[3]|0,w>>>0>=t>>>0&&!(A(w,a,h<<1)|0)?(w|0)==(t|0)?t=1:t=Re(l)|0:t=0,t|0}f(O,"$");function St(t){switch(t=t|0,d[t>>1]|0){case 107:{t=O(t+-2|0,150,4)|0;break}case 101:{(d[t+-2>>1]|0)==117?t=O(t+-4|0,122,6)|0:t=0;break}default:t=0}return t|0}f(St,"j");function G(t,a){t=t|0,a=a|0;var h=0;return h=n[3]|0,h>>>0<=t>>>0&&(d[t>>1]|0)==a<<16>>16?(h|0)==(t|0)?h=1:h=fe(d[t+-2>>1]|0)|0:h=0,h|0}f(G,"B");function fe(t){t=t|0;e:do if((t+-9&65535)<5)t=1;else{switch(t<<16>>16){case 32:case 160:{t=1;break e}default:}t=t<<16>>16!=46&(te(t)|0)}while(!1);return t|0}f(fe,"E");function de(){var t=0,a=0,h=0;t=n[73]|0,h=n[72]|0;e:for(;a=h+2|0,!(h>>>0>=t>>>0);)switch(d[a>>1]|0){case 13:case 10:break e;default:h=a}n[72]=a}f(de,"P");function $(t){for(t=t|0;!(q(t)|0||te(t)|0);)if(t=(n[72]|0)+2|0,n[72]=t,t=d[t>>1]|0,!(t<<16>>16)){t=0;break}return t|0}f($,"q");function vt(){var t=0;switch(t=n[(n[61]|0)+20>>2]|0,t|0){case 1:{t=-1;break}case 2:{t=-2;break}default:t=t-(n[3]|0)>>1}return t|0}f(vt,"z");function xt(t){return t=t|0,!(O(t,196,5)|0)&&!(O(t,44,3)|0)?t=O(t,206,2)|0:t=1,t|0}f(xt,"D");function re(t){switch(t=t|0,t<<16>>16){case 160:case 32:case 12:case 11:case 9:{t=1;break}default:t=0}return t|0}f(re,"F");function ge(t){return t=t|0,(d[t>>1]|0)==46&&(d[t+-2>>1]|0)==46?t=(d[t+-4>>1]|0)==46:t=0,t|0}f(ge,"G");function z(t){return t=t|0,(n[3]|0)==(t|0)?t=1:t=Re(t+-2|0)|0,t|0}f(z,"H");function _t(){var t=0;return t=n[(n[62]|0)+12>>2]|0,t?t=t-(n[3]|0)>>1:t=-1,t|0}f(_t,"J");function Et(){var t=0;return t=n[(n[61]|0)+12>>2]|0,t?t=t-(n[3]|0)>>1:t=-1,t|0}f(Et,"K");function Lt(){var t=0;return t=n[(n[62]|0)+8>>2]|0,t?t=t-(n[3]|0)>>1:t=-1,t|0}f(Lt,"L");function Ot(){var t=0;return t=n[(n[61]|0)+16>>2]|0,t?t=t-(n[3]|0)>>1:t=-1,t|0}f(Ot,"M");function Rt(){var t=0;return t=n[(n[61]|0)+4>>2]|0,t?t=t-(n[3]|0)>>1:t=-1,t|0}f(Rt,"N");function At(){var t=0;return t=n[61]|0,t=n[(t|0?t+32|0:236)>>2]|0,n[61]=t,(t|0)!=0|0}f(At,"Q");function Nt(){var t=0;return t=n[62]|0,t=n[(t|0?t+16|0:240)>>2]|0,n[62]=t,(t|0)!=0|0}f(Nt,"R");function M(){b[802]=1,n[68]=(n[72]|0)-(n[3]|0)>>1,n[72]=(n[73]|0)+2}f(M,"T");function q(t){return t=t|0,(t|128)<<16>>16==160|(t+-9&65535)<5|0}f(q,"V");function H(t){return t=t|0,t<<16>>16==39|t<<16>>16==34|0}f(H,"W");function It(){return(n[(n[61]|0)+8>>2]|0)-(n[3]|0)>>1|0}f(It,"X");function Mt(){return(n[(n[62]|0)+4>>2]|0)-(n[3]|0)>>1|0}f(Mt,"Y");function Ae(t){return t=t|0,t<<16>>16==13|t<<16>>16==10|0}f(Ae,"Z");function Ut(){return(n[n[61]>>2]|0)-(n[3]|0)>>1|0}f(Ut,"_");function jt(){return(n[n[62]>>2]|0)-(n[3]|0)>>1|0}f(jt,"ee");function $t(){return R[(n[61]|0)+24>>0]|0|0}f($t,"ae");function Dt(t){t=t|0,n[3]=t}f(Dt,"re");function Tt(){return n[(n[61]|0)+28>>2]|0}f(Tt,"ie");function Ft(){return(b[803]|0)!=0|0}f(Ft,"se");function Pt(){return(b[804]|0)!=0|0}f(Pt,"fe");function Wt(){return n[68]|0}f(Wt,"te");function qt(t){return t=t|0,E=t+992+15&-16,992}return f(qt,"ce"),{su:qt,ai:Ot,e:Wt,ee:Mt,ele:_t,els:Lt,es:jt,f:Pt,id:vt,ie:Rt,ip:$t,is:Ut,it:Tt,ms:Ft,p:N,re:Nt,ri:At,sa:Ct,se:Et,ses:Dt,ss:It}}(typeof self<"u"?self:global,{},ie),ye=x.su(Z-(2<<17))}const i=_.length+1;x.ses(ye),x.sa(i-1),Pe(_,new Uint16Array(ie,ye,i)),x.p()||(y=x.e(),D());const o=[],c=[];for(;x.ri();){const u=x.is(),p=x.ie(),g=x.ai(),b=x.id(),d=x.ss(),n=x.se(),R=x.it();let L;x.ip()&&(L=Ce(b===-1?u:u+1,_.charCodeAt(b===-1?u-1:u))),o.push({t:R,n:L,s:u,e:p,ss:d,se:n,d:b,a:g})}for(;x.re();){const u=x.es(),p=x.ee(),g=x.els(),b=x.ele(),d=_.charCodeAt(u),n=g>=0?_.charCodeAt(g):-1;c.push({s:u,e:p,ls:g,le:b,n:d===34||d===39?Ce(u+1,d):_.slice(u,p),ln:g<0?void 0:n===34||n===39?Ce(g+1,n):_.slice(g,b)})}return[o,c,!!x.f(),!!x.ms()]}f(cr,"parse");function Ce(s,e){y=s;let r="",i=y;for(;;){y>=_.length&&D();const o=_.charCodeAt(y);if(o===e)break;o===92?(r+=_.slice(i,y),r+=ur(),i=y):(o===8232||o===8233||qe(o)&&D(),++y)}return r+=_.slice(i,y++),r}f(Ce,"b");function ur(){let s=_.charCodeAt(++y);switch(++y,s){case 110:return`
`;case 114:return"\r";case 120:return String.fromCharCode(Se(2));case 117:return function(){const e=_.charCodeAt(y);let r;return e===123?(++y,r=Se(_.indexOf("}",y)-y),++y,r>1114111&&D()):r=Se(4),r<=65535?String.fromCharCode(r):(r-=65536,String.fromCharCode(55296+(r>>10),56320+(1023&r)))}();case 116:return"	";case 98:return"\b";case 118:return"\v";case 102:return"\f";case 13:_.charCodeAt(y)===10&&++y;case 10:return"";case 56:case 57:D();default:if(s>=48&&s<=55){let e=_.substr(y-1,3).match(/^[0-7]+/)[0],r=parseInt(e,8);return r>255&&(e=e.slice(0,-1),r=parseInt(e,8)),y+=e.length-1,s=_.charCodeAt(y),e==="0"&&s!==56&&s!==57||D(),String.fromCharCode(r)}return qe(s)?"":String.fromCharCode(s)}}f(ur,"k");function Se(s){const e=y;let r=0,i=0;for(let o=0;o<s;++o,++y){let c,u=_.charCodeAt(y);if(u!==95){if(u>=97)c=u-97+10;else if(u>=65)c=u-65+10;else{if(!(u>=48&&u<=57))break;c=u-48}if(c>=16)break;i=u,r=16*r+c}else i!==95&&o!==0||D(),i=u}return i!==95&&y-e===s||D(),r}f(Se,"l");function qe(s){return s===13||s===10}f(qe,"u");function D(){throw Object.assign(Error(`Parse error ${We}:${_.slice(0,y).split(`
`).length}:${y-_.lastIndexOf(`
`,y-1)}`),{idx:y})}f(D,"o");let ve;typeof WebAssembly<"u"&&(async()=>{const{parse:s,init:e}=await Promise.resolve().then(function(){return require("./lexer-DgIbo0BU.cjs")});await e,ve=s})();const Be=f((s,e)=>ve?ve(s,e):cr(s,e),"parseEsm"),lr=f(s=>{if(!s.includes("import")&&!s.includes("export"))return!1;try{return Be(s)[3]}catch{return!0}},"isESM"),Je="2",hr=(s=>{const e="default";return s[e]&&typeof s[e]=="object"&&"__esModule"in s[e]?s[e]:s}).toString(),fr=`.then(${hr})`,xe=f((s,e,r)=>{if(r){if(!e.includes("import("))return}else if(!e.includes("import"))return;const o=Be(e,s)[0].filter(g=>g.d>-1);if(o.length===0)return;const c=new Ee(e);for(const g of o)c.appendRight(g.se,fr);const u=c.toString(),p=c.generateMap({source:s,includeContent:!1,hires:"boundary"});return{code:u,map:p}},"transformDynamicImport"),Ge=f(s=>{try{const e=j.readFileSync(s,"utf8");return JSON.parse(e)}catch{}},"readJsonFile"),ze=f(()=>{},"noop"),He=f(()=>Math.floor(Date.now()/1e8),"getTime");class dr extends Map{static{f(this,"FileCache")}cacheDirectory=zt.tmpdir;oldCacheDirectory=X.join(Gt.tmpdir(),"tsx");cacheFiles;constructor(){super(),j.mkdirSync(this.cacheDirectory,{recursive:!0}),this.cacheFiles=j.readdirSync(this.cacheDirectory).map(e=>{const[r,i]=e.split("-");return{time:Number(r),key:i,fileName:e}}),setImmediate(()=>{this.expireDiskCache(),this.removeOldCacheDirectory()})}get(e){const r=super.get(e);if(r)return r;const i=this.cacheFiles.find(u=>u.key===e);if(!i)return;const o=X.join(this.cacheDirectory,i.fileName),c=Ge(o);if(!c){j.promises.unlink(o).then(()=>{const u=this.cacheFiles.indexOf(i);this.cacheFiles.splice(u,1)},()=>{});return}return super.set(e,c),c}set(e,r){if(super.set(e,r),r){const i=He();j.promises.writeFile(X.join(this.cacheDirectory,`${i}-${e}`),JSON.stringify(r)).catch(ze)}return this}expireDiskCache(){const e=He();for(const r of this.cacheFiles)e-r.time>7&&j.promises.unlink(X.join(this.cacheDirectory,r.fileName)).catch(ze)}async removeOldCacheDirectory(){try{await j.promises.access(this.oldCacheDirectory).then(()=>!0)&&("rm"in j.promises?await j.promises.rm(this.oldCacheDirectory,{recursive:!0,force:!0}):await j.promises.rmdir(this.oldCacheDirectory,{recursive:!0}))}catch{}}}var se=process.env.TSX_DISABLE_CACHE?new Map:new dr;const gr=/^[\w+.-]+:\/\//,br=/^([\w+.-]+:)\/\/([^@/#?]*@)?([^:/#?]*)(:\d+)?(\/[^#?]*)?(\?[^#]*)?(#.*)?/,pr=/^file:(?:\/\/((?![a-z]:)[^/#?]*)?)?(\/?[^#?]*)(\?[^#]*)?(#.*)?/i;function wr(s){return gr.test(s)}f(wr,"isAbsoluteUrl");function mr(s){return s.startsWith("//")}f(mr,"isSchemeRelativeUrl");function Xe(s){return s.startsWith("/")}f(Xe,"isAbsolutePath");function kr(s){return s.startsWith("file:")}f(kr,"isFileUrl");function Ke(s){return/^[.?#]/.test(s)}f(Ke,"isRelative");function oe(s){const e=br.exec(s);return Ye(e[1],e[2]||"",e[3],e[4]||"",e[5]||"/",e[6]||"",e[7]||"")}f(oe,"parseAbsoluteUrl");function yr(s){const e=pr.exec(s),r=e[2];return Ye("file:","",e[1]||"","",Xe(r)?r:"/"+r,e[3]||"",e[4]||"")}f(yr,"parseFileUrl");function Ye(s,e,r,i,o,c,u){return{scheme:s,user:e,host:r,port:i,path:o,query:c,hash:u,type:7}}f(Ye,"makeUrl");function Qe(s){if(mr(s)){const r=oe("http:"+s);return r.scheme="",r.type=6,r}if(Xe(s)){const r=oe("http://foo.com"+s);return r.scheme="",r.host="",r.type=5,r}if(kr(s))return yr(s);if(wr(s))return oe(s);const e=oe("http://foo.com/"+s);return e.scheme="",e.host="",e.type=s?s.startsWith("?")?3:s.startsWith("#")?2:4:1,e}f(Qe,"parseUrl");function Cr(s){if(s.endsWith("/.."))return s;const e=s.lastIndexOf("/");return s.slice(0,e+1)}f(Cr,"stripPathFilename");function Sr(s,e){Ze(e,e.type),s.path==="/"?s.path=e.path:s.path=Cr(e.path)+s.path}f(Sr,"mergePaths");function Ze(s,e){const r=e<=4,i=s.path.split("/");let o=1,c=0,u=!1;for(let g=1;g<i.length;g++){const b=i[g];if(!b){u=!0;continue}if(u=!1,b!=="."){if(b===".."){c?(u=!0,c--,o--):r&&(i[o++]=b);continue}i[o++]=b,c++}}let p="";for(let g=1;g<o;g++)p+="/"+i[g];(!p||u&&!p.endsWith("/.."))&&(p+="/"),s.path=p}f(Ze,"normalizePath");function vr(s,e){if(!s&&!e)return"";const r=Qe(s);let i=r.type;if(e&&i!==7){const c=Qe(e),u=c.type;switch(i){case 1:r.hash=c.hash;case 2:r.query=c.query;case 3:case 4:Sr(r,c);case 5:r.user=c.user,r.host=c.host,r.port=c.port;case 6:r.scheme=c.scheme}u>i&&(i=u)}Ze(r,i);const o=r.query+r.hash;switch(i){case 2:case 3:return o;case 4:{const c=r.path.slice(1);return c?Ke(e||s)&&!Ke(c)?"./"+c+o:c+o:o||"."}case 5:return r.path+o;default:return r.scheme+"//"+r.user+r.host+r.port+r.path+o}}f(vr,"resolve$1");function Ve(s,e){return e&&!e.endsWith("/")&&(e+="/"),vr(s,e)}f(Ve,"resolve");function xr(s){if(!s)return"";const e=s.lastIndexOf("/");return s.slice(0,e+1)}f(xr,"stripFilename");const F=0;function _r(s,e){const r=et(s,0);if(r===s.length)return s;e||(s=s.slice());for(let i=r;i<s.length;i=et(s,i+1))s[i]=Lr(s[i],e);return s}f(_r,"maybeSort");function et(s,e){for(let r=e;r<s.length;r++)if(!Er(s[r]))return r;return s.length}f(et,"nextUnsortedSegmentLine");function Er(s){for(let e=1;e<s.length;e++)if(s[e][F]<s[e-1][F])return!1;return!0}f(Er,"isSorted");function Lr(s,e){return e||(s=s.slice()),s.sort(Or)}f(Lr,"sortSegments");function Or(s,e){return s[F]-e[F]}f(Or,"sortComparator");let ae=!1;function Rr(s,e,r,i){for(;r<=i;){const o=r+(i-r>>1),c=s[o][F]-e;if(c===0)return ae=!0,o;c<0?r=o+1:i=o-1}return ae=!1,r-1}f(Rr,"binarySearch");function Ar(s,e,r){for(let i=r-1;i>=0&&s[i][F]===e;r=i--);return r}f(Ar,"lowerBound");function Nr(){return{lastKey:-1,lastNeedle:-1,lastIndex:-1}}f(Nr,"memoizedState");function Ir(s,e,r,i){const{lastKey:o,lastNeedle:c,lastIndex:u}=r;let p=0,g=s.length-1;if(i===o){if(e===c)return ae=u!==-1&&s[u][F]===e,u;e>=c?p=u===-1?0:u:g=u}return r.lastKey=i,r.lastNeedle=e,r.lastIndex=Rr(s,e,p,g)}f(Ir,"memoizedBinarySearch");class tt{static{f(this,"TraceMap")}constructor(e,r){const i=typeof e=="string";if(!i&&e._decodedMemo)return e;const o=i?JSON.parse(e):e,{version:c,file:u,names:p,sourceRoot:g,sources:b,sourcesContent:d}=o;this.version=c,this.file=u,this.names=p||[],this.sourceRoot=g,this.sources=b,this.sourcesContent=d,this.ignoreList=o.ignoreList||o.x_google_ignoreList||void 0;const n=Ve(g||"",xr(r));this.resolvedSources=b.map(L=>Ve(L||"",n));const{mappings:R}=o;typeof R=="string"?(this._encoded=R,this._decoded=void 0):(this._encoded=void 0,this._decoded=_r(R,i)),this._decodedMemo=Nr(),this._bySources=void 0,this._bySourceMemos=void 0}}function gn(s){return s}f(gn,"cast$2");function rt(s){var e;return(e=s)._decoded||(e._decoded=Xt(s._encoded))}f(rt,"decodedMappings");function Mr(s,e,r){const i=rt(s);if(e>=i.length)return null;const o=i[e],c=Ur(o,s._decodedMemo,e,r);return c===-1?null:o[c]}f(Mr,"traceSegment");function Ur(s,e,r,i,o){let c=Ir(s,i,e,r);return ae&&(c=Ar(s,i,c)),c===-1||c===s.length?-1:c}f(Ur,"traceSegmentInternal");class _e{static{f(this,"SetArray")}constructor(){this._indexes={__proto__:null},this.array=[]}}function bn(s){return s}f(bn,"cast$1");function nt(s,e){return s._indexes[e]}f(nt,"get");function V(s,e){const r=nt(s,e);if(r!==void 0)return r;const{array:i,_indexes:o}=s,c=i.push(e);return o[e]=c-1}f(V,"put");function jr(s,e){const r=nt(s,e);if(r===void 0)return;const{array:i,_indexes:o}=s;for(let c=r+1;c<i.length;c++){const u=i[c];i[c-1]=u,o[u]--}o[e]=void 0,i.pop()}f(jr,"remove");const $r=0,Dr=1,Tr=2,Fr=3,Pr=4,it=-1;class Wr{static{f(this,"GenMapping")}constructor({file:e,sourceRoot:r}={}){this._names=new _e,this._sources=new _e,this._sourcesContent=[],this._mappings=[],this.file=e,this.sourceRoot=r,this._ignoreList=new _e}}function pn(s){return s}f(pn,"cast");const qr=f((s,e,r,i,o,c,u,p)=>zr(!0,s,e,r,i,o,c,u),"maybeAddSegment");function Br(s,e,r){const{_sources:i,_sourcesContent:o}=s,c=V(i,e);o[c]=r}f(Br,"setSourceContent");function Jr(s,e,r=!0){const{_sources:i,_sourcesContent:o,_ignoreList:c}=s,u=V(i,e);u===o.length&&(o[u]=null),r?V(c,u):jr(c,u)}f(Jr,"setIgnore");function st(s){const{_mappings:e,_sources:r,_sourcesContent:i,_names:o,_ignoreList:c}=s;return Kr(e),{version:3,file:s.file||void 0,names:o.array,sourceRoot:s.sourceRoot||void 0,sources:r.array,sourcesContent:i,mappings:e,ignoreList:c.array}}f(st,"toDecodedMap");function Gr(s){const e=st(s);return Object.assign(Object.assign({},e),{mappings:Te(e.mappings)})}f(Gr,"toEncodedMap");function zr(s,e,r,i,o,c,u,p,g){const{_mappings:b,_sources:d,_sourcesContent:n,_names:R}=e,L=Hr(b,r),E=Xr(L,i);if(!o)return Yr(L,E)?void 0:ot(L,E,[i]);const N=V(d,o),U=p?V(R,p):it;if(N===n.length&&(n[N]=null),!Qr(L,E,N,c,u,U))return ot(L,E,p?[i,N,c,u,U]:[i,N,c,u])}f(zr,"addSegmentInternal");function Hr(s,e){for(let r=s.length;r<=e;r++)s[r]=[];return s[e]}f(Hr,"getLine");function Xr(s,e){let r=s.length;for(let i=r-1;i>=0;r=i--){const o=s[i];if(e>=o[$r])break}return r}f(Xr,"getColumnIndex");function ot(s,e,r){for(let i=s.length;i>e;i--)s[i]=s[i-1];s[e]=r}f(ot,"insert");function Kr(s){const{length:e}=s;let r=e;for(let i=r-1;i>=0&&!(s[i].length>0);r=i,i--);r<e&&(s.length=r)}f(Kr,"removeEmptyFinalLines");function Yr(s,e){return e===0?!0:s[e-1].length===1}f(Yr,"skipSourceless");function Qr(s,e,r,i,o,c){if(e===0)return!1;const u=s[e-1];return u.length===1?!1:r===u[Dr]&&i===u[Tr]&&o===u[Fr]&&c===(u.length===5?u[Pr]:it)}f(Qr,"skipSource");const at=ct("",-1,-1,"",null,!1),Zr=[];function ct(s,e,r,i,o,c){return{source:s,line:e,column:r,name:i,content:o,ignore:c}}f(ct,"SegmentObject");function ut(s,e,r,i,o){return{map:s,sources:e,source:r,content:i,ignore:o}}f(ut,"Source");function lt(s,e){return ut(s,e,"",null,!1)}f(lt,"MapSource");function Vr(s,e,r){return ut(null,Zr,s,e,r)}f(Vr,"OriginalSource");function en(s){const e=new Wr({file:s.map.file}),{sources:r,map:i}=s,o=i.names,c=rt(i);for(let u=0;u<c.length;u++){const p=c[u];for(let g=0;g<p.length;g++){const b=p[g],d=b[0];let n=at;if(b.length!==1){const J=r[b[1]];if(n=ht(J,b[2],b[3],b.length===5?o[b[4]]:""),n==null)continue}const{column:R,line:L,name:E,content:N,source:U,ignore:P}=n;qr(e,u,d,U,L,R,E),U&&N!=null&&Br(e,U,N),P&&Jr(e,U,!0)}}return e}f(en,"traceMappings");function ht(s,e,r,i){if(!s.map)return ct(s.source,e,r,i,s.content,s.ignore);const o=Mr(s.map,e,r);return o==null?null:o.length===1?at:ht(s.sources[o[1]],o[2],o[3],o.length===5?s.map.names[o[4]]:i)}f(ht,"originalPositionFor");function tn(s){return Array.isArray(s)?s:[s]}f(tn,"asArray");function rn(s,e){const r=tn(s).map(c=>new tt(c,"")),i=r.pop();for(let c=0;c<r.length;c++)if(r[c].sources.length>1)throw new Error(`Transformation map ${c} must have exactly one source file.
Did you specify these with the most recent transformation maps first?`);let o=ft(i,e,"",0);for(let c=r.length-1;c>=0;c--)o=lt(r[c],[o]);return o}f(rn,"buildSourceMapTree");function ft(s,e,r,i){const{resolvedSources:o,sourcesContent:c,ignoreList:u}=s,p=i+1,g=o.map((b,d)=>{const n={importer:r,depth:p,source:b||"",content:void 0,ignore:void 0},R=e(n.source,n),{source:L,content:E,ignore:N}=n;if(R)return ft(new tt(R,L),e,L,p);const U=E!==void 0?E:c?c[d]:null,P=N!==void 0?N:u?u.includes(d):!1;return Vr(L,U,P)});return lt(s,g)}f(ft,"build");class nn{static{f(this,"SourceMap")}constructor(e,r){const i=r.decodedMappings?st(e):Gr(e);this.version=i.version,this.file=i.file,this.mappings=i.mappings,this.names=i.names,this.ignoreList=i.ignoreList,this.sourceRoot=i.sourceRoot,this.sources=i.sources,r.excludeContent||(this.sourcesContent=i.sourcesContent)}toString(){return JSON.stringify(this)}}function dt(s,e,r){const i={excludeContent:!!r,decodedMappings:!1},o=rn(s,e);return new nn(en(o),i)}f(dt,"remapping");const sn=f((s,e,r)=>{const i=[],o={code:e};for(const c of r){const u=c(s,o.code);u&&(Object.assign(o,u),i.unshift(u.map))}return{...o,map:dt(i,()=>null)}},"applyTransformersSync"),on=f(async(s,e,r)=>{const i=[],o={code:e};for(const c of r){const u=await c(s,o.code);u&&(Object.assign(o,u),i.unshift(u.map))}return{...o,map:dt(i,()=>null)}},"applyTransformers"),an=Object.freeze({target:`node${process.versions.node}`,loader:"default"}),cn=/^--inspect(?:-brk|-port|-publish-uid|-wait)?(?:=|$)/,un=process.execArgv.some(s=>cn.test(s)),gt={...an,sourcemap:!0,sourcesContent:!!process.env.NODE_V8_COVERAGE||un,minifyWhitespace:!0,keepNames:!0},bt=f(s=>{const e=s.sourcefile;if(e){const r=X.extname(e.split("?")[0]);r?r===".cts"||r===".mts"?s.sourcefile=`${e.slice(0,-3)}ts`:r===".mjs"&&(s.sourcefile=`${e.slice(0,-3)}js`):s.sourcefile+=".js"}return r=>(r.map&&(s.sourcefile!==e&&(r.map=r.map.replace(JSON.stringify(s.sourcefile),JSON.stringify(e))),r.map=JSON.parse(r.map)),r)},"patchOptions"),pt=f(s=>{throw s.name="TransformError",delete s.errors,delete s.warnings,s},"formatEsbuildError"),ln=f((s,e,r)=>{const i={};let o,c,u;if(e.startsWith("file://")){o=e;const d=new URL(e);c=Ne.fileURLToPath(d)}else[c,u]=e.split("?"),o=Ne.pathToFileURL(c)+(u?`?${u}`:"");c.endsWith(".cjs")||c.endsWith(".cts")||(i["import.meta.url"]=JSON.stringify(o));const p={...gt,format:"cjs",sourcefile:c,define:i,banner:`__filename=${JSON.stringify(c)};(()=>{`,footer:"})()",platform:"node",...r},g=Ie([s,JSON.stringify(p),ne.version,Je].join("-"));let b=se.get(g);return b||(b=sn(e,s,[(d,n)=>{const R=bt(p);let L;try{L=ne.transformSync(n,p)}catch(E){throw pt(E)}return R(L)},(d,n)=>xe(d,n,!0)]),se.set(g,b)),b},"transformSync"),hn=f(async(s,e,r)=>{const i={...gt,format:"esm",sourcefile:e,...r},o=Ie([s,JSON.stringify(i),ne.version,Je].join("-"));let c=se.get(o);return c||(c=await on(e,s,[async(u,p)=>{const g=bt(i);let b;try{b=await ne.transform(p,i)}catch(d){throw pt(d)}return g(b)},(u,p)=>xe(u,p,!0)]),se.set(o,c)),c},"transform");exports.isESM=lr,exports.readJsonFile=Ge,exports.transform=hn,exports.transformDynamicImport=xe,exports.transformSync=ln;
