import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { prisma } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get payment statistics
    const totalRevenue = await prisma.paymentRecord.aggregate({
      _sum: {
        amount: true,
      },
      where: {
        status: 'verified',
      },
    });

    const paymentCounts = await prisma.paymentRecord.groupBy({
      by: ['status'],
      _count: {
        status: true,
      },
    });

    const totalPayments = await prisma.paymentRecord.count();

    // Get user management statistics
    const totalUsers = await prisma.userManagement.count();
    const activeUsers = await prisma.userManagement.count({
      where: {
        isActive: true,
      },
    });

    // Get recent payment records
    const recentPayments = await prisma.paymentRecord.findMany({
      take: 5,
      orderBy: {
        createdAt: 'desc',
      },
      include: {
        recordedByUser: {
          select: {
            firstName: true,
            lastName: true,
          },
        },
      },
    });

    // Get financial reports count
    const reportsGenerated = await prisma.financialReport.count();

    // Calculate payment status breakdown
    const paymentStatusBreakdown = paymentCounts.reduce((acc, item) => {
      acc[item.status] = item._count.status;
      return acc;
    }, {} as Record<string, number>);

    const stats = {
      totalRevenue: totalRevenue._sum.amount || 0,
      totalUsers: activeUsers,
      totalPayments,
      reportsGenerated,
      paymentStatusBreakdown,
      recentPayments: recentPayments.map(payment => ({
        id: payment.id,
        studentId: payment.studentId,
        amount: payment.amount,
        paymentDate: payment.paymentDate,
        paymentMethod: payment.paymentMethod,
        status: payment.status,
        description: payment.description,
        recordedBy: `${payment.recordedByUser.firstName} ${payment.recordedByUser.lastName}`,
      })),
    };

    return NextResponse.json({
      success: true,
      data: stats,
    });
  } catch (error) {
    console.error('Dashboard stats error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch dashboard statistics' 
      },
      { status: 500 }
    );
  }
}
