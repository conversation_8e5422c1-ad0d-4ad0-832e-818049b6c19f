import { PrismaClient, AdminRole } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Seeding admin database...');

  // Create default admin user
  const hashedPassword = await bcrypt.hash('admin123', 12);
  
  const adminUser = await prisma.adminUser.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      passwordHash: hashedPassword,
      role: AdminRole.admin,
      firstName: 'System',
      lastName: 'Administrator',
      phone: '+1234567890',
      mfaEnabled: false, // Disabled for development
      isActive: true,
    },
  });

  console.log('✅ Created admin user:', adminUser.email);

  // Create default cashier user
  const cashierUser = await prisma.adminUser.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      passwordHash: hashedPassword,
      role: AdminRole.cashier,
      firstName: 'John',
      lastName: 'Cashier',
      phone: '+**********',
      mfaEnabled: false,
      isActive: true,
    },
  });

  console.log('✅ Created cashier user:', cashierUser.email);

  // Create default accounting user
  const accountingUser = await prisma.adminUser.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      passwordHash: hashedPassword,
      role: AdminRole.accounting,
      firstName: 'Jane',
      lastName: 'Accountant',
      phone: '+**********',
      mfaEnabled: false,
      isActive: true,
    },
  });

  console.log('✅ Created accounting user:', accountingUser.email);

  // Create some sample system configuration
  await prisma.systemConfig.upsert({
    where: { configKey: 'system.name' },
    update: {},
    create: {
      configKey: 'system.name',
      configValue: 'Innovative Centre CRM',
      description: 'System name displayed in the application',
      updatedBy: adminUser.id,
    },
  });

  await prisma.systemConfig.upsert({
    where: { configKey: 'security.session_timeout' },
    update: {},
    create: {
      configKey: 'security.session_timeout',
      configValue: 1800, // 30 minutes
      description: 'Session timeout in seconds',
      updatedBy: adminUser.id,
    },
  });

  await prisma.systemConfig.upsert({
    where: { configKey: 'security.mfa_required' },
    update: {},
    create: {
      configKey: 'security.mfa_required',
      configValue: false,
      description: 'Whether MFA is required for all users',
      updatedBy: adminUser.id,
    },
  });

  console.log('✅ Created system configuration');

  // Create sample fee structures for different courses
  const feeStructures = [
    {
      courseId: 'course-english-basic',
      feeType: 'tuition' as const,
      amount: 299.99,
      effectiveDate: new Date('2024-01-01'),
      createdBy: adminUser.id,
    },
    {
      courseId: 'course-english-advanced',
      feeType: 'tuition' as const,
      amount: 399.99,
      effectiveDate: new Date('2024-01-01'),
      createdBy: adminUser.id,
    },
    {
      courseId: 'course-math-basic',
      feeType: 'tuition' as const,
      amount: 249.99,
      effectiveDate: new Date('2024-01-01'),
      createdBy: adminUser.id,
    },
    {
      courseId: 'course-english-basic',
      feeType: 'registration' as const,
      amount: 50.00,
      effectiveDate: new Date('2024-01-01'),
      createdBy: adminUser.id,
    },
    {
      courseId: 'course-english-basic',
      feeType: 'material' as const,
      amount: 75.00,
      effectiveDate: new Date('2024-01-01'),
      createdBy: adminUser.id,
    },
  ];

  for (const fee of feeStructures) {
    await prisma.feeStructure.create({
      data: fee,
    });
  }

  console.log('✅ Created sample fee structures');

  // Create sample payment records
  const samplePayments = [
    {
      studentId: 'student-001',
      amount: 299.99,
      paymentDate: new Date('2024-01-15'),
      paymentMethod: 'card' as const,
      description: 'English Basic Course - Tuition Fee',
      status: 'verified' as const,
      notes: 'Payment processed successfully via credit card',
      recordedBy: cashierUser.id,
      verifiedBy: accountingUser.id,
      verificationDate: new Date('2024-01-16'),
    },
    {
      studentId: 'student-002',
      amount: 399.99,
      paymentDate: new Date('2024-01-20'),
      paymentMethod: 'bank_transfer' as const,
      description: 'English Advanced Course - Tuition Fee',
      status: 'recorded' as const,
      notes: 'Bank transfer pending verification',
      recordedBy: cashierUser.id,
    },
    {
      studentId: 'student-003',
      amount: 249.99,
      paymentDate: new Date('2024-01-25'),
      paymentMethod: 'cash' as const,
      description: 'Math Basic Course - Tuition Fee',
      status: 'verified' as const,
      notes: 'Cash payment received and verified',
      recordedBy: cashierUser.id,
      verifiedBy: accountingUser.id,
      verificationDate: new Date('2024-01-25'),
    },
    {
      studentId: 'student-001',
      amount: 50.00,
      paymentDate: new Date('2024-01-10'),
      paymentMethod: 'card' as const,
      description: 'Registration Fee',
      status: 'verified' as const,
      notes: 'Registration fee for English Basic Course',
      recordedBy: cashierUser.id,
      verifiedBy: accountingUser.id,
      verificationDate: new Date('2024-01-10'),
    },
  ];

  for (const payment of samplePayments) {
    const paymentRecord = await prisma.paymentRecord.create({
      data: payment,
    });

    // Create corresponding financial transaction
    await prisma.financialTransaction.create({
      data: {
        paymentRecordId: paymentRecord.id,
        transactionType: 'payment',
        amount: payment.amount,
        description: `Payment transaction for ${payment.description}`,
        performedBy: payment.recordedBy,
        ipAddress: '*************',
      },
    });
  }

  console.log('✅ Created sample payment records and transactions');

  // Create sample user management records for cross-service users
  const userManagementRecords = [
    {
      serviceName: 'crm-staff-service',
      serviceUserId: 'staff-001',
      email: '<EMAIL>',
      role: 'teacher',
      managedBy: adminUser.id,
    },
    {
      serviceName: 'crm-staff-service',
      serviceUserId: 'staff-002',
      email: '<EMAIL>',
      role: 'coordinator',
      managedBy: adminUser.id,
    },
    {
      serviceName: 'crm-student-service',
      serviceUserId: 'student-001',
      email: '<EMAIL>',
      role: 'student',
      managedBy: adminUser.id,
    },
    {
      serviceName: 'crm-student-service',
      serviceUserId: 'student-002',
      email: '<EMAIL>',
      role: 'student',
      managedBy: adminUser.id,
    },
    {
      serviceName: 'crm-student-service',
      serviceUserId: 'student-003',
      email: '<EMAIL>',
      role: 'student',
      managedBy: adminUser.id,
    },
  ];

  for (const userRecord of userManagementRecords) {
    await prisma.userManagement.create({
      data: userRecord,
    });
  }

  console.log('✅ Created cross-service user management records');

  console.log('🎉 Seeding completed successfully!');
  console.log('');
  console.log('Default users created:');
  console.log('- Admin: <EMAIL> / admin123');
  console.log('- Cashier: <EMAIL> / admin123');
  console.log('- Accounting: <EMAIL> / admin123');
  console.log('');
  console.log('Sample data created:');
  console.log('- 5 fee structures for different courses');
  console.log('- 4 payment records with transactions');
  console.log('- 5 cross-service user management records');
  console.log('- System configuration settings');
}

main()
  .catch((e) => {
    console.error('❌ Seeding failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
