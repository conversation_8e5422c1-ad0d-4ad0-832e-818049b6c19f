// Jest setup file for shared types tests

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Mock fetch globally
global.fetch = jest.fn();

// Mock crypto for Node.js environment
Object.defineProperty(global, 'crypto', {
  value: {
    createHmac: jest.fn().mockReturnValue({
      update: jest.fn().mockReturnThis(),
      digest: jest.fn().mockReturnValue('mocked-hash'),
    }),
  },
});

// Mock setTimeout and clearTimeout for testing
const mockSetTimeout = jest.fn().mockImplementation((callback, delay) => {
  // For testing, execute immediately
  if (delay < 100) {
    callback();
  }
  return 1;
});

// Add the __promisify__ property to satisfy TypeScript
Object.defineProperty(mockSetTimeout, '__promisify__', {
  value: jest.fn(),
  writable: true,
  enumerable: false,
  configurable: true,
});

global.setTimeout = mockSetTimeout as any;
global.clearTimeout = jest.fn();

// Reset all mocks before each test
beforeEach(() => {
  jest.clearAllMocks();
});
