<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/c8d16a4f793b0c36.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-ceb7eeaf6d559a68.js"/><script src="/_next/static/chunks/4bd1b696-67ee12fb04071d3b.js" async=""></script><script src="/_next/static/chunks/684-42609a042cd3abcf.js" async=""></script><script src="/_next/static/chunks/main-app-33a71795e2a62fa4.js" async=""></script><title>CRM Shared Types</title><meta name="description" content="Shared TypeScript types for Innovative Centre CRM"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body><div hidden=""><!--$--><!--/$--></div><div class="min-h-screen flex items-center justify-center"><div class="text-center"><h1 class="text-4xl font-bold mb-4">CRM Shared Types</h1><p class="text-lg text-gray-600">Shared TypeScript types and interfaces for Innovative Centre CRM</p><div class="mt-8 space-y-2"><p class="text-sm">This package provides:</p><ul class="text-sm text-gray-500 space-y-1"><li>• User types and roles</li><li>• Database entity interfaces</li><li>• API request/response types</li><li>• Shared enums and constants</li></ul></div></div></div><!--$--><!--/$--><script src="/_next/static/chunks/webpack-ceb7eeaf6d559a68.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[7555,[],\"\"]\n3:I[1295,[],\"\"]\n4:I[9665,[],\"OutletBoundary\"]\n7:I[4911,[],\"AsyncMetadataOutlet\"]\n9:I[9665,[],\"ViewportBoundary\"]\nb:I[9665,[],\"MetadataBoundary\"]\nd:I[6614,[],\"\"]\n:HL[\"/_next/static/css/c8d16a4f793b0c36.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"uZKp6e2ydP-menPEUGhu7\",\"p\":\"\",\"c\":[\"\",\"\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"__PAGE__\",{}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/c8d16a4f793b0c36.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"children\":[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"div\",null,{\"className\":\"min-h-screen flex items-center justify-center\",\"children\":[\"$\",\"div\",null,{\"className\":\"text-center\",\"children\":[[\"$\",\"h1\",null,{\"className\":\"text-4xl font-bold mb-4\",\"children\":\"CRM Shared Types\"}],[\"$\",\"p\",null,{\"className\":\"text-lg text-gray-600\",\"children\":\"Shared TypeScript types and interfaces for Innovative Centre CRM\"}],[\"$\",\"div\",null,{\"className\":\"mt-8 space-y-2\",\"children\":[[\"$\",\"p\",null,{\"className\":\"text-sm\",\"children\":\"This package provides:\"}],[\"$\",\"ul\",null,{\"className\":\"text-sm text-gray-500 space-y-1\",\"children\":[[\"$\",\"li\",null,{\"children\":\"• User types and roles\"}],[\"$\",\"li\",null,{\"children\":\"• Database entity interfaces\"}],[\"$\",\"li\",null,{\"children\":\"• API request/response types\"}],[\"$\",\"li\",null,{\"children\":\"• Shared enums and constants\"}]]}]]}]]}]}],null,[\"$\",\"$L4\",null,{\"children\":[\"$L5\",\"$L6\",[\"$\",\"$L7\",null,{\"promise\":\"$@8\"}]]}]]}],{},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"W_8846gxYkS_OIxUKFRXjv\",{\"children\":[[\"$\",\"$L9\",null,{\"children\":\"$La\"}],null]}],[\"$\",\"$Lb\",null,{\"children\":\"$Lc\"}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$d\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"e:\"$Sreact.suspense\"\nf:I[4911,[],\"AsyncMetadata\"]\nc:[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$e\",null,{\"fallback\":null,\"children\":[\"$\",\"$Lf\",null,{\"promise\":\"$@10\"}]}]}]\n6:null\n"])</script><script>self.__next_f.push([1,"a:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\n5:null\n"])</script><script>self.__next_f.push([1,"8:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"CRM Shared Types\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Shared TypeScript types for Innovative Centre CRM\"}]],\"error\":null,\"digest\":\"$undefined\"}\n10:{\"metadata\":\"$8:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>