(()=>{var e={};e.id=13,e.ids=[13],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1378:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>g,routeModule:()=>y,serverHooks:()=>v,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>f});var s={};t.r(s),t.d(s,{POST:()=>m});var a=t(6559),i=t(8088),n=t(7719),o=t(2190),d=t(2909),u=t(5069),p=t(6330),c=t(5697);let l=c.z.object({verified:c.z.boolean(),notes:c.z.string().optional()});async function m(e,{params:r}){try{let t=await (0,d.j2)();if(!t?.user)return o.NextResponse.json({error:"Unauthorized"},{status:401});if(!["admin","accounting"].includes(t.user.role))return o.NextResponse.json({error:"Forbidden"},{status:403});let{id:s}=await r,a=await e.json(),{verified:i,notes:n}=l.parse(a),c=await u.z.paymentRecord.findUnique({where:{id:s}});if(!c)return o.NextResponse.json({error:"Payment not found"},{status:404});let m=await u.z.paymentRecord.update({where:{id:s},data:{status:i?p.PaymentStatus.verified:p.PaymentStatus.disputed,verifiedBy:t.user.id,verificationDate:new Date,notes:n||c.notes},include:{recordedByUser:{select:{firstName:!0,lastName:!0,email:!0}},verifiedByUser:{select:{firstName:!0,lastName:!0,email:!0}}}});return await u.z.financialTransaction.create({data:{paymentRecordId:m.id,transactionType:p.TransactionType.adjustment,amount:c.amount,description:`Payment ${i?"verified":"disputed"}: ${n||"No notes"}`,performedBy:t.user.id,ipAddress:"127.0.0.1"}}),await u.z.auditLog.create({data:{userId:t.user.id,action:i?"VERIFY_PAYMENT":"DISPUTE_PAYMENT",resourceType:"PAYMENT_RECORD",resourceId:m.id,oldValues:{status:c.status},newValues:{status:i?p.PaymentStatus.verified:p.PaymentStatus.disputed,notes:n,verifiedBy:t.user.id,verificationDate:new Date},ipAddress:"127.0.0.1",userAgent:e.headers.get("user-agent")||"Unknown"}}),o.NextResponse.json({success:!0,data:m,message:`Payment ${i?"verified":"disputed"} successfully`})}catch(e){if(e instanceof c.z.ZodError)return o.NextResponse.json({error:"Validation error",details:e.errors},{status:400});return console.error("Error verifying payment:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}let y=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/payments/[id]/verify/route",pathname:"/api/payments/[id]/verify",filename:"route",bundlePath:"app/api/payments/[id]/verify/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-admin-service\\src\\app\\api\\payments\\[id]\\verify\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:w,workUnitAsyncStorage:f,serverHooks:v}=y;function g(){return(0,n.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:f})}},2909:(e,r,t)=>{"use strict";t.d(r,{Y9:()=>d,j2:()=>u});var s=t(8643),a=t(189),i=t(6467),n=t(5069),o=t(5663);let{handlers:d,auth:u,signIn:p,signOut:c}=(0,s.Ay)({adapter:(0,i.y)(n.z),session:{strategy:"jwt",maxAge:1800},pages:{signIn:"/auth/signin",error:"/auth/error"},providers:[(0,a.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"},mfaCode:{label:"MFA Code",type:"text"}},async authorize(e){if(!e?.email||!e?.password)return null;try{let r=await n.z.adminUser.findUnique({where:{email:e.email}});if(!r||!r.isActive||!await o.Ay.compare(e.password,r.passwordHash))return null;return r.mfaEnabled&&!e.mfaCode&&console.warn("MFA required but not implemented yet"),await n.z.adminUser.update({where:{id:r.id},data:{lastLogin:new Date}}),await n.z.auditLog.create({data:{userId:r.id,action:"LOGIN",resourceType:"AUTH",resourceId:r.id,newValues:{timestamp:new Date,success:!0},ipAddress:"127.0.0.1",userAgent:"Unknown"}}),{id:r.id,email:r.email,name:`${r.firstName} ${r.lastName}`,role:r.role}}catch(e){return console.error("Authentication error:",e),null}}})],callbacks:{jwt:async({token:e,user:r})=>(r&&(e.role=r.role,e.id=r.id),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.id,e.user.role=r.role),e),async authorized({auth:e,request:{nextUrl:r}}){let t=!!e?.user,s=r.pathname.startsWith("/dashboard"),a=r.pathname.startsWith("/auth");return s?!!t:!a||!t||Response.redirect(new URL("/dashboard",r))}},events:{async signOut(e){let r="token"in e&&e.token?.id?e.token.id:"session"in e&&e.session?.user?.id?e.session.user.id:null;r&&await n.z.auditLog.create({data:{userId:r,action:"LOGOUT",resourceType:"AUTH",resourceId:r,newValues:{timestamp:new Date},ipAddress:"127.0.0.1",userAgent:"Unknown"}})}}}),{GET:l,POST:m}=d},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5069:(e,r,t)=>{"use strict";t.d(r,{db:()=>i,z:()=>a});var s=t(6330);let a=globalThis.prisma??new s.PrismaClient({log:["query","error","warn"]}),i=a},5511:e=>{"use strict";e.exports=require("crypto")},6330:e=>{"use strict";e.exports=require("@prisma/client")},6487:()=>{},8335:()=>{},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,199,828,697],()=>t(1378));module.exports=s})();