import { PrismaClient, StaffR<PERSON>, TeacherStatus, CabinetStatus } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Seeding staff database...');

  // Create default staff users
  const hashedPassword = await bcrypt.hash('staff123', 12);
  
  // Create manager user
  const managerUser = await prisma.staffUser.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      passwordHash: hashedPassword,
      role: StaffRole.manager,
      firstName: '<PERSON>',
      lastName: 'Manager',
      phone: '+1234567890',
      employeeId: 'MGR001',
      department: 'Management',
      hireDate: new Date('2023-01-01'),
      isActive: true,
    },
  });

  console.log('✅ Created manager user:', managerUser.email);

  // Create reception user
  const receptionUser = await prisma.staffUser.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      passwordHash: hashedPassword,
      role: StaffRole.reception,
      firstName: 'Jane',
      lastName: 'Reception',
      phone: '+1234567891',
      employeeId: 'REC001',
      department: 'Front Office',
      hireDate: new Date('2023-02-01'),
      isActive: true,
    },
  });

  console.log('✅ Created reception user:', receptionUser.email);

  // Create teacher user
  const teacherUser = await prisma.staffUser.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      passwordHash: hashedPassword,
      role: StaffRole.teacher,
      firstName: 'Alice',
      lastName: 'Teacher',
      phone: '+1234567892',
      employeeId: 'TCH001',
      department: 'Education',
      hireDate: new Date('2023-03-01'),
      isActive: true,
    },
  });

  console.log('✅ Created teacher user:', teacherUser.email);

  // Create teacher profile for the teacher user
  const teacher = await prisma.teacher.upsert({
    where: { staffUserId: teacherUser.id },
    update: {},
    create: {
      staffUserId: teacherUser.id,
      specialization: 'English Language Teaching',
      qualifications: [
        {
          degree: 'Bachelor of Arts in English',
          institution: 'University of Education',
          year: 2020,
        },
        {
          degree: 'TESOL Certificate',
          institution: 'International TESOL Institute',
          year: 2021,
        },
      ],
      status: TeacherStatus.active,
    },
  });

  console.log('✅ Created teacher profile for:', teacherUser.email);

  // Create sample cabinets
  const cabinet1 = await prisma.cabinet.upsert({
    where: { cabinetNumber: 'A101' },
    update: {},
    create: {
      cabinetNumber: 'A101',
      cabinetName: 'Main Classroom A',
      capacity: 20,
      equipment: ['Projector', 'Whiteboard', 'Audio System', 'Air Conditioning'],
      location: 'Building A, First Floor',
      status: CabinetStatus.available,
    },
  });

  const cabinet2 = await prisma.cabinet.upsert({
    where: { cabinetNumber: 'A102' },
    update: {},
    create: {
      cabinetNumber: 'A102',
      cabinetName: 'Computer Lab',
      capacity: 15,
      equipment: ['Computers', 'Projector', 'Whiteboard', 'Internet Access'],
      location: 'Building A, First Floor',
      status: CabinetStatus.available,
    },
  });

  console.log('✅ Created sample cabinets');

  // Create sample course
  const course = await prisma.course.upsert({
    where: { courseCode: 'ENG101' },
    update: {},
    create: {
      courseCode: 'ENG101',
      title: 'English for Beginners',
      description: 'Basic English language course for beginners',
      level: 'Beginner',
      durationWeeks: 12,
      maxStudents: 15,
      price: 500.00,
      createdBy: managerUser.id,
    },
  });

  console.log('✅ Created sample course:', course.courseCode);

  // Create more courses
  const advancedCourse = await prisma.course.upsert({
    where: { courseCode: 'ENG201' },
    update: {},
    create: {
      courseCode: 'ENG201',
      title: 'English Advanced',
      description: 'Advanced English language course for intermediate students',
      level: 'Advanced',
      durationWeeks: 16,
      maxStudents: 12,
      price: 750.00,
      createdBy: managerUser.id,
    },
  });

  const mathCourse = await prisma.course.upsert({
    where: { courseCode: 'MATH101' },
    update: {},
    create: {
      courseCode: 'MATH101',
      title: 'Mathematics Basics',
      description: 'Basic mathematics course for beginners',
      level: 'Beginner',
      durationWeeks: 10,
      maxStudents: 20,
      price: 400.00,
      createdBy: managerUser.id,
    },
  });

  console.log('✅ Created additional courses');

  // Create sample groups
  const group1 = await prisma.group.upsert({
    where: { id: 'group-eng101-morning' },
    update: {},
    create: {
      id: 'group-eng101-morning',
      groupName: 'English Beginners - Morning',
      courseId: course.id,
      teacherId: teacher.id,
      cabinetId: cabinet1.id,
      maxStudents: 15,
      currentStudents: 3,
      startDate: new Date('2024-02-01'),
      endDate: new Date('2024-04-30'),
      schedule: {
        days: ['Monday', 'Wednesday', 'Friday'],
        time: '09:00-11:00',
        timezone: 'UTC'
      },
      createdBy: managerUser.id,
    },
  });

  const group2 = await prisma.group.upsert({
    where: { id: 'group-eng201-evening' },
    update: {},
    create: {
      id: 'group-eng201-evening',
      groupName: 'English Advanced - Evening',
      courseId: advancedCourse.id,
      teacherId: teacher.id,
      cabinetId: cabinet2.id,
      maxStudents: 12,
      currentStudents: 2,
      startDate: new Date('2024-02-15'),
      endDate: new Date('2024-06-15'),
      schedule: {
        days: ['Tuesday', 'Thursday'],
        time: '18:00-20:00',
        timezone: 'UTC'
      },
      createdBy: managerUser.id,
    },
  });

  console.log('✅ Created sample groups');

  // Create sample leads
  const leads = [
    {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '+1234567890',
      source: 'website' as const,
      status: 'new' as const,
      notes: 'Interested in English courses, prefers morning classes',
      assignedTo: receptionUser.id,
    },
    {
      firstName: 'Jane',
      lastName: 'Smith',
      email: '<EMAIL>',
      phone: '+1234567891',
      source: 'referral' as const,
      status: 'contacted' as const,
      notes: 'Referred by existing student, looking for advanced English',
      assignedTo: receptionUser.id,
    },
    {
      firstName: 'Bob',
      lastName: 'Wilson',
      email: '<EMAIL>',
      phone: '+1234567892',
      source: 'walk_in' as const,
      status: 'qualified' as const,
      notes: 'Completed assessment, ready for enrollment',
      assignedTo: receptionUser.id,
    },
    {
      firstName: 'Alice',
      lastName: 'Johnson',
      email: '<EMAIL>',
      phone: '+1234567893',
      source: 'social_media' as const,
      status: 'converted' as const,
      notes: 'Enrolled in English Beginners course',
      assignedTo: receptionUser.id,
      convertedToStudentId: 'student-001',
      conversionDate: new Date('2024-01-20'),
    },
  ];

  for (const leadData of leads) {
    await prisma.lead.create({
      data: leadData,
    });
  }

  console.log('✅ Created sample leads');

  // Create sample student enrollments
  const enrollments = [
    {
      studentId: 'student-001',
      courseId: course.id,
      groupId: group1.id,
      enrollmentDate: new Date('2024-01-20'),
    },
    {
      studentId: 'student-002',
      courseId: advancedCourse.id,
      groupId: group2.id,
      enrollmentDate: new Date('2024-01-25'),
    },
    {
      studentId: 'student-003',
      courseId: course.id,
      groupId: group1.id,
      enrollmentDate: new Date('2024-01-30'),
    },
  ];

  for (const enrollment of enrollments) {
    await prisma.studentEnrollment.create({
      data: enrollment,
    });
  }

  console.log('✅ Created sample student enrollments');

  // Create sample assignments
  const assignments = [
    {
      teacherId: teacher.id,
      studentId: 'student-001',
      groupId: group1.id,
      title: 'Introduction Essay',
      description: 'Write a 200-word essay about yourself',
      dueDate: new Date('2024-02-15'),
      createdBy: teacherUser.id,
    },
    {
      teacherId: teacher.id,
      studentId: 'student-002',
      groupId: group2.id,
      title: 'Advanced Grammar Exercise',
      description: 'Complete the advanced grammar workbook chapters 1-3',
      dueDate: new Date('2024-03-01'),
      createdBy: teacherUser.id,
    },
    {
      teacherId: teacher.id,
      studentId: 'student-003',
      groupId: group1.id,
      title: 'Vocabulary Quiz Preparation',
      description: 'Study vocabulary list for upcoming quiz',
      dueDate: new Date('2024-02-20'),
      createdBy: teacherUser.id,
    },
  ];

  for (const assignment of assignments) {
    await prisma.studentAssignment.create({
      data: assignment,
    });
  }

  console.log('✅ Created sample assignments');

  // Create sample resources
  const resources = [
    {
      title: 'English Grammar Basics',
      type: 'document' as const,
      fileUrl: '/resources/english-grammar-basics.pdf',
      courseId: course.id,
      groupId: group1.id,
      isPublic: true,
      createdBy: teacherUser.id,
    },
    {
      title: 'Pronunciation Guide',
      type: 'link' as const,
      content: 'https://pronunciation-guide.example.com',
      courseId: course.id,
      groupId: group1.id,
      isPublic: true,
      createdBy: teacherUser.id,
    },
    {
      title: 'Class Notes - Week 1',
      type: 'note' as const,
      content: 'Introduction to basic English grammar and vocabulary. Covered: present tense, basic vocabulary, pronunciation basics.',
      courseId: course.id,
      groupId: group1.id,
      isPublic: false,
      createdBy: teacherUser.id,
    },
  ];

  for (const resource of resources) {
    await prisma.studentResource.create({
      data: resource,
    });
  }

  console.log('✅ Created sample resources');

  console.log('🎉 Seeding completed successfully!');
  console.log('');
  console.log('Default users created:');
  console.log('- Manager: <EMAIL> / staff123');
  console.log('- Reception: <EMAIL> / staff123');
  console.log('- Teacher: <EMAIL> / staff123');
  console.log('');
  console.log('Sample data created:');
  console.log('- 3 courses (English Beginners, English Advanced, Math Basics)');
  console.log('- 2 classrooms/cabinets');
  console.log('- 2 groups with schedules');
  console.log('- 4 leads in different stages');
  console.log('- 3 student enrollments');
  console.log('- 3 assignments');
  console.log('- 3 learning resources');
}

main()
  .catch((e) => {
    console.error('❌ Seeding failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
